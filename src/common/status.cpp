// Copyright(C) 2023 InfiniFlow, Inc. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

module;

#include <cstring>

module status;

import stl;
import third_party;
import default_values;

namespace infinity {

Status::Status(ErrorCode code, const char *msg) : code_(code) { msg_ = MakeUnique<String>(msg); }

Status::Status(Status &s) { MoveStatus(s); }

Status::Status(Status &&s) noexcept { MoveStatus(s); }

Status &Status::operator=(Status &s) noexcept {
    MoveStatus(s);
    return *this;
}

Status &Status::operator=(Status &&s) noexcept {
    MoveStatus(s);
    return *this;
}

void Status::MoveStatus(Status &s) {
    code_ = s.code_;
    msg_ = std::move(s.msg_);
}

void Status::MoveStatus(Status &&s) {
    code_ = s.code_;
    msg_ = std::move(s.msg_);
}

void Status::Init(ErrorCode code, const char *msg) {
    if (msg_.get() != nullptr) {
        msg_.reset();
    }
    msg_ = MakeUnique<String>(msg);
    code_ = code;
}

void Status::AppendMessage(const String &msg) {
    if (msg_.get() != nullptr) {
        msg_->append(msg);
    } else {
        msg_ = MakeUnique<String>(msg);
    }
}

const char *Status::message() const {
    if (msg_.get() != nullptr) {
        return msg_->c_str();
    } else {
        return nullptr;
    }
}

// Error functions

Status Status::Ignore() { return Status(ErrorCode::kIgnore); }

// 1. Config error
Status Status::InvalidTimeInfo(const String &time_info) {
    return Status(ErrorCode::kInvalidTimeInfo, MakeUnique<String>(fmt::format("Invalid time info format: {}", time_info)));
}

Status Status::EmptyConfigParameter() { return Status(ErrorCode::kEmptyConfigParameter, MakeUnique<String>("Empty configure parameter.")); }

Status Status::MismatchVersion(const String &current_version, const String &expected_version) {
    return Status(ErrorCode::kMismatchVersion,
                  MakeUnique<String>(fmt::format("Current infinity version: {}, expected version: {}", current_version, expected_version)));
}

Status Status::InvalidTimezone(const String &timezone) {
    return Status(ErrorCode::kInvalidTimezone, MakeUnique<String>(fmt::format("Invalid time zone: {}.", timezone)));
}

Status Status::InvalidByteSize(const String &byte_size) {
    return Status(ErrorCode::kInvalidByteSize, MakeUnique<String>(fmt::format("Invalid byte size: {}.", byte_size)));
}

Status Status::InvalidIPAddr(const String &ip_addr) {
    return Status(ErrorCode::kInvalidIPAddr, MakeUnique<String>(fmt::format("Invalid ip address: {}.", ip_addr)));
}

Status Status::InvalidLogLevel(const String &log_level) {
    return Status(ErrorCode::kInvalidLogLevel, MakeUnique<String>(fmt::format("Invalid log level: {}.", log_level)));
}

Status Status::InvalidConfig(const String &detailed_info) { return Status(ErrorCode::kInvalidConfig, MakeUnique<String>(detailed_info)); }

// 2. Auth error
Status Status::WrongPasswd(const String &user_name) {
    return Status(ErrorCode::kWrongPasswd, MakeUnique<String>(fmt::format("Invalid password to login user: {}", user_name)));
}

Status Status::InsufficientPrivilege(const String &user_name, const String &detailed_error) {
    return Status(ErrorCode::kInsufficientPrivilege, MakeUnique<String>(fmt::format("{} do not have permission to {}", user_name, detailed_error)));
}

Status Status::UnsupportedVersionIndex(i64 given_index) {
    return Status(
        ErrorCode::kUnsupportedVersionIndex,
        MakeUnique<String>(fmt::format(
            "Index: {} isn't supported, you are using a deprecated version of Python SDK. Please install the corresponding version Python SDK.",
            given_index)));
}

Status Status::ClientVersionMismatch(const char *expected_version, const char *given_version) {
    return Status(ErrorCode::kClientVersionMismatch,
                  MakeUnique<String>(fmt::format("Expected version index: {}, connecting version: {}", expected_version, given_version)));
}

Status Status::AdminOnlySupportInMaintenanceMode() {
    return Status(ErrorCode::kAdminOnlySupportInMaintenanceMode, MakeUnique<String>("Only maintenance mode supports ADMIN command"));
}

Status Status::NotSupportInMaintenanceMode() {
    return Status(ErrorCode::kAdminOnlySupportInMaintenanceMode, MakeUnique<String>("Not support in maintenance mode"));
}

Status Status::InfinityIsStarting() { return Status(ErrorCode::kInfinityIsStarting, MakeUnique<String>("Infinity is starting")); }

Status Status::InfinityIsIniting() { return Status(ErrorCode::kInfinityIsIniting, MakeUnique<String>("Infinity is initing")); }

// 3. Syntax error or access rule violation
Status Status::InvalidUserName(const String &user_name) {
    return Status(ErrorCode::kInvalidUsername, MakeUnique<String>(fmt::format("{} is a invalid user name", user_name)));
}

Status Status::InvalidPasswd() { return Status(ErrorCode::kInvalidPasswd, MakeUnique<String>(fmt::format("Invalid password"))); }

Status Status::InvalidIdentifierName(const String &db_name) {
    return Status(ErrorCode::kInvalidIdentifierName, MakeUnique<String>(fmt::format("{} is a invalid identifier name", db_name)));
}

Status Status::InvalidTableName(const String &table_name) {
    return Status(ErrorCode::kInvalidTableName, MakeUnique<String>(fmt::format("{} is a invalid table name", table_name)));
}

Status Status::InvalidColumnName(const String &column_name) {
    return Status(ErrorCode::kInvalidColumnName, MakeUnique<String>(fmt::format("{} is a invalid column name", column_name)));
}

Status Status::InvalidIndexName(const String &index_name) {
    return Status(ErrorCode::kInvalidIndexName, MakeUnique<String>(fmt::format("{} is a invalid index name", index_name)));
}

Status Status::InvalidColumnDefinition(const String &detailed_info) {
    return Status(ErrorCode::kInvalidColumnDefinition, MakeUnique<String>(detailed_info));
}

Status Status::InvalidTableDefinition(const String &detailed_info) {
    return Status(ErrorCode::kInvalidTableDefinition, MakeUnique<String>(detailed_info));
}

Status Status::InvalidIndexDefinition(const String &detailed_info) {
    return Status(ErrorCode::kInvalidIndexDefinition, MakeUnique<String>(detailed_info));
}

Status Status::DataTypeMismatch(const String &type1, const String &type2) {
    return Status(ErrorCode::kDataTypeMismatch, MakeUnique<String>(fmt::format("Expected: {}, but {} is given.", type1, type2)));
}
Status Status::NameTooLong(const String &name, const String &object_type) {
    return Status(ErrorCode::kNameTooLong, MakeUnique<String>(fmt::format("{} is too long for a {} name", name, object_type)));
}

Status Status::ReservedName(const String &name) {
    return Status(ErrorCode::kReservedName, MakeUnique<String>(fmt::format("{} is a reserved name", name)));
}

Status Status::SyntaxError(const String &detailed_info) {
    return Status(ErrorCode::kSyntaxError, MakeUnique<String>(fmt::format("{}", detailed_info)));
}

Status Status::InvalidParameterValue(const String &parameter_name, const String &parameter_value, const String &recommend_value) {
    return Status(ErrorCode::kInvalidParameterValue,
                  MakeUnique<String>(fmt::format("{}: {} is invalid, Recommended value: {}", parameter_name, parameter_value, recommend_value)));
}

Status Status::DuplicateUserName(const String &user_name) {
    return Status(ErrorCode::kDuplicateUser, MakeUnique<String>(fmt::format("User: {} is already existed", user_name)));
}

Status Status::DuplicateDatabase(const String &db_name) {
    return Status(ErrorCode::kDuplicateDatabaseName, MakeUnique<String>(fmt::format("Database: {} is already existed", db_name)));
}

Status Status::DuplicateTable(const String &table_name) {
    return Status(ErrorCode::kDuplicateTableName, MakeUnique<String>(fmt::format("Table: {} is already existed", table_name)));
}

Status Status::DuplicateIndex(const String &index_name) {
    return Status(ErrorCode::kDuplicateIndexName, MakeUnique<String>(fmt::format("Index: {} is already existed", index_name)));
}

Status Status::DuplicateIndexOnColumn(const String &table_name, const String &column_name) {
    return Status(ErrorCode::kDuplicateIndexOnColumn,
                  MakeUnique<String>(fmt::format("Index: {} is created on column : {} with same parameters", table_name, column_name)));
}

Status Status::NoUser(const String &user_name) {
    return Status(ErrorCode::kNoSuchUser, MakeUnique<String>(fmt::format("No such user: {}", user_name)));
}

Status Status::DBNotExist(const String &db_name) {
    return Status(ErrorCode::kDBNotExist, MakeUnique<String>(fmt::format("Database: {} doesn't exist.", db_name)));
}

Status Status::TableNotExist(const String &table_name) {
    return Status(ErrorCode::kTableNotExist, MakeUnique<String>(fmt::format("Table: {} doesn't exist.", table_name)));
}

Status Status::IndexNotExist(const String &index_name) {
    return Status(ErrorCode::kIndexNotExist, MakeUnique<String>(fmt::format("Index: {} doesn't exist.", index_name)));
}

Status Status::ColumnNotExist(const String &column_name) {
    return Status(ErrorCode::kColumnNotExist, MakeUnique<String>(fmt::format("Column: {} doesn't exist", column_name)));
}

Status Status::ColumnNotExist(const ColumnID &column_idx) {
    return Status(ErrorCode::kColumnNotExist, MakeUnique<String>(fmt::format("Column: {} column doesn't exist", column_idx)));
}

Status Status::AggNotAllowInWhere(const String &func_name) {
    return Status(ErrorCode::kAggNotAllowInWhereClause,
                  MakeUnique<String>(fmt::format("Aggregate function: {} isn't allowed in where clause", func_name)));
}

Status Status::ColumnInSelectNotInGroupBy(const String &column_name) {
    return Status(ErrorCode::kColumnInSelectNotInGroupBy,
                  MakeUnique<String>(fmt::format("Column: {} must appear in the GROUP BY clause or be used in an aggregated function", column_name)));
}

Status Status::NoSysVar(const String &variable_name) {
    return Status(ErrorCode::kNoSuchSystemVar, MakeUnique<String>(fmt::format("No such system variable: {}", variable_name)));
}

Status Status::SetInvalidVarValue(const String &variable_name, const String &valid_value_range) {
    return Status(ErrorCode::kInvalidSystemVarValue, MakeUnique<String>(fmt::format("{} value range is {}", variable_name, valid_value_range)));
}

Status Status::ReadOnlySysVar(const String &sys_var) {
    return Status(ErrorCode::kSystemVarReadOnly, MakeUnique<String>(fmt::format("{} is read-only", sys_var)));
}

Status Status::FunctionNotFound(const String &function_name) {
    return Status(ErrorCode::kFunctionNotFound, MakeUnique<String>(fmt::format("{} not found", function_name)));
}

Status Status::SpecialFunctionNotFound() { return Status(ErrorCode::kSpecialFunctionNotFound); }

Status Status::NotSupport(const String &detailed_info) { return Status(ErrorCode::kNotSupported, MakeUnique<String>(detailed_info)); }

Status Status::DroppingUsingDb(const String &db_name) {
    return Status(ErrorCode::kDroppingUsingDb, MakeUnique<String>(fmt::format("Can't drop using database: {}", db_name)));
}

Status Status::SessionNotFound(i64 session_id) {
    return Status(ErrorCode::kSessionNotFound, MakeUnique<String>(fmt::format("Session id: {} isn't found", session_id)));
}

Status Status::RecursiveAggregate(const String &expr_name) {
    return Status(ErrorCode::kRecursiveAgg, MakeUnique<String>(fmt::format("{} is in another aggregate expression", expr_name)));
}

Status Status::FunctionArgsError(const String &func_name) {
    return Status(ErrorCode::kFunctionArgsError, MakeUnique<String>(fmt::format("{} arguments have errors", func_name)));
}

Status Status::ImportFileFormatError(const infinity::String &detailed_info) {
    return Status(ErrorCode::kImportFileFormatError, MakeUnique<String>(fmt::format("Import file format error: {}", detailed_info)));
}

Status Status::DataNotExist(const infinity::String &detailed_info) {
    return Status(ErrorCode::kImportFileFormatError, MakeUnique<String>(fmt::format("Data not exist: {}", detailed_info)));
}

Status Status::EmptyDBName() { return Status(ErrorCode::kEmptyDbName, MakeUnique<String>("Empty database name.")); }

Status Status::EmptyTableName() { return Status(ErrorCode::kEmptyTableName, MakeUnique<String>("Empty table name.")); }

Status Status::EmptyColumnName() { return Status(ErrorCode::kEmptyColumnName, MakeUnique<String>("Empty column name.")); }

Status Status::EmptyIndexName() { return Status(ErrorCode::kEmptyIndexName, MakeUnique<String>("Empty index name.")); }

Status Status::ExceedDBNameLength(u64 db_name_length) {
    return Status(ErrorCode::kExceedDBNameLength,
                  MakeUnique<String>(fmt::format("Given database name length exceeds {}", MAX_IDENTIFIER_NAME_LENGTH)));
}

Status Status::ExceedTableNameLength(u64 table_name_length) {
    return Status(ErrorCode::kExceedTableNameLength,
                  MakeUnique<String>(fmt::format("Given table name length exceeds {}", MAX_IDENTIFIER_NAME_LENGTH)));
}

Status Status::ExceedColumnNameLength(u64 column_name_length) {
    return Status(ErrorCode::kExceedColumnNameLength,
                  MakeUnique<String>(fmt::format("Given column name length exceeds {}", MAX_IDENTIFIER_NAME_LENGTH)));
}

Status Status::ExceedIndexNameLength(u64 index_name_length) {
    return Status(ErrorCode::kExceedIndexNameLength,
                  MakeUnique<String>(fmt::format("Given index name length exceeds {}", MAX_IDENTIFIER_NAME_LENGTH)));
}

Status Status::NoColumnDefined(const infinity::String &table_name) {
    return Status(ErrorCode::kNoColumnDefined, MakeUnique<String>(fmt::format("Try to define Table {} without any column.", table_name)));
}

Status Status::NotSupportedTypeConversion(const String &from_type, const String &to_type) {
    return Status(ErrorCode::kNotSupportedTypeConversion, MakeUnique<String>(fmt::format("Not support to convert {} to {}", from_type, to_type)));
}

Status Status::EmptySelectFields() { return Status(ErrorCode::kEmptySelectFields, MakeUnique<String>("Select fields are empty")); }

Status Status::InvalidDataType() { return Status(ErrorCode::kInvalidDataType, MakeUnique<String>("Invalid data type")); }

Status Status::ParseMatchExprFailed(const String &fields, const String &matching_text) {
    return Status(ErrorCode::kParseMatchExprFailed,
                  MakeUnique<String>(fmt::format("Trying to match: {} on fields: {} failed.", matching_text, fields)));
}

Status Status::FTSIndexNotExist(const String &table_name) {
    return Status(ErrorCode::kFTSIndexNotExist, MakeUnique<String>(fmt::format("Full text index of table: {} not exists.", table_name)));
}

Status Status::UnknownFTSFault() { return Status(ErrorCode::kUnknownFTSFault, MakeUnique<String>(fmt::format("Unknown full text index fault."))); }

Status Status::InvalidConstraintType() { return Status(ErrorCode::kInvalidConstraintType, MakeUnique<String>("Invalid constraint type.")); }

Status Status::InvalidKnnDistanceType() { return Status(ErrorCode::kInvalidKnnDistanceType, MakeUnique<String>("Invalid knn distance type.")); }

Status Status::InvalidEmbeddingDataType(const String &type_str) {
    return Status(ErrorCode::kInvalidEmbeddingDataType, MakeUnique<String>(fmt::format("Invalid embedding data type: {}.", type_str)));
}

Status Status::InvalidConstantType() { return Status(ErrorCode::kInvalidConstantType, MakeUnique<String>("Invalid constant type.")); }

Status Status::InvalidParsedExprType() { return Status(ErrorCode::kInvalidParsedExprType, MakeUnique<String>("Invalid parsed expression type.")); }

Status Status::InvalidIndexType(const String &message) {
    if (message.empty()) {
        return Status(ErrorCode::kInvalidIndexType, MakeUnique<String>("No index type is given"));
    } else {
        return Status(ErrorCode::kInvalidIndexType, MakeUnique<String>(fmt::format("Invalid index type: ", message)));
    }
}

Status Status::InvalidIndexParam(const String &param_name) {
    return Status(ErrorCode::kInvalidIndexParam, MakeUnique<String>(fmt::format("Invalid index parameter type: {}", param_name)));
}

Status Status::LackIndexParam() { return Status(ErrorCode::kLackIndexParam, MakeUnique<String>("Lack index parameter")); }

Status Status::InvalidFilterExpression(const String &expr_str) {
    return Status(ErrorCode::kInvalidFilterExpression,
                  MakeUnique<String>(fmt::format("Invalid expression in where clause: {} expression", expr_str)));
}

Status Status::MultipleFunctionMatched(const String &function, const String &functions) {
    return Status(ErrorCode::kMultipleFunctionMatched, MakeUnique<String>(fmt::format("{}: matched multiple functions: {}", function, functions)));
}

Status Status::InsertWithoutValues() { return Status(ErrorCode::kInsertWithoutValues, MakeUnique<String>("Insert into table without any values")); }

Status Status::InvalidConflictType() { return Status(ErrorCode::kInvalidConflictType, MakeUnique<String>("invalid conflict type")); }

Status Status::InvalidJsonFormat(const String &error_message) {
    return Status(ErrorCode::kInvalidJsonFormat, MakeUnique<String>(fmt::format("Invalid format json: {}", error_message)));
}

Status Status::DuplicateColumnName(const String &column_name) {
    return Status(ErrorCode::kDuplicateColumnName, MakeUnique<String>(fmt::format("Duplicated column name: {}", column_name)));
}

Status Status::InvalidExpression(const String &expr_str) { return Status(ErrorCode::kInvalidExpression, MakeUnique<String>(expr_str)); }

Status Status::SegmentNotExist(SegmentID segment_id) {
    return Status(ErrorCode::kSegmentNotExist, MakeUnique<String>(fmt::format("Segment: {} doesn't exist", segment_id)));
}

Status Status::BlockNotExist(BlockID block_id) {
    return Status(ErrorCode::kBlockNotExist, MakeUnique<String>(fmt::format("Block: {} doesn't exist", block_id)));
}

Status Status::AggregateFunctionWithEmptyArgs() {
    return Status(ErrorCode::kAggregateFunctionWithEmptyArgs, MakeUnique<String>("Aggregate function with empty arguments"));
}

Status Status::InvalidCommand(const String &detailed_error) {
    return Status(ErrorCode::kInvalidCommand, MakeUnique<String>(fmt::format("Invalid command: {}", detailed_error)));
}

Status Status::AnalyzerNotFound(const String &name) {
    return Status(ErrorCode::kAnalyzerNotFound, MakeUnique<String>(fmt::format("Analyzer {} isn't found", name)));
}

Status Status::NotSupportedAnalyzer(const String &name) {
    return Status(ErrorCode::kNotSupportedAnalyzer, MakeUnique<String>(fmt::format("Analyzer {} isn't supported", name)));
}

Status Status::InvalidAnalyzerName(const String &name) { return Status(ErrorCode::kInvalidAnalyzerName, MakeUnique<String>(name)); }

Status Status::InvalidAnalyzerFile(const String &detailed_info) {
    return Status(ErrorCode::kInvalidAnalyzerName, MakeUnique<String>(fmt::format("Invalid analyzer file path: {}", detailed_info)));
}

Status Status::ChunkNotExist(ChunkID chunk_id) {
    return Status(ErrorCode::kChunkNotExist, MakeUnique<String>(fmt::format("Index chunk: {} doesn't exist", chunk_id)));
}

Status Status::NameMismatched(const String &name_left, const String &name_right) {
    return Status(ErrorCode::kNameMismatched, MakeUnique<String>(fmt::format("It is {}, expects {}", name_left, name_right)));
}

Status Status::TransactionNotFound(TransactionID txn_id) {
    return Status(ErrorCode::kTransactionNotFound, MakeUnique<String>(fmt::format("Transaction {} isn't found", txn_id)));
}

Status Status::InvalidDatabaseIndex(u64 database_index, u64 capacity) {
    return Status(ErrorCode::kInvalidDatabaseIndex,
                  MakeUnique<String>(fmt::format("Invalid database index: {} (0-{})", database_index, capacity - 1)));
}

Status Status::InvalidTableIndex(u64 table_index, u64 capacity) {
    return Status(ErrorCode::kInvalidTableIndex, MakeUnique<String>(fmt::format("Invalid table index: {} (0-{})", table_index, capacity - 1)));
}

Status Status::FunctionIsDisable(const String &function_name) {
    return Status(ErrorCode::kFunctionIsDisable, MakeUnique<String>(fmt::format("Function: {} is disable", function_name)));
}

Status Status::NotFound(const String &detailed_info) { return Status(ErrorCode::kNotFound, MakeUnique<String>(detailed_info)); }

Status Status::ErrorInit(const String &detailed_info) { return Status(ErrorCode::kErrorInit, MakeUnique<String>(detailed_info)); }

Status Status::FileIsOpen(const String &filename) { return Status(ErrorCode::kFileIsOpen, MakeUnique<String>(filename)); }

Status Status::Unknown(const String &name) { return Status(ErrorCode::kUnknown, MakeUnique<String>(fmt::format("Unknown {}", name))); }

Status Status::InvalidQueryOption(const String &detail) {
    return Status(ErrorCode::kUnknown, MakeUnique<String>(fmt::format("Invalid query option: {}", detail)));
}

Status Status::FailToStartTxn(const String &detail) { return Status(ErrorCode::kFailToStartTxn, MakeUnique<String>(detail)); }

Status Status::AlreadyLocked(const String &detail) { return Status(ErrorCode::kAlreadyLocked, MakeUnique<String>(detail)); }

Status Status::NotLocked(const String &detail) { return Status(ErrorCode::kNotLocked, MakeUnique<String>(detail)); }

Status Status::TableIsUsing(const String &detail) { return Status(ErrorCode::kTableIsUsing, MakeUnique<String>(detail)); }

Status Status::DuplicateColumnIndex(const String &detail) { return Status(ErrorCode::kDuplicateColumnIndex, MakeUnique<String>(detail)); }

Status Status::InvalidParameter(const String &detail) { return Status(ErrorCode::kInvalidParameter, MakeUnique<String>(detail)); }

Status Status::IndexOnColumn(const String &column_name) {
    return Status(ErrorCode::kIndexOnColumn, MakeUnique<String>(fmt::format("Index is created on column: {}", column_name)));
}

// 4. TXN fail
Status Status::TxnRollback(u64 txn_id, const String &rollback_reason) {
    return Status(ErrorCode::kTxnRollback, MakeUnique<String>(fmt::format("Transaction: {} is rollback. {}", txn_id, rollback_reason)));
}

Status Status::TxnConflict(u64 txn_id, const String &conflict_reason) {
    return Status(ErrorCode::kTxnConflict,
                  MakeUnique<String>(fmt::format("Transaction: {} is conflicted, detailed info: {}", txn_id, conflict_reason)));
}

Status Status::TxnConflictNoRetry(u64 txn_id, const String &conflict_reason) {
    return Status(ErrorCode::kTxnConflictNoRetry,
                  MakeUnique<String>(fmt::format("Transaction: {} is conflicted, detailed info: {}", txn_id, conflict_reason)));
}

Status Status::TxnWWConflict(const String &detailed_message) { return Status(ErrorCode::kTxnWWConflict, MakeUnique<String>(detailed_message)); }

Status Status::TxnRWConflict(const String &detailed_message) { return Status(ErrorCode::kTxnRWConflict, MakeUnique<String>(detailed_message)); }

// 5. Insufficient resource or exceed limits
Status Status::DiskFull(const String &detailed_info) {
    return Status(ErrorCode::kDiskFull, MakeUnique<String>(fmt::format("Disk full: {}", detailed_info)));
}

Status Status::OutOfMemory(const String &detailed_info) {
    return Status(ErrorCode::kOutOfMemory, MakeUnique<String>(fmt::format("Out of memory: {}", detailed_info)));
}

Status Status::TooManyConnections(const String &detailed_info) {
    return Status(ErrorCode::kTooManyConnections, MakeUnique<String>(fmt::format("Too many connections, {}", detailed_info)));
}

Status Status::ConfigurationLimitExceed(const String &config_name, const String &config_value, const String &valid_value_range) {
    return Status(ErrorCode::kConfigurationLimitExceed,
                  MakeUnique<String>(
                      fmt::format("Set {} value: {} failed, {} value valid range : {}", config_name, config_value, config_name, valid_value_range)));
}

Status Status::QueryTooBig(const String &query_text, u64 ast_node) {
    return Status(ErrorCode::kQueryIsTooComplex, MakeUnique<String>(fmt::format("Query: {} is too complex with {} AST nodes", query_text, ast_node)));
}

Status Status::FailToGetSysInfo(const String &detailed_info) { return Status(ErrorCode::kFailToGetSysInfo, MakeUnique<String>(detailed_info)); }

// 6. Operation intervention
Status Status::QueryCancelled(const String &query_text) {
    return Status(ErrorCode::kQueryCancelled, MakeUnique<String>(fmt::format("Query: {} is cancelled", query_text)));
}

Status Status::QueryNotSupported(const String &query_text, const String &detailed_reason) {
    return Status(ErrorCode::kQueryNotSupported, MakeUnique<String>(fmt::format("Query: {} isn't supported, {}", query_text, detailed_reason)));
}

Status Status::ClientClose() { return Status(ErrorCode::kClientClose); }

// 7. System error
Status Status::IOError(const String &detailed_info) {
    return Status(ErrorCode::kIOError, MakeUnique<String>(fmt::format("IO error: {}", detailed_info)));
}

Status Status::DuplicatedFile(const String &filename) {
    return Status(ErrorCode::kDuplicatedFile, MakeUnique<String>(fmt::format("File already existed: {}", filename)));
}

Status Status::ConfigFileError(const String &path, const String &detailed_info) {
    return Status(ErrorCode::kConfigFileError, MakeUnique<String>(fmt::format("Config file: {}, {}", path, detailed_info)));
}

Status Status::LockFileError(const String &path, const String &error_msg) {
    return Status(ErrorCode::kLockFileError, MakeUnique<String>(fmt::format("Lock file error: {}, {}", path, error_msg)));
}

Status Status::FileCorrupted(const String &path) {
    return Status(ErrorCode::kFileCorrupted, MakeUnique<String>(fmt::format("Catalog: {} is corrupted", path)));
}

Status Status::DataCorrupted(const String &path) {
    return Status(ErrorCode::kDataCorrupted, MakeUnique<String>(fmt::format("Data: {} is corrupted", path)));
}

Status Status::IndexCorrupted(const String &path) {
    return Status(ErrorCode::kIndexCorrupted, MakeUnique<String>(fmt::format("Index: {} is corrupted", path)));
}

Status Status::FileNotFound(const String &path) {
    return Status(ErrorCode::kFileNotFound, MakeUnique<String>(fmt::format("File: {} isn't found", path)));
}

Status Status::DirNotFound(const String &path) {
    return Status(ErrorCode::kDirNotFound, MakeUnique<String>(fmt::format("Directory: {} isn't found", path)));
}

Status Status::DataIOError(const String &detailed_info) {
    return Status(ErrorCode::kDataIOError, MakeUnique<String>(fmt::format("Data read error: {}", detailed_info)));
}

Status Status::UnexpectedError(const String &detailed_info) {
    return Status(ErrorCode::kUnexpectedError, MakeUnique<String>(fmt::format("Unexpected error: {}", detailed_info)));
}

Status Status::ParserError(const String &detailed_info) {
    return Status(ErrorCode::kParserError, MakeUnique<String>(fmt::format("Parser error: {}", detailed_info)));
}

Status Status::MmapFileError(const String &detailed_info) {
    return Status(ErrorCode::kMmapFileError, MakeUnique<String>(fmt::format("mmap error: {}", detailed_info)));
}

Status Status::MunmapFileError(const String &detailed_info) {
    return Status(ErrorCode::kMunmapFileError, MakeUnique<String>(fmt::format("munmap error: {}", detailed_info)));
}

Status Status::InvalidFileFlag(u8 flag) {
    return Status(ErrorCode::kInvalidFileFlag, MakeUnique<String>(fmt::format("Invalid open file flag: {}", flag)));
}

Status Status::FailToRunPython(const String &reason) { return Status(ErrorCode::kFailToRunPython, MakeUnique<String>(reason)); }

Status Status::InvalidServerAddress(const String &error_address) {
    return Status(ErrorCode::kInvalidServerAddress, MakeUnique<String>(fmt::format("Invalid server address: {}", error_address)));
}

Status Status::ColumnCountMismatch(const String &detailed_info) {
    return Status(ErrorCode::kColumnCountMismatch, MakeUnique<String>(fmt::format("Column count mismatch: {}", detailed_info)));
}

Status Status::CantConnectServer(const String &ip, i64 port, const String &reason) {
    return Status(ErrorCode::kCantConnectServer, MakeUnique<String>(fmt::format("Can't connect server: {}:{}, {}", ip, port, reason)));
}

Status Status::NotExistNode(const String &node_info) {
    return Status(ErrorCode::kNotExistNode, MakeUnique<String>(fmt::format("Node doesn't exist: {}", node_info)));
}
Status Status::DuplicateNode(const String &node_info) {
    return Status(ErrorCode::kDuplicateNode, MakeUnique<String>(fmt::format("Duplicate node: {}", node_info)));
}

Status Status::CantConnectLeader(const String &leader_info) {
    return Status(ErrorCode::kCantConnectLeader, MakeUnique<String>(fmt::format("Can't connect leader: {}", leader_info)));
}

Status Status::MinioInvalidAccessKey(const String &detailed) {
    return Status(ErrorCode::kMinioInvalidAccessKey, MakeUnique<String>(fmt::format("Minio error: {}", detailed)));
}

Status Status::MinioBucketNotExists(const String &bucket_name) {
    return Status(ErrorCode::kMinioBucketNotExists, MakeUnique<String>(fmt::format("Bucket {} not found", bucket_name)));
}

Status Status::InvalidStorageType(const String &expected, const String &actual) {
    return Status(ErrorCode::kInvalidStorageType,
                  MakeUnique<String>(fmt::format("Expect storage type: {}, actual storage type: {}", expected, actual)));
}

Status Status::NotRegistered(const String &node_info) {
    return Status(ErrorCode::kNotRegistered, MakeUnique<String>(fmt::format("Not registered node: {}", node_info)));
}

Status Status::CantSwitchRole(const String &detailed_info) { return Status(ErrorCode::kCantSwitchRole, MakeUnique<String>(detailed_info)); }

Status Status::TooManyFollower(infinity::u8 follower_limit) {
    return Status(ErrorCode::kTooManyFollower, MakeUnique<String>(fmt::format("Too many followers, limit: {}", follower_limit)));
}

Status Status::TooManyLearner() { return Status(ErrorCode::kTooManyLearner, MakeUnique<String>("Too many learner, limit: 255")); }

Status Status::Checkpointing() { return Status(ErrorCode::kCheckpointing, MakeUnique<String>("Checkpointing")); }

// meta
Status Status::InvalidEntry() { return Status(ErrorCode::kInvalidEntry, MakeUnique<String>("Invalid entry")); }

Status Status::NotFoundEntry() { return Status(ErrorCode::kNotFoundEntry, MakeUnique<String>("Not found entry")); }

Status Status::DuplicateEntry(const String &detail_info) { return Status(ErrorCode::kDuplicateEntry, MakeUnique<String>(detail_info)); }

Status Status::EmptyEntryList() { return Status(ErrorCode::kEmptyEntryList, MakeUnique<String>("Empty entry list")); }

Status Status::NoWALEntryFound(const String &file_name, i64 index) {
    return Status(ErrorCode::kEmptyEntryList, MakeUnique<String>(fmt::format("No WAL entry: {} found in WAL file: {}", index, file_name)));
}

Status Status::WrongCheckpointType(const String &expect_type, const String &actual_type) {
    return Status(ErrorCode::kWrongCheckpointType,
                  MakeUnique<String>(fmt::format("Expect checkpoint type: {}, actual checkpoint type: {}", expect_type, actual_type)));
}

Status Status::InvalidNodeRole(const String &message) { return Status(ErrorCode::kInvalidNodeRole, MakeUnique<String>(message)); }

Status Status::InvalidNodeStatus(const String &message) { return Status(ErrorCode::kInvalidNodeStatus, MakeUnique<String>(message)); }

Status Status::NodeInfoUpdated(const String &message) { return Status(ErrorCode::kNodeInfoUpdated, MakeUnique<String>(message)); }

Status Status::NodeNameMismatch(const String &actual_node_name, const String &expected_node_name) {
    return Status(ErrorCode::kNodeNameMismatch,
                  MakeUnique<String>(fmt::format("Expect node name: {}, actual node name: {}", expected_node_name, actual_node_name)));
}

Status Status::CatalogError(const String &detailed_info) {
    return Status(ErrorCode::kCatalogError, MakeUnique<String>(fmt::format("Catalog error: {}", detailed_info)));
}

Status Status::BufferManagerError(const String &detailed_info) {
    return Status(ErrorCode::kBufferManagerError, MakeUnique<String>(fmt::format("Buffer manager error: {}", detailed_info)));
}

Status Status::RocksDBError(rocksdb::Status rocksdb_s, const String &msg) {
    return Status(ErrorCode::kRocksDBError, MakeUnique<String>(fmt::format("{}, {}", msg, rocksdb_s.ToString())));
}

Status Status::RocksDBError(rocksdb::IOStatus rocksdb_s, const String &msg) {
    return Status(ErrorCode::kRocksDBError, MakeUnique<String>(fmt::format("{}, {}", msg, rocksdb_s.ToString())));
}

} // namespace infinity
