// Copyright(C) 2023 InfiniFlow, Inc. All rights reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

module;

import stl;

export module builtin_functions;

namespace infinity {

class NewCatalog;

export class BuiltinFunctions : public EnableSharedFromThis<BuiltinFunctions> {
public:
    explicit BuiltinFunctions(NewCatalog *catalog_ptr);

    void Init();

private:
    NewCatalog *catalog_ptr_{};

private:
    void RegisterAggregateFunction();

    void RegisterScalarFunction();

    void RegisterTableFunction();

    void RegisterSpecialFunction();
};

} // namespace infinity
