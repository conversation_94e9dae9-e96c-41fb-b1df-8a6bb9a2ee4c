//  Copyright(C) 2023 InfiniFlow, Inc. All rights reserved.
//
//  Licensed under the Apache License, Version 2.0 (the "License");
//  you may not use this file except in compliance with the License.
//  You may obtain a copy of the License at
//
//      https://www.apache.org/licenses/LICENSE-2.0
//
//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
//  See the License for the specific language governing permissions and
//  limitations under the License.

module;

export module update_segment_bloom_filter_task;

import stl;
import bg_task;
import bg_task_type;

namespace infinity {

export class UpdateSegmentBloomFilterTask final : public BGTask {
public:
    static void CreateAndSubmitTask();

    explicit UpdateSegmentBloomFilterTask() : BGTask(BGTaskType::kUpdateSegmentBloomFilterData, false) {}

    String ToString() const override { return "UpdateSegmentBloomFilterTask"; }

    void Execute();

    static void ExecuteInner();

private:
};

} // namespace infinity
