#line 2 "lexer.cpp"

#line 4 "lexer.cpp"

#define  YY_INT_ALIGNED short int

/* A lexical scanner generated by flex */

/* %not-for-header */
/* %if-c-only */
/* %if-not-reentrant */
/* %endif */
/* %endif */
/* %ok-for-header */

#define FLEX_SCANNER
#define YY_FLEX_MAJOR_VERSION 2
#define YY_FLEX_MINOR_VERSION 6
#define YY_FLEX_SUBMINOR_VERSION 4
#if YY_FLEX_SUBMINOR_VERSION > 0
#define FLEX_BETA
#endif

/* %if-c++-only */
/* %endif */

/* %if-c-only */
#ifdef yy_create_buffer
#define sql_create_buffer_ALREADY_DEFINED
#else
#define yy_create_buffer sql_create_buffer
#endif

#ifdef yy_delete_buffer
#define sql_delete_buffer_ALREADY_DEFINED
#else
#define yy_delete_buffer sql_delete_buffer
#endif

#ifdef yy_scan_buffer
#define sql_scan_buffer_ALREADY_DEFINED
#else
#define yy_scan_buffer sql_scan_buffer
#endif

#ifdef yy_scan_string
#define sql_scan_string_ALREADY_DEFINED
#else
#define yy_scan_string sql_scan_string
#endif

#ifdef yy_scan_bytes
#define sql_scan_bytes_ALREADY_DEFINED
#else
#define yy_scan_bytes sql_scan_bytes
#endif

#ifdef yy_init_buffer
#define sql_init_buffer_ALREADY_DEFINED
#else
#define yy_init_buffer sql_init_buffer
#endif

#ifdef yy_flush_buffer
#define sql_flush_buffer_ALREADY_DEFINED
#else
#define yy_flush_buffer sql_flush_buffer
#endif

#ifdef yy_load_buffer_state
#define sql_load_buffer_state_ALREADY_DEFINED
#else
#define yy_load_buffer_state sql_load_buffer_state
#endif

#ifdef yy_switch_to_buffer
#define sql_switch_to_buffer_ALREADY_DEFINED
#else
#define yy_switch_to_buffer sql_switch_to_buffer
#endif

#ifdef yypush_buffer_state
#define sqlpush_buffer_state_ALREADY_DEFINED
#else
#define yypush_buffer_state sqlpush_buffer_state
#endif

#ifdef yypop_buffer_state
#define sqlpop_buffer_state_ALREADY_DEFINED
#else
#define yypop_buffer_state sqlpop_buffer_state
#endif

#ifdef yyensure_buffer_stack
#define sqlensure_buffer_stack_ALREADY_DEFINED
#else
#define yyensure_buffer_stack sqlensure_buffer_stack
#endif

#ifdef yylex
#define sqllex_ALREADY_DEFINED
#else
#define yylex sqllex
#endif

#ifdef yyrestart
#define sqlrestart_ALREADY_DEFINED
#else
#define yyrestart sqlrestart
#endif

#ifdef yylex_init
#define sqllex_init_ALREADY_DEFINED
#else
#define yylex_init sqllex_init
#endif

#ifdef yylex_init_extra
#define sqllex_init_extra_ALREADY_DEFINED
#else
#define yylex_init_extra sqllex_init_extra
#endif

#ifdef yylex_destroy
#define sqllex_destroy_ALREADY_DEFINED
#else
#define yylex_destroy sqllex_destroy
#endif

#ifdef yyget_debug
#define sqlget_debug_ALREADY_DEFINED
#else
#define yyget_debug sqlget_debug
#endif

#ifdef yyset_debug
#define sqlset_debug_ALREADY_DEFINED
#else
#define yyset_debug sqlset_debug
#endif

#ifdef yyget_extra
#define sqlget_extra_ALREADY_DEFINED
#else
#define yyget_extra sqlget_extra
#endif

#ifdef yyset_extra
#define sqlset_extra_ALREADY_DEFINED
#else
#define yyset_extra sqlset_extra
#endif

#ifdef yyget_in
#define sqlget_in_ALREADY_DEFINED
#else
#define yyget_in sqlget_in
#endif

#ifdef yyset_in
#define sqlset_in_ALREADY_DEFINED
#else
#define yyset_in sqlset_in
#endif

#ifdef yyget_out
#define sqlget_out_ALREADY_DEFINED
#else
#define yyget_out sqlget_out
#endif

#ifdef yyset_out
#define sqlset_out_ALREADY_DEFINED
#else
#define yyset_out sqlset_out
#endif

#ifdef yyget_leng
#define sqlget_leng_ALREADY_DEFINED
#else
#define yyget_leng sqlget_leng
#endif

#ifdef yyget_text
#define sqlget_text_ALREADY_DEFINED
#else
#define yyget_text sqlget_text
#endif

#ifdef yyget_lineno
#define sqlget_lineno_ALREADY_DEFINED
#else
#define yyget_lineno sqlget_lineno
#endif

#ifdef yyset_lineno
#define sqlset_lineno_ALREADY_DEFINED
#else
#define yyset_lineno sqlset_lineno
#endif

#ifdef yyget_column
#define sqlget_column_ALREADY_DEFINED
#else
#define yyget_column sqlget_column
#endif

#ifdef yyset_column
#define sqlset_column_ALREADY_DEFINED
#else
#define yyset_column sqlset_column
#endif

#ifdef yywrap
#define sqlwrap_ALREADY_DEFINED
#else
#define yywrap sqlwrap
#endif

/* %endif */

#ifdef yyget_lval
#define sqlget_lval_ALREADY_DEFINED
#else
#define yyget_lval sqlget_lval
#endif

#ifdef yyset_lval
#define sqlset_lval_ALREADY_DEFINED
#else
#define yyset_lval sqlset_lval
#endif

#ifdef yyget_lloc
#define sqlget_lloc_ALREADY_DEFINED
#else
#define yyget_lloc sqlget_lloc
#endif

#ifdef yyset_lloc
#define sqlset_lloc_ALREADY_DEFINED
#else
#define yyset_lloc sqlset_lloc
#endif

#ifdef yyalloc
#define sqlalloc_ALREADY_DEFINED
#else
#define yyalloc sqlalloc
#endif

#ifdef yyrealloc
#define sqlrealloc_ALREADY_DEFINED
#else
#define yyrealloc sqlrealloc
#endif

#ifdef yyfree
#define sqlfree_ALREADY_DEFINED
#else
#define yyfree sqlfree
#endif

/* %if-c-only */

/* %endif */

/* First, we deal with  platform-specific or compiler-specific issues. */

/* begin standard C headers. */
/* %if-c-only */
#include <stdio.h>
#include <string.h>
#include <errno.h>
#include <stdlib.h>
/* %endif */

/* %if-tables-serialization */
/* %endif */
/* end standard C headers. */

/* %if-c-or-c++ */
/* flex integer type definitions */

#ifndef FLEXINT_H
#define FLEXINT_H

/* C99 systems have <inttypes.h>. Non-C99 systems may or may not. */

#if defined (__STDC_VERSION__) && __STDC_VERSION__ >= 199901L

/* C99 says to define __STDC_LIMIT_MACROS before including stdint.h,
 * if you want the limit (max/min) macros for int types. 
 */
#ifndef __STDC_LIMIT_MACROS
#define __STDC_LIMIT_MACROS 1
#endif

#include <inttypes.h>
typedef int8_t flex_int8_t;
typedef uint8_t flex_uint8_t;
typedef int16_t flex_int16_t;
typedef uint16_t flex_uint16_t;
typedef int32_t flex_int32_t;
typedef uint32_t flex_uint32_t;
#else
typedef signed char flex_int8_t;
typedef short int flex_int16_t;
typedef int flex_int32_t;
typedef unsigned char flex_uint8_t; 
typedef unsigned short int flex_uint16_t;
typedef unsigned int flex_uint32_t;

/* Limits of integral types. */
#ifndef INT8_MIN
#define INT8_MIN               (-128)
#endif
#ifndef INT16_MIN
#define INT16_MIN              (-32767-1)
#endif
#ifndef INT32_MIN
#define INT32_MIN              (-2147483647-1)
#endif
#ifndef INT8_MAX
#define INT8_MAX               (127)
#endif
#ifndef INT16_MAX
#define INT16_MAX              (32767)
#endif
#ifndef INT32_MAX
#define INT32_MAX              (2147483647)
#endif
#ifndef UINT8_MAX
#define UINT8_MAX              (255U)
#endif
#ifndef UINT16_MAX
#define UINT16_MAX             (65535U)
#endif
#ifndef UINT32_MAX
#define UINT32_MAX             (4294967295U)
#endif

#ifndef SIZE_MAX
#define SIZE_MAX               (~(size_t)0)
#endif

#endif /* ! C99 */

#endif /* ! FLEXINT_H */

/* %endif */

/* begin standard C++ headers. */
/* %if-c++-only */
/* %endif */

/* TODO: this is always defined, so inline it */
#define yyconst const

#if defined(__GNUC__) && __GNUC__ >= 3
#define yynoreturn __attribute__((__noreturn__))
#else
#define yynoreturn
#endif

/* %not-for-header */
/* Returned upon end-of-file. */
#define YY_NULL 0
/* %ok-for-header */

/* %not-for-header */
/* Promotes a possibly negative, possibly signed char to an
 *   integer in range [0..255] for use as an array index.
 */
#define YY_SC_TO_UI(c) ((YY_CHAR) (c))
/* %ok-for-header */

/* %if-reentrant */

/* An opaque pointer. */
#ifndef YY_TYPEDEF_YY_SCANNER_T
#define YY_TYPEDEF_YY_SCANNER_T
typedef void* yyscan_t;
#endif

/* For convenience, these vars (plus the bison vars far below)
   are macros in the reentrant scanner. */
#define yyin yyg->yyin_r
#define yyout yyg->yyout_r
#define yyextra yyg->yyextra_r
#define yyleng yyg->yyleng_r
#define yytext yyg->yytext_r
#define yylineno (YY_CURRENT_BUFFER_LVALUE->yy_bs_lineno)
#define yycolumn (YY_CURRENT_BUFFER_LVALUE->yy_bs_column)
#define yy_flex_debug yyg->yy_flex_debug_r

/* %endif */

/* %if-not-reentrant */
/* %endif */

/* Enter a start condition.  This macro really ought to take a parameter,
 * but we do it the disgusting crufty way forced on us by the ()-less
 * definition of BEGIN.
 */
#define BEGIN yyg->yy_start = 1 + 2 *
/* Translate the current start state into a value that can be later handed
 * to BEGIN to return to the state.  The YYSTATE alias is for lex
 * compatibility.
 */
#define YY_START ((yyg->yy_start - 1) / 2)
#define YYSTATE YY_START
/* Action number for EOF rule of a given start state. */
#define YY_STATE_EOF(state) (YY_END_OF_BUFFER + state + 1)
/* Special action meaning "start processing a new file". */
#define YY_NEW_FILE yyrestart( yyin , yyscanner )
#define YY_END_OF_BUFFER_CHAR 0

/* Size of default input buffer. */
#ifndef YY_BUF_SIZE
#ifdef __ia64__
/* On IA-64, the buffer size is 16k, not 8k.
 * Moreover, YY_BUF_SIZE is 2*YY_READ_BUF_SIZE in the general case.
 * Ditto for the __ia64__ case accordingly.
 */
#define YY_BUF_SIZE 32768
#else
#define YY_BUF_SIZE 16384
#endif /* __ia64__ */
#endif

/* The state buf must be large enough to hold one state per character in the main buffer.
 */
#define YY_STATE_BUF_SIZE   ((YY_BUF_SIZE + 2) * sizeof(yy_state_type))

#ifndef YY_TYPEDEF_YY_BUFFER_STATE
#define YY_TYPEDEF_YY_BUFFER_STATE
typedef struct yy_buffer_state *YY_BUFFER_STATE;
#endif

#ifndef YY_TYPEDEF_YY_SIZE_T
#define YY_TYPEDEF_YY_SIZE_T
typedef size_t yy_size_t;
#endif

/* %if-not-reentrant */
/* %endif */

/* %if-c-only */
/* %if-not-reentrant */
/* %endif */
/* %endif */

#define EOB_ACT_CONTINUE_SCAN 0
#define EOB_ACT_END_OF_FILE 1
#define EOB_ACT_LAST_MATCH 2
    
    #define YY_LESS_LINENO(n)
    #define YY_LINENO_REWIND_TO(ptr)
    
/* Return all but the first "n" matched characters back to the input stream. */
#define yyless(n) \
	do \
		{ \
		/* Undo effects of setting up yytext. */ \
        int yyless_macro_arg = (n); \
        YY_LESS_LINENO(yyless_macro_arg);\
		*yy_cp = yyg->yy_hold_char; \
		YY_RESTORE_YY_MORE_OFFSET \
		yyg->yy_c_buf_p = yy_cp = yy_bp + yyless_macro_arg - YY_MORE_ADJ; \
		YY_DO_BEFORE_ACTION; /* set up yytext again */ \
		} \
	while ( 0 )
#define unput(c) yyunput( c, yyg->yytext_ptr , yyscanner )

#ifndef YY_STRUCT_YY_BUFFER_STATE
#define YY_STRUCT_YY_BUFFER_STATE
struct yy_buffer_state
	{
/* %if-c-only */
	FILE *yy_input_file;
/* %endif */

/* %if-c++-only */
/* %endif */

	char *yy_ch_buf;		/* input buffer */
	char *yy_buf_pos;		/* current position in input buffer */

	/* Size of input buffer in bytes, not including room for EOB
	 * characters.
	 */
	int yy_buf_size;

	/* Number of characters read into yy_ch_buf, not including EOB
	 * characters.
	 */
	int yy_n_chars;

	/* Whether we "own" the buffer - i.e., we know we created it,
	 * and can realloc() it to grow it, and should free() it to
	 * delete it.
	 */
	int yy_is_our_buffer;

	/* Whether this is an "interactive" input source; if so, and
	 * if we're using stdio for input, then we want to use getc()
	 * instead of fread(), to make sure we stop fetching input after
	 * each newline.
	 */
	int yy_is_interactive;

	/* Whether we're considered to be at the beginning of a line.
	 * If so, '^' rules will be active on the next match, otherwise
	 * not.
	 */
	int yy_at_bol;

    int yy_bs_lineno; /**< The line count. */
    int yy_bs_column; /**< The column count. */

	/* Whether to try to fill the input buffer when we reach the
	 * end of it.
	 */
	int yy_fill_buffer;

	int yy_buffer_status;

#define YY_BUFFER_NEW 0
#define YY_BUFFER_NORMAL 1
	/* When an EOF's been seen but there's still some text to process
	 * then we mark the buffer as YY_EOF_PENDING, to indicate that we
	 * shouldn't try reading from the input source any more.  We might
	 * still have a bunch of tokens to match, though, because of
	 * possible backing-up.
	 *
	 * When we actually see the EOF, we change the status to "new"
	 * (via yyrestart()), so that the user can continue scanning by
	 * just pointing yyin at a new input file.
	 */
#define YY_BUFFER_EOF_PENDING 2

	};
#endif /* !YY_STRUCT_YY_BUFFER_STATE */

/* %if-c-only Standard (non-C++) definition */
/* %not-for-header */
/* %if-not-reentrant */
/* %endif */
/* %ok-for-header */

/* %endif */

/* We provide macros for accessing buffer states in case in the
 * future we want to put the buffer states in a more general
 * "scanner state".
 *
 * Returns the top of the stack, or NULL.
 */
#define YY_CURRENT_BUFFER ( yyg->yy_buffer_stack \
                          ? yyg->yy_buffer_stack[yyg->yy_buffer_stack_top] \
                          : NULL)
/* Same as previous macro, but useful when we know that the buffer stack is not
 * NULL or when we need an lvalue. For internal use only.
 */
#define YY_CURRENT_BUFFER_LVALUE yyg->yy_buffer_stack[yyg->yy_buffer_stack_top]

/* %if-c-only Standard (non-C++) definition */

/* %if-not-reentrant */
/* %not-for-header */
/* %ok-for-header */

/* %endif */

void yyrestart ( FILE *input_file , yyscan_t yyscanner );
void yy_switch_to_buffer ( YY_BUFFER_STATE new_buffer , yyscan_t yyscanner );
YY_BUFFER_STATE yy_create_buffer ( FILE *file, int size , yyscan_t yyscanner );
void yy_delete_buffer ( YY_BUFFER_STATE b , yyscan_t yyscanner );
void yy_flush_buffer ( YY_BUFFER_STATE b , yyscan_t yyscanner );
void yypush_buffer_state ( YY_BUFFER_STATE new_buffer , yyscan_t yyscanner );
void yypop_buffer_state ( yyscan_t yyscanner );

static void yyensure_buffer_stack ( yyscan_t yyscanner );
static void yy_load_buffer_state ( yyscan_t yyscanner );
static void yy_init_buffer ( YY_BUFFER_STATE b, FILE *file , yyscan_t yyscanner );
#define YY_FLUSH_BUFFER yy_flush_buffer( YY_CURRENT_BUFFER , yyscanner)

YY_BUFFER_STATE yy_scan_buffer ( char *base, yy_size_t size , yyscan_t yyscanner );
YY_BUFFER_STATE yy_scan_string ( const char *yy_str , yyscan_t yyscanner );
YY_BUFFER_STATE yy_scan_bytes ( const char *bytes, int len , yyscan_t yyscanner );

/* %endif */

void *yyalloc ( yy_size_t , yyscan_t yyscanner );
void *yyrealloc ( void *, yy_size_t , yyscan_t yyscanner );
void yyfree ( void * , yyscan_t yyscanner );

#define yy_new_buffer yy_create_buffer
#define yy_set_interactive(is_interactive) \
	{ \
	if ( ! YY_CURRENT_BUFFER ){ \
        yyensure_buffer_stack (yyscanner); \
		YY_CURRENT_BUFFER_LVALUE =    \
            yy_create_buffer( yyin, YY_BUF_SIZE , yyscanner); \
	} \
	YY_CURRENT_BUFFER_LVALUE->yy_is_interactive = is_interactive; \
	}
#define yy_set_bol(at_bol) \
	{ \
	if ( ! YY_CURRENT_BUFFER ){\
        yyensure_buffer_stack (yyscanner); \
		YY_CURRENT_BUFFER_LVALUE =    \
            yy_create_buffer( yyin, YY_BUF_SIZE , yyscanner); \
	} \
	YY_CURRENT_BUFFER_LVALUE->yy_at_bol = at_bol; \
	}
#define YY_AT_BOL() (YY_CURRENT_BUFFER_LVALUE->yy_at_bol)

/* %% [1.0] yytext/yyin/yyout/yy_state_type/yylineno etc. def's & init go here */
/* Begin user sect3 */

#define sqlwrap(yyscanner) (/*CONSTCOND*/1)
#define YY_SKIP_YYWRAP

#define FLEX_DEBUG
typedef flex_uint8_t YY_CHAR;

typedef int yy_state_type;

#define yytext_ptr yytext_r

/* %% [1.5] DFA */

/* %if-c-only Standard (non-C++) definition */

static yy_state_type yy_get_previous_state ( yyscan_t yyscanner );
static yy_state_type yy_try_NUL_trans ( yy_state_type current_state  , yyscan_t yyscanner);
static int yy_get_next_buffer ( yyscan_t yyscanner );
static void yynoreturn yy_fatal_error ( const char* msg , yyscan_t yyscanner );

/* %endif */

/* Done after the current pattern has been matched and before the
 * corresponding action - sets up yytext.
 */
#define YY_DO_BEFORE_ACTION \
	yyg->yytext_ptr = yy_bp; \
/* %% [2.0] code to fiddle yytext and yyleng for yymore() goes here \ */\
	yyleng = (int) (yy_cp - yy_bp); \
	yyg->yy_hold_char = *yy_cp; \
	*yy_cp = '\0'; \
/* %% [3.0] code to copy yytext_ptr to yytext[] goes here, if %array \ */\
	yyg->yy_c_buf_p = yy_cp;
/* %% [4.0] data tables for the DFA and the user's section 1 definitions go here */
#define YY_NUM_RULES 214
#define YY_END_OF_BUFFER 215
/* This struct is not used in this scanner,
   but its presence is necessary. */
struct yy_trans_info
	{
	flex_int32_t yy_verify;
	flex_int32_t yy_nxt;
	};
static const flex_int16_t yy_accept[782] =
    {   0,
        0,    0,  211,  211,  215,  213,    1,    1,  213,  213,
      203,  209,  203,  203,  206,  203,  203,  203,  208,  208,
      208,  208,  208,  208,  208,  208,  208,  208,  208,  208,
      208,  208,  208,  208,  208,  208,  208,  208,  208,  208,
      208,  208,  208,  211,  212,    1,  199,    0,  206,  205,
      204,  201,  200,  198,  202,  208,  208,  208,  208,  208,
        9,  208,  208,  208,  208,  208,  208,   22,  208,  208,
      208,  208,  208,  208,  208,  208,  208,  208,  208,  208,
      208,  208,  208,  208,  208,  208,  208,  208,  208,  208,
      208,  208,  208,  208,  208,  208,  208,   86,  208,   88,

       98,  208,  208,  208,  208,  208,  208,  208,  208,  208,
      208,  208,  208,  208,  208,  208,  208,  128,  208,  130,
      208,  208,  208,  208,  208,  208,  208,  208,  208,  208,
      208,  208,  208,  208,  208,  208,  208,  208,  208,  208,
      175,  208,  208,  208,  208,  208,  208,  208,  208,  208,
      208,  208,  208,  211,  210,  207,  204,    0,    2,  208,
        4,  208,    7,  208,   10,  208,  208,  208,   14,  208,
      208,   17,  208,  208,  208,  208,  208,  208,  208,  208,
      208,  208,  208,  208,  208,  208,   48,  208,  208,  208,
      208,  208,  208,  208,  208,  208,  208,   60,  208,  208,

      208,  208,  208,  208,  208,  208,  208,  208,  208,  208,
      208,  208,  208,  208,  208,  208,  208,  208,  208,  208,
      208,  208,  208,  208,   94,  208,  100,  208,  208,  208,
      208,  208,  107,  208,  208,  208,  208,  208,  208,  208,
      208,  208,  123,  208,  208,  126,  208,  208,  208,  208,
      208,  208,  208,  208,  208,  208,  208,  208,  208,  208,
      208,  208,  208,  208,  208,  208,  208,  208,  208,  155,
      208,  208,  208,  208,  208,  208,  208,  208,  208,  208,
      208,  208,  208,  208,  208,  208,  208,  208,  208,  186,
      208,  208,  208,  208,  208,  208,  208,  208,  208,    0,

      204,  208,  208,  208,  208,  208,  208,  208,  208,   18,
      208,  208,  208,   23,   24,  208,  208,  208,  208,  208,
      208,  208,  208,  208,  208,  208,   40,  208,  208,   43,
       46,   49,  208,  208,  208,  208,   56,  208,  208,   57,
       31,   58,  208,  208,  208,  208,  208,  208,  208,  208,
      208,  208,  208,  208,  208,   74,   75,  208,  208,  208,
      208,  208,  208,  208,   83,  208,  208,  208,  208,  208,
      208,   97,   99,  208,  208,  103,  104,  208,  106,  108,
      109,  208,  208,  208,  208,  208,  208,  208,  208,  121,
      120,  208,  208,  208,  208,  208,  133,  208,  208,  208,

      208,  208,  208,  208,  208,  143,  208,  208,  208,  208,
      208,  208,  208,  208,  208,  208,  208,  158,  208,  208,
      208,  208,  208,  208,  208,  208,  170,  171,  172,  208,
      208,  178,  208,  208,  208,  208,  208,  208,  185,  208,
      208,  208,  208,  192,  193,  208,  195,  196,    3,  208,
        6,    8,  208,  208,  208,  208,   19,  208,  208,  208,
       27,   28,  208,   30,  208,  208,  208,  208,  208,  208,
      208,   42,  208,  208,  208,  208,  208,  208,  208,  208,
      208,  208,  208,  208,  208,  208,  208,   67,   68,   69,
       71,  208,  208,  208,  208,   78,  208,  208,  208,  208,

       84,  208,  208,   89,   91,  208,  208,  208,  208,  208,
      105,  110,  208,  208,  208,  208,  116,  208,  208,  122,
      208,  208,  208,  131,  132,  208,  135,  208,  208,  208,
      208,  208,  208,  142,  208,  208,  208,  208,  148,  208,
      208,  208,  208,  208,  208,  208,  208,  208,  208,  208,
      165,  167,  208,  208,  208,  208,  179,  180,  208,  208,
      208,  183,  208,  208,  208,  208,  194,  197,  208,  208,
      208,   13,   15,   20,  208,   21,  208,   29,  208,   33,
      208,  208,   38,  208,   41,  208,  208,  208,  208,   54,
      208,  208,   51,  208,   61,  208,   64,  208,   66,  208,

      208,  208,   73,   76,   77,   79,   80,  208,  208,  208,
       87,  208,   92,  208,  208,  208,  101,  208,  111,  208,
      112,  114,  117,  208,  208,  124,  127,  208,  208,  208,
      208,  208,  208,  208,  208,  208,  147,  146,  208,  208,
      150,  151,  208,  153,  208,  208,  208,  162,  208,  164,
      166,  168,  208,  208,  208,  181,  208,  184,  187,  208,
      208,  191,  208,   11,  208,   16,   25,  208,   34,   35,
       36,   37,   39,  208,  208,   52,   53,  208,  208,  208,
       63,   65,   62,   70,  208,  208,   82,   85,   90,   93,
      208,  208,  102,  208,  115,  208,  119,  125,  208,  208,

      136,  137,  138,  208,  208,  141,  144,  145,  208,  152,
      156,  154,  208,  208,  208,  208,  208,  174,  208,  208,
      190,  208,  208,   12,   26,  208,   44,   47,  208,   50,
      208,   72,  208,  208,   96,  113,  208,  129,  208,  139,
      208,  149,  157,  159,  160,  208,  208,  208,  208,  182,
      188,  208,  208,   45,   55,   59,   81,   95,  208,  208,
      208,  161,  208,  208,  173,  208,  189,    5,   32,  208,
      208,  140,  163,  208,  208,  118,  134,  169,  176,  177,
        0
    } ;

static const YY_CHAR yy_ec[256] =
    {   0,
        1,    1,    1,    1,    1,    1,    1,    1,    2,    3,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    2,    4,    5,    1,    1,    6,    1,    7,    6,
        6,    6,    6,    6,    8,    9,    6,   10,   11,   10,
       10,   10,   10,   12,   10,   10,   10,    6,    6,   13,
       14,   15,    6,    1,   16,   17,   18,   19,   20,   21,
       22,   23,   24,   25,   26,   27,   28,   29,   30,   31,
       32,   33,   34,   35,   36,   37,   38,   39,   40,   41,
        6,    1,    6,    6,   42,    1,   43,   44,   45,   46,

       47,   48,   49,   50,   51,   52,   53,   54,   55,   56,
       57,   58,   59,   60,   61,   62,   63,   64,   65,   66,
       67,   68,    6,    6,    6,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,

        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1
    } ;

static const YY_CHAR yy_meta[69] =
    {   0,
        1,    1,    2,    1,    2,    1,    3,    1,    1,    4,
        4,    4,    1,    1,    1,    4,    4,    4,    4,    4,
        4,    4,    4,    4,    4,    4,    4,    4,    4,    4,
        4,    4,    4,    4,    4,    4,    4,    4,    4,    4,
        4,    4,    4,    4,    4,    4,    4,    4,    4,    4,
        4,    4,    4,    4,    4,    4,    4,    4,    4,    4,
        4,    4,    4,    4,    4,    4,    4,    4
    } ;

static const flex_int16_t yy_base[786] =
    {   0,
        0,    0,  242,  237,  242, 1595,   67,   69,  185,    0,
     1595, 1595,   63,   66,   70,   69,  180,  178,   66,  108,
      160,  205,   59,  253,   63,  117,  148,   59,   71,  161,
      203,  115,  300,  210,   56,  110,  344,  396,  242,  208,
      165,    0,   74,    0,  147,   99, 1595,  145,   94,   97,
      281, 1595, 1595, 1595, 1595,    0,  217,  252,   78,  103,
      121,  111,  125,  212,  156,  245,  210,    0,  247,  279,
      204,  265,  291,  304,  290,  332,  293,  299,  308,  313,
      309,  338,  350,  383,  344,  351,  374,  382,  351,  363,
      365,  383,  384,  407,  427,  389,  405,    0,  404,  436,

        0,  411,  411,  442,  438,  435,  448,  434,  444,  445,
      446,  450,  444,  468,  453,  456,  462,    0,  451,  470,
      470,  474,  478,  488,  497,  498,  506,  497,  482,  552,
      493,  509,  510,  512,  513,  497,  546,  536,  513,  543,
        0,  521,  535,  549,  550,  557,  552,  555,  560,  565,
      569,  556,  578,    0, 1595, 1595,  612,  190,    0,  587,
      587,  599,    0,  604,    0,  587,  596,  603,  600,  612,
      604,    0,  612,  614,  619,  618,  608,  621,  624,  614,
      614,  622,  608,  649,  637,  663,  639,  650,  664,  661,
      664,  649,  669,  656,  657,  669,  670,    0,  671,  675,

      660,  669,  662,  663,  682,  687,  670,  678,  691,  697,
      700,  704,  716,  698,  711,  717,  714,  703,  706,  720,
      711,  722,  723,  724,  727,  716,    0,  740,  713,  729,
      726,  731,  719,  734,  740,  745,  757,  746,  753,  754,
      754,  771,    0,  765,  773,  760,  771,  776,  777,  775,
      765,  772,  762,  772,  776,  806,  772,  780,  781,  782,
      797,  798,  812,  809,  808,  812,  815,  824,  811,    0,
      808,  820,  817,  816,  821,  816,  825,  827,  821,  821,
      828,  838,  819,  832,  845,  839,  857,  843,  865,    0,
      861,  872,  859,  878,  862,  860,  870,  877,  868,  195,

      377,  875,  887,  873,  867,  889,  894,  882,  896,    0,
      887,  895,  899,    0,    0,  893,  895,  909,  914,  914,
      924,  917,  929,  934,  927,  932,    0,  918,  920,  938,
      921,    0,  929,  922,  924,  932,    0,  937,  936,    0,
        0,    0,  945,  934,  930,  932,  953,  940,  958,  955,
      955,  960,  974,  968,  983,    0,    0,  973,  988,  974,
      977,  987,  981,  979,  976,  987,  979,  974,  981,  982,
      995,    0,    0,  998,  990,    0,    0,  985,    0,    0,
        0,  998,  999,  998,  996, 1008, 1026, 1027, 1019, 1019,
        0, 1039, 1038, 1031, 1027, 1028,    0, 1038, 1028, 1042,

     1049, 1050, 1043, 1048, 1057,    0, 1032, 1034, 1044, 1043,
     1039, 1051, 1059, 1053, 1063, 1085, 1081,    0, 1079, 1073,
     1077, 1093, 1093, 1094, 1081, 1086,    0,    0, 1083, 1094,
     1085,    0, 1100, 1092, 1086, 1101, 1090, 1104,    0, 1107,
     1105, 1113, 1101,    0,    0, 1115,    0, 1102,    0, 1121,
        0,    0, 1131, 1122, 1124, 1129, 1127, 1149, 1133, 1137,
        0,    0, 1148,    0, 1151, 1141, 1142, 1154, 1151, 1156,
     1155,    0, 1160, 1153, 1163, 1153, 1161, 1158, 1154, 1165,
     1170, 1155, 1156, 1171, 1187, 1178, 1196,    0,    0,  138,
        0, 1177, 1184, 1191, 1194,    0, 1200, 1190, 1200, 1192,

        0, 1197, 1207, 1208,    0, 1194, 1210, 1197, 1200, 1215,
        0,    0, 1208, 1218, 1199, 1223, 1210, 1208, 1243,    0,
     1230, 1232, 1244,    0,    0, 1235,    0, 1243, 1241, 1242,
     1249, 1244, 1258,    0, 1259, 1260, 1261, 1249,    0, 1255,
     1261, 1266, 1258, 1253, 1259, 1266, 1268, 1273, 1281, 1270,
     1265,    0, 1280, 1284, 1292, 1306,    0,    0, 1303, 1298,
     1308,    0, 1295, 1314, 1314, 1299,    0,    0, 1298, 1305,
      132,    0,    0,    0, 1306,    0, 1314,    0, 1302, 1304,
     1304, 1306, 1308, 1308,    0, 1310, 1317, 1320, 1316,    0,
     1317, 1335,    0, 1343,    0, 1353,    0, 1346,    0, 1341,

      130, 1357,    0,    0,    0,    0,    0, 1359, 1342, 1348,
        0, 1350,    0, 1352, 1366, 1371,    0, 1355,    0, 1369,
        0, 1356,    0, 1371, 1365, 1359,    0, 1354, 1361, 1368,
     1378, 1359, 1381, 1370, 1372, 1374,    0,    0, 1401, 1403,
        0, 1395, 1395,    0, 1402, 1406, 1406,    0, 1410,    0,
        0, 1422, 1423, 1405, 1423,    0, 1422,    0,    0, 1410,
     1417,    0, 1421,    0,   99,    0, 1412, 1423,    0,    0,
        0,    0,    0, 1429, 1430,    0,    0, 1431, 1417, 1424,
        0,    0,    0,    0, 1422, 1436,    0,    0,    0,    0,
     1442, 1434,    0, 1436,    0, 1463,    0,    0, 1463, 1464,

        0,    0,    0, 1451, 1465,    0,    0,    0, 1455,    0,
     1457,    0, 1457, 1458, 1464, 1462, 1468,    0, 1462, 1479,
        0, 1479, 1470,    0,    0, 1471, 1469,    0, 1471,    0,
     1483,    0, 1471, 1472,    0,    0, 1474,    0, 1484,    0,
     1494,    0,    0,    0, 1481, 1500, 1502, 1506, 1514,    0,
     1505, 1514, 1515,    0,    0,    0,    0,    0, 1515, 1528,
     1513,    0, 1528, 1533,    0, 1520,    0,    0,    0, 1518,
     1532,    0,    0, 1513, 1525,    0,    0,    0, 1521,    0,
     1595, 1582, 1586,  106, 1590
    } ;

static const flex_int16_t yy_def[786] =
    {   0,
      781,    1,  782,  782,  781,  781,  781,  781,  781,  783,
      781,  781,  781,  781,  781,  781,  781,  781,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  785,  781,  781,  781,  783,  781,  781,
      781,  781,  781,  781,  781,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,

      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  785,  781,  781,  781,  781,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,

      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  781,

      781,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,

      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,

      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,

      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,

      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
      784,  784,  784,  784,  784,  784,  784,  784,  784,  784,
        0,  781,  781,  781,  781
    } ;

static const flex_int16_t yy_nxt[1664] =
    {   0,
        6,    7,    8,    9,   10,   11,   12,   13,   14,   15,
       15,   15,   16,   17,   18,   19,   20,   21,   22,   23,
       24,   25,   26,   27,   28,   29,   30,   31,   32,   33,
       34,   35,   36,   37,   38,   39,   40,   41,   42,   43,
       42,    6,   19,   20,   21,   22,   23,   24,   25,   26,
       27,   28,   29,   30,   31,   32,   33,   34,   35,   36,
       37,   38,   39,   40,   41,   42,   43,   42,   46,   46,
       46,   46,   49,   49,   49,   50,   50,   50,   51,   49,
       49,   49,   52,   53,   57,   81,   82,   83,  102,   91,
      103,  126,   58,  153,   59,   92,  163,   84,   60,   61,

       46,   46,   51,   49,   49,   49,   50,   50,   50,   56,
      724,   57,   81,   82,   83,  102,   91,  103,  126,   58,
      153,   59,   92,  163,   84,   60,   61,   62,   63,  127,
      113,   64,   93,  128,   65,  164,   94,   66,  165,  129,
       95,  684,  665,   67,  114,  166,   96,   68,  601,  156,
      115,  167,   97,  155,   62,   63,  127,  113,   64,   93,
      128,   65,  164,   94,   66,  165,  129,   95,   98,   99,
       67,  114,  166,   96,   68,   69,  100,  115,  167,   97,
      104,  101,   70,   71,  105,  170,   72,  151,  152,   73,
      106,   55,   74,   54,  107,   98,   99,  300,   47,  301,

      301,  301,   69,  100,  301,  301,  301,  104,  101,   70,
       71,  105,  170,   72,  151,  152,   73,  106,  108,   74,
       75,  107,  109,  148,   76,  122,  110,  149,   77,  123,
      173,  150,  111,  168,   78,  159,  178,   79,  112,  124,
       80,  781,  125,   45,  160,  108,  169,   75,   45,  109,
      148,   76,  122,  110,  149,   77,  123,  173,  150,  111,
      168,   78,  159,  178,   79,  112,  124,   80,   85,  125,
      144,  160,  145,  169,  171,  146,   86,  147,  161,   87,
      174,  175,   88,  172,  179,   89,  162,  781,   90,  781,
      157,  157,  157,  781,  781,   85,  781,  144,  176,  145,

      158,  171,  146,   86,  147,  161,   87,  174,  175,   88,
      172,  179,   89,  162,  177,   90,  116,  180,  181,  182,
      117,  183,  781,  184,  186,  176,  192,  158,  118,  187,
      119,  781,  120,  185,  193,  121,  781,  194,  781,  781,
      195,  177,  196,  116,  180,  181,  182,  117,  183,  188,
      184,  186,  189,  192,  197,  118,  187,  119,  190,  120,
      185,  193,  121,  130,  194,  191,  131,  195,  198,  196,
      204,  132,  133,  781,  134,  781,  188,  205,  135,  189,
      210,  197,  781,  136,  781,  190,  301,  301,  301,  211,
      130,  781,  191,  131,  213,  198,  212,  204,  132,  133,

      199,  134,  200,  206,  205,  135,  201,  210,  208,  207,
      136,  137,  214,  202,  209,  138,  211,  203,  139,  140,
      215,  213,  216,  212,  219,  141,  220,  199,  142,  200,
      206,  143,  221,  201,  226,  208,  207,  781,  137,  214,
      202,  209,  138,  781,  203,  139,  140,  215,  217,  216,
      227,  219,  141,  220,  222,  142,  233,  228,  143,  221,
      218,  226,  229,  230,  223,  231,  232,  234,  235,  224,
      225,  237,  236,  238,  239,  217,  240,  227,  241,  244,
      245,  222,  246,  233,  228,  247,  242,  218,  248,  229,
      230,  223,  231,  232,  234,  235,  224,  225,  237,  236,

      238,  239,  243,  240,  249,  241,  244,  245,  250,  246,
      251,  252,  247,  242,  253,  248,  254,  257,  263,  264,
      255,  258,  271,  259,  272,  273,  256,  274,  275,  243,
      276,  249,  281,  260,  261,  250,  284,  251,  252,  262,
      781,  253,  781,  254,  257,  263,  264,  255,  258,  271,
      259,  272,  273,  256,  274,  275,  285,  276,  781,  281,
      260,  261,  277,  284,  279,  286,  262,  265,  289,  266,
      282,  283,  287,  267,  280,  292,  290,  295,  268,  278,
      291,  293,  288,  285,  296,  269,  270,  294,  297,  277,
      298,  279,  286,  299,  265,  289,  266,  282,  283,  287,

      267,  280,  292,  290,  295,  268,  278,  291,  293,  288,
      302,  296,  269,  270,  294,  297,  303,  298,  304,  305,
      299,  157,  157,  157,  306,  307,  308,  309,  310,  311,
      312,  158,  313,  314,  316,  317,  318,  302,  319,  320,
      321,  323,  325,  303,  324,  304,  305,  327,  315,  322,
      326,  306,  307,  308,  309,  310,  311,  312,  158,  313,
      314,  316,  317,  318,  328,  319,  320,  321,  323,  325,
      329,  324,  332,  333,  327,  315,  322,  326,  330,  334,
      335,  337,  331,  338,  336,  339,  340,  341,  342,  343,
      344,  328,  345,  346,  349,  347,  350,  329,  348,  332,

      333,  351,  352,  353,  354,  330,  334,  335,  337,  331,
      338,  336,  339,  340,  341,  342,  343,  344,  355,  345,
      346,  349,  347,  350,  356,  348,  357,  358,  351,  352,
      353,  354,  359,  360,  361,  362,  363,  364,  365,  366,
      367,  368,  369,  370,  373,  355,  371,  376,  377,  378,
      379,  356,  380,  357,  358,  381,  372,  382,  374,  359,
      360,  361,  362,  363,  364,  365,  366,  367,  368,  369,
      370,  373,  375,  371,  376,  377,  378,  379,  383,  380,
      384,  386,  381,  372,  382,  374,  385,  387,  388,  389,
      390,  391,  392,  393,  394,  395,  396,  397,  398,  375,

      399,  400,  401,  402,  405,  383,  406,  384,  386,  781,
      407,  408,  409,  385,  387,  388,  389,  390,  391,  392,
      393,  394,  395,  396,  397,  398,  403,  399,  400,  401,
      402,  405,  410,  406,  411,  412,  404,  407,  408,  409,
      413,  414,  415,  416,  417,  418,  419,  420,  421,  422,
      423,  424,  425,  403,  426,  427,  428,  429,  430,  410,
      431,  411,  412,  404,  432,  433,  436,  413,  414,  415,
      416,  417,  418,  419,  420,  421,  422,  423,  424,  425,
      437,  426,  427,  428,  429,  430,  434,  431,  435,  438,
      439,  432,  433,  436,  440,  441,  443,  444,  445,  447,

      448,  442,  446,  449,  450,  451,  452,  437,  453,  454,
      455,  456,  457,  434,  458,  435,  438,  439,  459,  460,
      461,  440,  441,  443,  444,  445,  447,  448,  442,  446,
      449,  450,  451,  452,  462,  453,  454,  455,  456,  457,
      463,  458,  464,  465,  466,  459,  460,  461,  467,  468,
      469,  470,  471,  472,  473,  474,  475,  476,  477,  478,
      479,  462,  480,  481,  482,  483,  484,  463,  485,  464,
      465,  466,  486,  487,  488,  467,  468,  469,  470,  471,
      472,  473,  474,  475,  476,  477,  478,  479,  489,  480,
      481,  482,  483,  484,  490,  485,  491,  492,  493,  486,

      487,  488,  494,  495,  496,  497,  498,  499,  500,  501,
      502,  503,  504,  505,  506,  489,  507,  509,  510,  511,
      512,  490,  513,  491,  492,  493,  514,  508,  515,  494,
      495,  496,  497,  498,  499,  500,  501,  502,  503,  504,
      505,  506,  516,  507,  509,  510,  511,  512,  517,  513,
      518,  519,  520,  514,  508,  515,  521,  522,  523,  524,
      525,  526,  527,  528,  529,  530,  531,  532,  535,  516,
      536,  537,  538,  539,  540,  517,  541,  518,  519,  520,
      533,  542,  543,  521,  522,  523,  524,  525,  526,  527,
      528,  529,  530,  531,  532,  535,  534,  536,  537,  538,

      539,  540,  544,  541,  545,  546,  547,  533,  542,  543,
      548,  549,  550,  551,  552,  553,  554,  555,  556,  557,
      558,  559,  560,  534,  561,  562,  563,  564,  565,  544,
      566,  545,  546,  547,  567,  568,  569,  548,  549,  550,
      551,  552,  553,  554,  555,  556,  557,  558,  559,  560,
      570,  561,  562,  563,  564,  565,  571,  566,  572,  573,
      574,  567,  568,  569,  575,  576,  577,  578,  579,  580,
      581,  582,  583,  584,  585,  586,  587,  570,  588,  589,
      590,  591,  592,  571,  593,  572,  573,  574,  594,  595,
      596,  575,  576,  577,  578,  579,  580,  581,  582,  583,

      584,  585,  586,  587,  597,  588,  589,  590,  591,  592,
      598,  593,  599,  600,  602,  594,  595,  596,  603,  604,
      605,  606,  607,  608,  609,  610,  611,  612,  613,  614,
      615,  597,  617,  616,  618,  619,  620,  598,  621,  599,
      600,  602,  622,  623,  624,  603,  604,  605,  606,  607,
      608,  609,  610,  611,  612,  613,  614,  615,  625,  617,
      616,  618,  619,  620,  626,  621,  627,  628,  629,  622,
      623,  624,  630,  631,  632,  633,  634,  635,  636,  637,
      638,  639,  640,  641,  642,  625,  643,  644,  645,  646,
      647,  626,  648,  627,  628,  629,  649,  650,  651,  630,

      631,  632,  633,  634,  635,  636,  637,  638,  639,  640,
      641,  642,  652,  643,  644,  645,  646,  647,  653,  648,
      654,  655,  656,  649,  650,  651,  657,  658,  659,  660,
      661,  662,  663,  664,  666,  667,  668,  669,  670,  652,
      671,  672,  673,  674,  675,  653,  676,  654,  655,  656,
      677,  678,  679,  657,  658,  659,  660,  661,  662,  663,
      664,  666,  667,  668,  669,  670,  680,  671,  672,  673,
      674,  675,  681,  676,  682,  683,  685,  677,  678,  679,
      686,  687,  688,  689,  690,  691,  692,  693,  694,  695,
      696,  697,  698,  680,  699,  700,  701,  702,  703,  681,

      704,  682,  683,  685,  705,  706,  707,  686,  687,  688,
      689,  690,  691,  692,  693,  694,  695,  696,  697,  698,
      708,  699,  700,  701,  702,  703,  709,  704,  710,  711,
      712,  705,  706,  707,  713,  714,  715,  716,  717,  718,
      719,  720,  721,  722,  723,  725,  726,  708,  727,  728,
      729,  730,  731,  709,  732,  710,  711,  712,  733,  734,
      735,  713,  714,  715,  716,  717,  718,  719,  720,  721,
      722,  723,  725,  726,  736,  727,  728,  729,  730,  731,
      737,  732,  738,  739,  740,  733,  734,  735,  741,  742,
      743,  744,  745,  746,  747,  748,  749,  750,  751,  752,

      753,  736,  754,  755,  756,  757,  758,  737,  759,  738,
      739,  740,  760,  761,  762,  741,  742,  743,  744,  745,
      746,  747,  748,  749,  750,  751,  752,  753,  763,  754,
      755,  756,  757,  758,  764,  759,  765,  766,  767,  760,
      761,  762,  768,  769,  770,  771,  772,  773,  774,  775,
      776,  777,  778,  779,  780,  763,  781,  781,  781,  781,
      781,  764,  781,  765,  766,  767,  781,  781,  781,  768,
      769,  770,  771,  772,  773,  774,  775,  776,  777,  778,
      779,  780,   44,   44,   44,   44,   48,  781,   48,   48,
      154,  154,  781,  154,    5,  781,  781,  781,  781,  781,

      781,  781,  781,  781,  781,  781,  781,  781,  781,  781,
      781,  781,  781,  781,  781,  781,  781,  781,  781,  781,
      781,  781,  781,  781,  781,  781,  781,  781,  781,  781,
      781,  781,  781,  781,  781,  781,  781,  781,  781,  781,
      781,  781,  781,  781,  781,  781,  781,  781,  781,  781,
      781,  781,  781,  781,  781,  781,  781,  781,  781,  781,
      781,  781,  781
    } ;

static const flex_int16_t yy_chk[1664] =
    {   0,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    7,    7,
        8,    8,   13,   13,   13,   14,   14,   14,   15,   15,
       15,   15,   16,   16,   19,   23,   23,   23,   28,   25,
       29,   35,   19,   43,   19,   25,   59,   23,   19,   19,

       46,   46,   49,   49,   49,   49,   50,   50,   50,  784,
      665,   19,   23,   23,   23,   28,   25,   29,   35,   19,
       43,   19,   25,   59,   23,   19,   19,   20,   20,   36,
       32,   20,   26,   36,   20,   60,   26,   20,   61,   36,
       26,  601,  571,   20,   32,   62,   26,   20,  490,   48,
       32,   63,   26,   45,   20,   20,   36,   32,   20,   26,
       36,   20,   60,   26,   20,   61,   36,   26,   27,   27,
       20,   32,   62,   26,   20,   21,   27,   32,   63,   26,
       30,   27,   21,   21,   30,   65,   21,   41,   41,   21,
       30,   18,   21,   17,   30,   27,   27,  158,    9,  158,

      158,  158,   21,   27,  300,  300,  300,   30,   27,   21,
       21,   30,   65,   21,   41,   41,   21,   30,   31,   21,
       22,   30,   31,   40,   22,   34,   31,   40,   22,   34,
       67,   40,   31,   64,   22,   57,   71,   22,   31,   34,
       22,    5,   34,    4,   57,   31,   64,   22,    3,   31,
       40,   22,   34,   31,   40,   22,   34,   67,   40,   31,
       64,   22,   57,   71,   22,   31,   34,   22,   24,   34,
       39,   57,   39,   64,   66,   39,   24,   39,   58,   24,
       69,   69,   24,   66,   72,   24,   58,    0,   24,    0,
       51,   51,   51,    0,    0,   24,    0,   39,   70,   39,

       51,   66,   39,   24,   39,   58,   24,   69,   69,   24,
       66,   72,   24,   58,   70,   24,   33,   73,   73,   73,
       33,   73,    0,   74,   75,   70,   77,   51,   33,   75,
       33,    0,   33,   74,   78,   33,    0,   79,    0,    0,
       80,   70,   81,   33,   73,   73,   73,   33,   73,   76,
       74,   75,   76,   77,   82,   33,   75,   33,   76,   33,
       74,   78,   33,   37,   79,   76,   37,   80,   83,   81,
       85,   37,   37,    0,   37,    0,   76,   86,   37,   76,
       89,   82,    0,   37,    0,   76,  301,  301,  301,   90,
       37,    0,   76,   37,   91,   83,   90,   85,   37,   37,

       84,   37,   84,   87,   86,   37,   84,   89,   88,   87,
       37,   38,   92,   84,   88,   38,   90,   84,   38,   38,
       93,   91,   94,   90,   96,   38,   97,   84,   38,   84,
       87,   38,   99,   84,  102,   88,   87,    0,   38,   92,
       84,   88,   38,    0,   84,   38,   38,   93,   95,   94,
      103,   96,   38,   97,  100,   38,  106,  104,   38,   99,
       95,  102,  104,  105,  100,  105,  105,  107,  108,  100,
      100,  109,  108,  110,  111,   95,  112,  103,  113,  115,
      116,  100,  117,  106,  104,  119,  114,   95,  120,  104,
      105,  100,  105,  105,  107,  108,  100,  100,  109,  108,

      110,  111,  114,  112,  121,  113,  115,  116,  122,  117,
      123,  124,  119,  114,  124,  120,  125,  126,  128,  129,
      125,  127,  131,  127,  132,  133,  125,  134,  135,  114,
      136,  121,  139,  127,  127,  122,  142,  123,  124,  127,
        0,  124,    0,  125,  126,  128,  129,  125,  127,  131,
      127,  132,  133,  125,  134,  135,  142,  136,    0,  139,
      127,  127,  137,  142,  138,  143,  127,  130,  145,  130,
      140,  140,  144,  130,  138,  147,  146,  149,  130,  137,
      146,  148,  144,  142,  150,  130,  130,  148,  151,  137,
      152,  138,  143,  153,  130,  145,  130,  140,  140,  144,

      130,  138,  147,  146,  149,  130,  137,  146,  148,  144,
      160,  150,  130,  130,  148,  151,  161,  152,  162,  164,
      153,  157,  157,  157,  166,  167,  168,  169,  170,  170,
      171,  157,  173,  174,  175,  176,  177,  160,  178,  179,
      180,  181,  182,  161,  181,  162,  164,  183,  174,  180,
      182,  166,  167,  168,  169,  170,  170,  171,  157,  173,
      174,  175,  176,  177,  184,  178,  179,  180,  181,  182,
      185,  181,  187,  188,  183,  174,  180,  182,  186,  189,
      190,  191,  186,  192,  190,  193,  194,  195,  196,  197,
      199,  184,  200,  201,  203,  202,  204,  185,  202,  187,

      188,  205,  206,  207,  208,  186,  189,  190,  191,  186,
      192,  190,  193,  194,  195,  196,  197,  199,  209,  200,
      201,  203,  202,  204,  210,  202,  211,  212,  205,  206,
      207,  208,  213,  214,  215,  216,  217,  218,  219,  220,
      221,  222,  223,  224,  226,  209,  225,  229,  230,  231,
      232,  210,  233,  211,  212,  234,  225,  235,  228,  213,
      214,  215,  216,  217,  218,  219,  220,  221,  222,  223,
      224,  226,  228,  225,  229,  230,  231,  232,  236,  233,
      237,  238,  234,  225,  235,  228,  237,  239,  240,  241,
      242,  244,  245,  246,  247,  248,  249,  250,  251,  228,

      252,  253,  254,  255,  257,  236,  258,  237,  238,    0,
      259,  260,  261,  237,  239,  240,  241,  242,  244,  245,
      246,  247,  248,  249,  250,  251,  256,  252,  253,  254,
      255,  257,  262,  258,  263,  264,  256,  259,  260,  261,
      265,  266,  267,  268,  269,  271,  272,  273,  274,  275,
      276,  277,  278,  256,  279,  280,  281,  282,  283,  262,
      284,  263,  264,  256,  285,  286,  288,  265,  266,  267,
      268,  269,  271,  272,  273,  274,  275,  276,  277,  278,
      289,  279,  280,  281,  282,  283,  287,  284,  287,  291,
      292,  285,  286,  288,  293,  294,  295,  296,  297,  298,

      299,  294,  297,  302,  303,  304,  305,  289,  306,  307,
      308,  309,  311,  287,  312,  287,  291,  292,  313,  316,
      317,  293,  294,  295,  296,  297,  298,  299,  294,  297,
      302,  303,  304,  305,  318,  306,  307,  308,  309,  311,
      319,  312,  320,  321,  322,  313,  316,  317,  323,  324,
      325,  326,  328,  329,  330,  331,  333,  334,  335,  336,
      338,  318,  339,  343,  344,  345,  346,  319,  347,  320,
      321,  322,  348,  349,  350,  323,  324,  325,  326,  328,
      329,  330,  331,  333,  334,  335,  336,  338,  351,  339,
      343,  344,  345,  346,  352,  347,  353,  354,  355,  348,

      349,  350,  358,  359,  360,  361,  362,  363,  364,  365,
      366,  367,  368,  369,  370,  351,  371,  374,  375,  378,
      382,  352,  383,  353,  354,  355,  384,  371,  385,  358,
      359,  360,  361,  362,  363,  364,  365,  366,  367,  368,
      369,  370,  386,  371,  374,  375,  378,  382,  387,  383,
      388,  389,  390,  384,  371,  385,  392,  393,  394,  395,
      396,  398,  399,  400,  401,  402,  403,  404,  407,  386,
      408,  409,  410,  411,  412,  387,  413,  388,  389,  390,
      405,  414,  415,  392,  393,  394,  395,  396,  398,  399,
      400,  401,  402,  403,  404,  407,  405,  408,  409,  410,

      411,  412,  416,  413,  417,  419,  420,  405,  414,  415,
      421,  422,  423,  424,  425,  426,  429,  430,  431,  433,
      434,  435,  436,  405,  437,  438,  440,  441,  442,  416,
      443,  417,  419,  420,  446,  448,  450,  421,  422,  423,
      424,  425,  426,  429,  430,  431,  433,  434,  435,  436,
      453,  437,  438,  440,  441,  442,  454,  443,  455,  456,
      457,  446,  448,  450,  458,  459,  460,  463,  465,  466,
      467,  468,  469,  470,  471,  473,  474,  453,  475,  476,
      477,  478,  479,  454,  480,  455,  456,  457,  481,  482,
      483,  458,  459,  460,  463,  465,  466,  467,  468,  469,

      470,  471,  473,  474,  484,  475,  476,  477,  478,  479,
      485,  480,  486,  487,  492,  481,  482,  483,  493,  494,
      495,  497,  498,  499,  500,  502,  503,  504,  506,  507,
      508,  484,  509,  508,  510,  513,  514,  485,  515,  486,
      487,  492,  516,  517,  518,  493,  494,  495,  497,  498,
      499,  500,  502,  503,  504,  506,  507,  508,  519,  509,
      508,  510,  513,  514,  521,  515,  522,  523,  526,  516,
      517,  518,  528,  529,  530,  531,  532,  533,  535,  536,
      537,  538,  540,  541,  542,  519,  543,  544,  545,  546,
      547,  521,  548,  522,  523,  526,  549,  550,  551,  528,

      529,  530,  531,  532,  533,  535,  536,  537,  538,  540,
      541,  542,  553,  543,  544,  545,  546,  547,  554,  548,
      555,  556,  559,  549,  550,  551,  560,  561,  563,  564,
      565,  566,  569,  570,  575,  577,  579,  580,  581,  553,
      582,  583,  584,  586,  587,  554,  588,  555,  556,  559,
      589,  591,  592,  560,  561,  563,  564,  565,  566,  569,
      570,  575,  577,  579,  580,  581,  594,  582,  583,  584,
      586,  587,  596,  588,  598,  600,  602,  589,  591,  592,
      608,  609,  610,  612,  614,  615,  616,  618,  620,  622,
      624,  625,  626,  594,  628,  629,  630,  631,  632,  596,

      633,  598,  600,  602,  634,  635,  636,  608,  609,  610,
      612,  614,  615,  616,  618,  620,  622,  624,  625,  626,
      639,  628,  629,  630,  631,  632,  640,  633,  642,  643,
      645,  634,  635,  636,  646,  647,  649,  652,  653,  654,
      655,  657,  660,  661,  663,  667,  668,  639,  674,  675,
      678,  679,  680,  640,  685,  642,  643,  645,  686,  691,
      692,  646,  647,  649,  652,  653,  654,  655,  657,  660,
      661,  663,  667,  668,  694,  674,  675,  678,  679,  680,
      696,  685,  699,  700,  704,  686,  691,  692,  705,  709,
      711,  713,  714,  715,  716,  717,  719,  720,  722,  723,

      726,  694,  727,  729,  731,  733,  734,  696,  737,  699,
      700,  704,  739,  741,  745,  705,  709,  711,  713,  714,
      715,  716,  717,  719,  720,  722,  723,  726,  746,  727,
      729,  731,  733,  734,  747,  737,  748,  749,  751,  739,
      741,  745,  752,  753,  759,  760,  761,  763,  764,  766,
      770,  771,  774,  775,  779,  746,    0,    0,    0,    0,
        0,  747,    0,  748,  749,  751,    0,    0,    0,  752,
      753,  759,  760,  761,  763,  764,  766,  770,  771,  774,
      775,  779,  782,  782,  782,  782,  783,    0,  783,  783,
      785,  785,    0,  785,  781,  781,  781,  781,  781,  781,

      781,  781,  781,  781,  781,  781,  781,  781,  781,  781,
      781,  781,  781,  781,  781,  781,  781,  781,  781,  781,
      781,  781,  781,  781,  781,  781,  781,  781,  781,  781,
      781,  781,  781,  781,  781,  781,  781,  781,  781,  781,
      781,  781,  781,  781,  781,  781,  781,  781,  781,  781,
      781,  781,  781,  781,  781,  781,  781,  781,  781,  781,
      781,  781,  781
    } ;

static const flex_int16_t yy_rule_linenum[214] =
    {   0,
       29,   31,   32,   33,   34,   35,   36,   37,   38,   39,
       40,   41,   42,   43,   44,   45,   46,   47,   48,   49,
       50,   51,   52,   53,   54,   55,   56,   57,   58,   59,
       60,   61,   62,   63,   64,   65,   66,   67,   68,   69,
       70,   71,   72,   73,   74,   75,   76,   77,   78,   79,
       80,   81,   82,   83,   84,   85,   86,   87,   88,   89,
       90,   91,   92,   93,   94,   95,   96,   97,   98,   99,
      100,  101,  102,  103,  104,  105,  106,  107,  108,  109,
      110,  111,  112,  113,  114,  115,  116,  117,  118,  119,
      120,  121,  122,  123,  124,  125,  126,  127,  128,  129,

      130,  131,  132,  133,  134,  135,  136,  137,  138,  139,
      140,  141,  142,  143,  144,  145,  146,  147,  148,  149,
      150,  151,  152,  153,  154,  155,  156,  157,  158,  159,
      160,  161,  162,  163,  164,  165,  166,  167,  168,  169,
      170,  171,  172,  173,  174,  175,  176,  177,  178,  179,
      180,  181,  182,  183,  184,  185,  186,  187,  188,  189,
      190,  191,  192,  193,  194,  195,  196,  197,  198,  199,
      200,  201,  202,  203,  204,  205,  206,  207,  208,  209,
      210,  211,  212,  213,  214,  215,  216,  217,  218,  219,
      220,  221,  222,  223,  224,  225,  226,  228,  229,  230,

      231,  232,  234,  236,  237,  242,  252,  261,  266,  267,
      268,  269,  272
    } ;

/* The intent behind this definition is that it'll catch
 * any uses of REJECT which flex missed.
 */
#define REJECT reject_used_but_not_detected
#define yymore() yymore_used_but_not_detected
#define YY_MORE_ADJ 0
#define YY_RESTORE_YY_MORE_OFFSET
#line 1 "lexer.l"
#line 2 "lexer.l"

#include "parser.h"
#include "parser_result.h"
#include <sstream>

static thread_local std::stringstream string_buffer;

#line 1397 "lexer.cpp"
#define YY_NO_INPUT 1

#line 1400 "lexer.cpp"

#define INITIAL 0
#define SINGLE_QUOTED_STRING 1

#ifndef YY_NO_UNISTD_H
/* Special case for "unistd.h", since it is non-ANSI. We include it way
 * down here because we want the user's section 1 to have been scanned first.
 * The user has a chance to override it with an option.
 */
/* %if-c-only */
#include <unistd.h>
/* %endif */
/* %if-c++-only */
/* %endif */
#endif

#ifndef YY_EXTRA_TYPE
#define YY_EXTRA_TYPE void *
#endif

/* %if-c-only Reentrant structure and macros (non-C++). */
/* %if-reentrant */

/* Holds the entire state of the reentrant scanner. */
struct yyguts_t
    {

    /* User-defined. Not touched by flex. */
    YY_EXTRA_TYPE yyextra_r;

    /* The rest are the same as the globals declared in the non-reentrant scanner. */
    FILE *yyin_r, *yyout_r;
    size_t yy_buffer_stack_top; /**< index of top of stack. */
    size_t yy_buffer_stack_max; /**< capacity of stack. */
    YY_BUFFER_STATE * yy_buffer_stack; /**< Stack as an array. */
    char yy_hold_char;
    int yy_n_chars;
    int yyleng_r;
    char *yy_c_buf_p;
    int yy_init;
    int yy_start;
    int yy_did_buffer_switch_on_eof;
    int yy_start_stack_ptr;
    int yy_start_stack_depth;
    int *yy_start_stack;
    yy_state_type yy_last_accepting_state;
    char* yy_last_accepting_cpos;

    int yylineno_r;
    int yy_flex_debug_r;

    char *yytext_r;
    int yy_more_flag;
    int yy_more_len;

    YYSTYPE * yylval_r;

    YYLTYPE * yylloc_r;

    }; /* end struct yyguts_t */

/* %if-c-only */

static int yy_init_globals ( yyscan_t yyscanner );

/* %endif */

/* %if-reentrant */

    /* This must go here because YYSTYPE and YYLTYPE are included
     * from bison output in section 1.*/
    #    define yylval yyg->yylval_r
    
    #    define yylloc yyg->yylloc_r
    
int yylex_init (yyscan_t* scanner);

int yylex_init_extra ( YY_EXTRA_TYPE user_defined, yyscan_t* scanner);

/* %endif */

/* %endif End reentrant structures and macros. */

/* Accessor methods to globals.
   These are made visible to non-reentrant scanners for convenience. */

int yylex_destroy ( yyscan_t yyscanner );

int yyget_debug ( yyscan_t yyscanner );

void yyset_debug ( int debug_flag , yyscan_t yyscanner );

YY_EXTRA_TYPE yyget_extra ( yyscan_t yyscanner );

void yyset_extra ( YY_EXTRA_TYPE user_defined , yyscan_t yyscanner );

FILE *yyget_in ( yyscan_t yyscanner );

void yyset_in  ( FILE * _in_str , yyscan_t yyscanner );

FILE *yyget_out ( yyscan_t yyscanner );

void yyset_out  ( FILE * _out_str , yyscan_t yyscanner );

			int yyget_leng ( yyscan_t yyscanner );

char *yyget_text ( yyscan_t yyscanner );

int yyget_lineno ( yyscan_t yyscanner );

void yyset_lineno ( int _line_number , yyscan_t yyscanner );

int yyget_column  ( yyscan_t yyscanner );

void yyset_column ( int _column_no , yyscan_t yyscanner );

/* %if-bison-bridge */

YYSTYPE * yyget_lval ( yyscan_t yyscanner );

void yyset_lval ( YYSTYPE * yylval_param , yyscan_t yyscanner );

       YYLTYPE *yyget_lloc ( yyscan_t yyscanner );
    
        void yyset_lloc ( YYLTYPE * yylloc_param , yyscan_t yyscanner );
    
/* %endif */

/* Macros after this point can all be overridden by user definitions in
 * section 1.
 */

#ifndef YY_SKIP_YYWRAP
#ifdef __cplusplus
extern "C" int yywrap ( yyscan_t yyscanner );
#else
extern int yywrap ( yyscan_t yyscanner );
#endif
#endif

/* %not-for-header */
#ifndef YY_NO_UNPUT
    
#endif
/* %ok-for-header */

/* %endif */

#ifndef yytext_ptr
static void yy_flex_strncpy ( char *, const char *, int , yyscan_t yyscanner);
#endif

#ifdef YY_NEED_STRLEN
static int yy_flex_strlen ( const char * , yyscan_t yyscanner);
#endif

#ifndef YY_NO_INPUT
/* %if-c-only Standard (non-C++) definition */
/* %not-for-header */
#ifdef __cplusplus
static int yyinput ( yyscan_t yyscanner );
#else
static int input ( yyscan_t yyscanner );
#endif
/* %ok-for-header */

/* %endif */
#endif

/* %if-c-only */

/* %endif */

/* Amount of stuff to slurp up with each read. */
#ifndef YY_READ_BUF_SIZE
#ifdef __ia64__
/* On IA-64, the buffer size is 16k, not 8k */
#define YY_READ_BUF_SIZE 16384
#else
#define YY_READ_BUF_SIZE 8192
#endif /* __ia64__ */
#endif

/* Copy whatever the last rule matched to the standard output. */
#ifndef ECHO
/* %if-c-only Standard (non-C++) definition */
/* This used to be an fputs(), but since the string might contain NUL's,
 * we now use fwrite().
 */
#define ECHO do { if (fwrite( yytext, (size_t) yyleng, 1, yyout )) {} } while (0)
/* %endif */
/* %if-c++-only C++ definition */
/* %endif */
#endif

/* Gets input and stuffs it into "buf".  number of characters read, or YY_NULL,
 * is returned in "result".
 */
#ifndef YY_INPUT
#define YY_INPUT(buf,result,max_size) \
/* %% [5.0] fread()/read() definition of YY_INPUT goes here unless we're doing C++ \ */\
	if ( YY_CURRENT_BUFFER_LVALUE->yy_is_interactive ) \
		{ \
		int c = '*'; \
		int n; \
		for ( n = 0; n < max_size && \
			     (c = getc( yyin )) != EOF && c != '\n'; ++n ) \
			buf[n] = (char) c; \
		if ( c == '\n' ) \
			buf[n++] = (char) c; \
		if ( c == EOF && ferror( yyin ) ) \
			YY_FATAL_ERROR( "input in flex scanner failed" ); \
		result = n; \
		} \
	else \
		{ \
		errno=0; \
		while ( (result = (int) fread(buf, 1, (yy_size_t) max_size, yyin)) == 0 && ferror(yyin)) \
			{ \
			if( errno != EINTR) \
				{ \
				YY_FATAL_ERROR( "input in flex scanner failed" ); \
				break; \
				} \
			errno=0; \
			clearerr(yyin); \
			} \
		}\
\
/* %if-c++-only C++ definition \ */\
/* %endif */

#endif

/* No semi-colon after return; correct usage is to write "yyterminate();" -
 * we don't want an extra ';' after the "return" because that will cause
 * some compilers to complain about unreachable statements.
 */
#ifndef yyterminate
#define yyterminate() return YY_NULL
#endif

/* Number of entries by which start-condition stack grows. */
#ifndef YY_START_STACK_INCR
#define YY_START_STACK_INCR 25
#endif

/* Report a fatal error. */
#ifndef YY_FATAL_ERROR
/* %if-c-only */
#define YY_FATAL_ERROR(msg) yy_fatal_error( msg , yyscanner)
/* %endif */
/* %if-c++-only */
/* %endif */
#endif

/* %if-tables-serialization structures and prototypes */
/* %not-for-header */
/* %ok-for-header */

/* %not-for-header */
/* %tables-yydmap generated elements */
/* %endif */
/* end tables serialization structures and prototypes */

/* %ok-for-header */

/* Default declaration of generated scanner - a define so the user can
 * easily add parameters.
 */
#ifndef YY_DECL
#define YY_DECL_IS_OURS 1
/* %if-c-only Standard (non-C++) definition */

extern int yylex \
               (YYSTYPE * yylval_param, YYLTYPE * yylloc_param , yyscan_t yyscanner);

#define YY_DECL int yylex \
               (YYSTYPE * yylval_param, YYLTYPE * yylloc_param , yyscan_t yyscanner)
/* %endif */
/* %if-c++-only C++ definition */
/* %endif */
#endif /* !YY_DECL */

/* Code executed at the beginning of each rule, after yytext and yyleng
 * have been set up.
 */
#ifndef YY_USER_ACTION
#define YY_USER_ACTION
#endif

/* Code executed at the end of each rule. */
#ifndef YY_BREAK
#define YY_BREAK /*LINTED*/break;
#endif

/* %% [6.0] YY_RULE_SETUP definition goes here */
#define YY_RULE_SETUP \
	YY_USER_ACTION

/* %not-for-header */
/** The main scanner function which does all the work.
 */
YY_DECL
{
	yy_state_type yy_current_state;
	char *yy_cp, *yy_bp;
	int yy_act;
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;

    yylval = yylval_param;

    yylloc = yylloc_param;

	if ( !yyg->yy_init )
		{
		yyg->yy_init = 1;

#ifdef YY_USER_INIT
		YY_USER_INIT;
#endif

		if ( ! yyg->yy_start )
			yyg->yy_start = 1;	/* first start state */

		if ( ! yyin )
/* %if-c-only */
			yyin = stdin;
/* %endif */
/* %if-c++-only */
/* %endif */

		if ( ! yyout )
/* %if-c-only */
			yyout = stdout;
/* %endif */
/* %if-c++-only */
/* %endif */

		if ( ! YY_CURRENT_BUFFER ) {
			yyensure_buffer_stack (yyscanner);
			YY_CURRENT_BUFFER_LVALUE =
				yy_create_buffer( yyin, YY_BUF_SIZE , yyscanner);
		}

		yy_load_buffer_state( yyscanner );
		}

	{
/* %% [7.0] user's declarations go here */
#line 27 "lexer.l"


#line 1754 "lexer.cpp"

	while ( /*CONSTCOND*/1 )		/* loops until end-of-file is reached */
		{
/* %% [8.0] yymore()-related code goes here */
		yy_cp = yyg->yy_c_buf_p;

		/* Support of yytext. */
		*yy_cp = yyg->yy_hold_char;

		/* yy_bp points to the position in yy_ch_buf of the start of
		 * the current run.
		 */
		yy_bp = yy_cp;

/* %% [9.0] code to set up and find next match goes here */
		yy_current_state = yyg->yy_start;
yy_match:
		do
			{
			YY_CHAR yy_c = yy_ec[YY_SC_TO_UI(*yy_cp)] ;
			if ( yy_accept[yy_current_state] )
				{
				yyg->yy_last_accepting_state = yy_current_state;
				yyg->yy_last_accepting_cpos = yy_cp;
				}
			while ( yy_chk[yy_base[yy_current_state] + yy_c] != yy_current_state )
				{
				yy_current_state = (int) yy_def[yy_current_state];
				if ( yy_current_state >= 782 )
					yy_c = yy_meta[yy_c];
				}
			yy_current_state = yy_nxt[yy_base[yy_current_state] + yy_c];
			++yy_cp;
			}
		while ( yy_current_state != 781 );
		yy_cp = yyg->yy_last_accepting_cpos;
		yy_current_state = yyg->yy_last_accepting_state;

yy_find_action:
/* %% [10.0] code to find the action number goes here */
		yy_act = yy_accept[yy_current_state];

		YY_DO_BEFORE_ACTION;

/* %% [11.0] code for yylineno update goes here */

do_action:	/* This label is used only to access EOF actions. */

/* %% [12.0] debug code goes here */
		if ( yy_flex_debug )
			{
			if ( yy_act == 0 )
				fprintf( stderr, "--scanner backing up\n" );
			else if ( yy_act < 214 )
				fprintf( stderr, "--accepting rule at line %ld (\"%s\")\n",
				         (long)yy_rule_linenum[yy_act], yytext );
			else if ( yy_act == 214 )
				fprintf( stderr, "--accepting default rule (\"%s\")\n",
				         yytext );
			else if ( yy_act == 215 )
				fprintf( stderr, "--(end of buffer or a NUL)\n" );
			else
				fprintf( stderr, "--EOF (start condition %d)\n", YY_START );
			}

		switch ( yy_act )
	{ /* beginning of action switch */
/* %% [13.0] actions go here */
			case 0: /* must back up */
			/* undo the effects of YY_DO_BEFORE_ACTION */
			*yy_cp = yyg->yy_hold_char;
			yy_cp = yyg->yy_last_accepting_cpos;
			yy_current_state = yyg->yy_last_accepting_state;
			goto yy_find_action;

case 1:
/* rule 1 can match eol */
YY_RULE_SETUP
#line 29 "lexer.l"
/* ignore \t\n and space */;
	YY_BREAK
case 2:
YY_RULE_SETUP
#line 31 "lexer.l"
{ return ADD; }
	YY_BREAK
case 3:
YY_RULE_SETUP
#line 32 "lexer.l"
{ return ADMIN; }
	YY_BREAK
case 4:
YY_RULE_SETUP
#line 33 "lexer.l"
{ return ALL; }
	YY_BREAK
case 5:
YY_RULE_SETUP
#line 34 "lexer.l"
{ return ALLOCATION; }
	YY_BREAK
case 6:
YY_RULE_SETUP
#line 35 "lexer.l"
{ return ALTER; }
	YY_BREAK
case 7:
YY_RULE_SETUP
#line 36 "lexer.l"
{ return AND; }
	YY_BREAK
case 8:
YY_RULE_SETUP
#line 37 "lexer.l"
{ return ARRAY; }
	YY_BREAK
case 9:
YY_RULE_SETUP
#line 38 "lexer.l"
{ return AS; }
	YY_BREAK
case 10:
YY_RULE_SETUP
#line 39 "lexer.l"
{ return ASC; }
	YY_BREAK
case 11:
YY_RULE_SETUP
#line 40 "lexer.l"
{ return BETWEEN; }
	YY_BREAK
case 12:
YY_RULE_SETUP
#line 41 "lexer.l"
{ return BFLOAT16; }
	YY_BREAK
case 13:
YY_RULE_SETUP
#line 42 "lexer.l"
{ return BIGINT; }
	YY_BREAK
case 14:
YY_RULE_SETUP
#line 43 "lexer.l"
{ return BIT; }
	YY_BREAK
case 15:
YY_RULE_SETUP
#line 44 "lexer.l"
{ return BITMAP; }
	YY_BREAK
case 16:
YY_RULE_SETUP
#line 45 "lexer.l"
{ return BOOLEAN; }
	YY_BREAK
case 17:
YY_RULE_SETUP
#line 46 "lexer.l"
{ return BOX; }
	YY_BREAK
case 18:
YY_RULE_SETUP
#line 47 "lexer.l"
{ return BLOB; }
	YY_BREAK
case 19:
YY_RULE_SETUP
#line 48 "lexer.l"
{ return BLOCK; }
	YY_BREAK
case 20:
YY_RULE_SETUP
#line 49 "lexer.l"
{ return BLOCKS; }
	YY_BREAK
case 21:
YY_RULE_SETUP
#line 50 "lexer.l"
{ return BUFFER; }
	YY_BREAK
case 22:
YY_RULE_SETUP
#line 51 "lexer.l"
{ return BY; }
	YY_BREAK
case 23:
YY_RULE_SETUP
#line 52 "lexer.l"
{ return CASE; }
	YY_BREAK
case 24:
YY_RULE_SETUP
#line 53 "lexer.l"
{ return CAST; }
	YY_BREAK
case 25:
YY_RULE_SETUP
#line 54 "lexer.l"
{ return CATALOG; }
	YY_BREAK
case 26:
YY_RULE_SETUP
#line 55 "lexer.l"
{ return CATALOGS; }
	YY_BREAK
case 27:
YY_RULE_SETUP
#line 56 "lexer.l"
{ return CHECK; }
	YY_BREAK
case 28:
YY_RULE_SETUP
#line 57 "lexer.l"
{ return CHUNK; }
	YY_BREAK
case 29:
YY_RULE_SETUP
#line 58 "lexer.l"
{ return CIRCLE; }
	YY_BREAK
case 30:
YY_RULE_SETUP
#line 59 "lexer.l"
{ return CLEAN; }
	YY_BREAK
case 31:
YY_RULE_SETUP
#line 60 "lexer.l"
{ return DUMP; }
	YY_BREAK
case 32:
YY_RULE_SETUP
#line 61 "lexer.l"
{ return COLLECTION; }
	YY_BREAK
case 33:
YY_RULE_SETUP
#line 62 "lexer.l"
{ return COLUMN; }
	YY_BREAK
case 34:
YY_RULE_SETUP
#line 63 "lexer.l"
{ return COLUMNS; }
	YY_BREAK
case 35:
YY_RULE_SETUP
#line 64 "lexer.l"
{ return COMMENT; }
	YY_BREAK
case 36:
YY_RULE_SETUP
#line 65 "lexer.l"
{ return COMPACT; }
	YY_BREAK
case 37:
YY_RULE_SETUP
#line 66 "lexer.l"
{ return CONFIGS; }
	YY_BREAK
case 38:
YY_RULE_SETUP
#line 67 "lexer.l"
{ return CONFIG; }
	YY_BREAK
case 39:
YY_RULE_SETUP
#line 68 "lexer.l"
{ return CONNECT; }
	YY_BREAK
case 40:
YY_RULE_SETUP
#line 69 "lexer.l"
{ return COPY; }
	YY_BREAK
case 41:
YY_RULE_SETUP
#line 70 "lexer.l"
{ return CREATE; }
	YY_BREAK
case 42:
YY_RULE_SETUP
#line 71 "lexer.l"
{ return CROSS; }
	YY_BREAK
case 43:
YY_RULE_SETUP
#line 72 "lexer.l"
{ return DATA; }
	YY_BREAK
case 44:
YY_RULE_SETUP
#line 73 "lexer.l"
{ return DATABASE; }
	YY_BREAK
case 45:
YY_RULE_SETUP
#line 74 "lexer.l"
{ return DATABASES; }
	YY_BREAK
case 46:
YY_RULE_SETUP
#line 75 "lexer.l"
{ return DATE; }
	YY_BREAK
case 47:
YY_RULE_SETUP
#line 76 "lexer.l"
{ return DATETIME; }
	YY_BREAK
case 48:
YY_RULE_SETUP
#line 77 "lexer.l"
{ return DAY; }
	YY_BREAK
case 49:
YY_RULE_SETUP
#line 78 "lexer.l"
{ return DAYS; }
	YY_BREAK
case 50:
YY_RULE_SETUP
#line 79 "lexer.l"
{ return DISTINCT; }
	YY_BREAK
case 51:
YY_RULE_SETUP
#line 80 "lexer.l"
{ return DOUBLE; }
	YY_BREAK
case 52:
YY_RULE_SETUP
#line 81 "lexer.l"
{ return DECIMAL; }
	YY_BREAK
case 53:
YY_RULE_SETUP
#line 82 "lexer.l"
{ return DEFAULT; }
	YY_BREAK
case 54:
YY_RULE_SETUP
#line 83 "lexer.l"
{ return DELETE; }
	YY_BREAK
case 55:
YY_RULE_SETUP
#line 84 "lexer.l"
{ return DELIMITER; }
	YY_BREAK
case 56:
YY_RULE_SETUP
#line 85 "lexer.l"
{ return DESC; }
	YY_BREAK
case 57:
YY_RULE_SETUP
#line 86 "lexer.l"
{ return DROP; }
	YY_BREAK
case 58:
YY_RULE_SETUP
#line 87 "lexer.l"
{ return ELSE; }
	YY_BREAK
case 59:
YY_RULE_SETUP
#line 88 "lexer.l"
{ return EMBEDDING; }
	YY_BREAK
case 60:
YY_RULE_SETUP
#line 89 "lexer.l"
{ return END; }
	YY_BREAK
case 61:
YY_RULE_SETUP
#line 90 "lexer.l"
{ return EXCEPT; }
	YY_BREAK
case 62:
YY_RULE_SETUP
#line 91 "lexer.l"
{ return EXTRACT; }
	YY_BREAK
case 63:
YY_RULE_SETUP
#line 92 "lexer.l"
{ return EXECUTE; }
	YY_BREAK
case 64:
YY_RULE_SETUP
#line 93 "lexer.l"
{ return EXISTS; }
	YY_BREAK
case 65:
YY_RULE_SETUP
#line 94 "lexer.l"
{ return EXPLAIN; }
	YY_BREAK
case 66:
YY_RULE_SETUP
#line 95 "lexer.l"
{ return EXPORT; }
	YY_BREAK
case 67:
YY_RULE_SETUP
#line 96 "lexer.l"
{ return FALSE; }
	YY_BREAK
case 68:
YY_RULE_SETUP
#line 97 "lexer.l"
{ return FILES; }
	YY_BREAK
case 69:
YY_RULE_SETUP
#line 98 "lexer.l"
{ return FLOAT; }
	YY_BREAK
case 70:
YY_RULE_SETUP
#line 99 "lexer.l"
{ return FLOAT16; }
	YY_BREAK
case 71:
YY_RULE_SETUP
#line 100 "lexer.l"
{ return FLUSH; }
	YY_BREAK
case 72:
YY_RULE_SETUP
#line 101 "lexer.l"
{ return FOLLOWER; }
	YY_BREAK
case 73:
YY_RULE_SETUP
#line 102 "lexer.l"
{ return FORMAT; }
	YY_BREAK
case 74:
YY_RULE_SETUP
#line 103 "lexer.l"
{ return FROM; }
	YY_BREAK
case 75:
YY_RULE_SETUP
#line 104 "lexer.l"
{ return FULL; }
	YY_BREAK
case 76:
YY_RULE_SETUP
#line 105 "lexer.l"
{ return FUSION; }
	YY_BREAK
case 77:
YY_RULE_SETUP
#line 106 "lexer.l"
{ return GLOBAL; }
	YY_BREAK
case 78:
YY_RULE_SETUP
#line 107 "lexer.l"
{ return GROUP; }
	YY_BREAK
case 79:
YY_RULE_SETUP
#line 108 "lexer.l"
{ return HAVING; }
	YY_BREAK
case 80:
YY_RULE_SETUP
#line 109 "lexer.l"
{ return HEADER; }
	YY_BREAK
case 81:
YY_RULE_SETUP
#line 110 "lexer.l"
{ return HIGHLIGHT; }
	YY_BREAK
case 82:
YY_RULE_SETUP
#line 111 "lexer.l"
{ return HISTORY; }
	YY_BREAK
case 83:
YY_RULE_SETUP
#line 112 "lexer.l"
{ return HOUR; }
	YY_BREAK
case 84:
YY_RULE_SETUP
#line 113 "lexer.l"
{ return HOURS; }
	YY_BREAK
case 85:
YY_RULE_SETUP
#line 114 "lexer.l"
{ return HUGEINT; }
	YY_BREAK
case 86:
YY_RULE_SETUP
#line 115 "lexer.l"
{ return IF; }
	YY_BREAK
case 87:
YY_RULE_SETUP
#line 116 "lexer.l"
{ return IGNORE; }
	YY_BREAK
case 88:
YY_RULE_SETUP
#line 117 "lexer.l"
{ return IN; }
	YY_BREAK
case 89:
YY_RULE_SETUP
#line 118 "lexer.l"
{ return INDEX; }
	YY_BREAK
case 90:
YY_RULE_SETUP
#line 119 "lexer.l"
{ return INDEXES; }
	YY_BREAK
case 91:
YY_RULE_SETUP
#line 120 "lexer.l"
{ return INNER; }
	YY_BREAK
case 92:
YY_RULE_SETUP
#line 121 "lexer.l"
{ return INSERT; }
	YY_BREAK
case 93:
YY_RULE_SETUP
#line 122 "lexer.l"
{ return INTEGER; }
	YY_BREAK
case 94:
YY_RULE_SETUP
#line 123 "lexer.l"
{ return INT; }
	YY_BREAK
case 95:
YY_RULE_SETUP
#line 124 "lexer.l"
{ return INTERSECT; }
	YY_BREAK
case 96:
YY_RULE_SETUP
#line 125 "lexer.l"
{ return INTERVAL; }
	YY_BREAK
case 97:
YY_RULE_SETUP
#line 126 "lexer.l"
{ return INTO; }
	YY_BREAK
case 98:
YY_RULE_SETUP
#line 127 "lexer.l"
{ return IS; }
	YY_BREAK
case 99:
YY_RULE_SETUP
#line 128 "lexer.l"
{ return JOIN; }
	YY_BREAK
case 100:
YY_RULE_SETUP
#line 129 "lexer.l"
{ return KEY; }
	YY_BREAK
case 101:
YY_RULE_SETUP
#line 130 "lexer.l"
{ return LEADER; }
	YY_BREAK
case 102:
YY_RULE_SETUP
#line 131 "lexer.l"
{ return LEARNER; }
	YY_BREAK
case 103:
YY_RULE_SETUP
#line 132 "lexer.l"
{ return LEFT; }
	YY_BREAK
case 104:
YY_RULE_SETUP
#line 133 "lexer.l"
{ return LIKE; }
	YY_BREAK
case 105:
YY_RULE_SETUP
#line 134 "lexer.l"
{ return LIMIT; }
	YY_BREAK
case 106:
YY_RULE_SETUP
#line 135 "lexer.l"
{ return LINE; }
	YY_BREAK
case 107:
YY_RULE_SETUP
#line 136 "lexer.l"
{ return LOG; }
	YY_BREAK
case 108:
YY_RULE_SETUP
#line 137 "lexer.l"
{ return LOGS; }
	YY_BREAK
case 109:
YY_RULE_SETUP
#line 138 "lexer.l"
{ return LSEG; }
	YY_BREAK
case 110:
YY_RULE_SETUP
#line 139 "lexer.l"
{ return MATCH; }
	YY_BREAK
case 111:
YY_RULE_SETUP
#line 140 "lexer.l"
{ return MAXSIM; }
	YY_BREAK
case 112:
YY_RULE_SETUP
#line 141 "lexer.l"
{ return MEMORY; }
	YY_BREAK
case 113:
YY_RULE_SETUP
#line 142 "lexer.l"
{ return MEMINDEX; }
	YY_BREAK
case 114:
YY_RULE_SETUP
#line 143 "lexer.l"
{ return MINUTE; }
	YY_BREAK
case 115:
YY_RULE_SETUP
#line 144 "lexer.l"
{ return MINUTES; }
	YY_BREAK
case 116:
YY_RULE_SETUP
#line 145 "lexer.l"
{ return MONTH; }
	YY_BREAK
case 117:
YY_RULE_SETUP
#line 146 "lexer.l"
{ return MONTHS; }
	YY_BREAK
case 118:
YY_RULE_SETUP
#line 147 "lexer.l"
{ return MULTIVECTOR; }
	YY_BREAK
case 119:
YY_RULE_SETUP
#line 148 "lexer.l"
{ return NATURAL; }
	YY_BREAK
case 120:
YY_RULE_SETUP
#line 149 "lexer.l"
{ return NULLABLE; }
	YY_BREAK
case 121:
YY_RULE_SETUP
#line 150 "lexer.l"
{ return NODE; }
	YY_BREAK
case 122:
YY_RULE_SETUP
#line 151 "lexer.l"
{ return NODES; }
	YY_BREAK
case 123:
YY_RULE_SETUP
#line 152 "lexer.l"
{ return NOT; }
	YY_BREAK
case 124:
YY_RULE_SETUP
#line 153 "lexer.l"
{ return OBJECT; }
	YY_BREAK
case 125:
YY_RULE_SETUP
#line 154 "lexer.l"
{ return OBJECTS; }
	YY_BREAK
case 126:
YY_RULE_SETUP
#line 155 "lexer.l"
{ return OFF; }
	YY_BREAK
case 127:
YY_RULE_SETUP
#line 156 "lexer.l"
{ return OFFSET; }
	YY_BREAK
case 128:
YY_RULE_SETUP
#line 157 "lexer.l"
{ return ON; }
	YY_BREAK
case 129:
YY_RULE_SETUP
#line 158 "lexer.l"
{ return OPTIMIZE; }
	YY_BREAK
case 130:
YY_RULE_SETUP
#line 159 "lexer.l"
{ return OR; }
	YY_BREAK
case 131:
YY_RULE_SETUP
#line 160 "lexer.l"
{ return ORDER; }
	YY_BREAK
case 132:
YY_RULE_SETUP
#line 161 "lexer.l"
{ return OUTER; }
	YY_BREAK
case 133:
YY_RULE_SETUP
#line 162 "lexer.l"
{ return PATH; }
	YY_BREAK
case 134:
YY_RULE_SETUP
#line 163 "lexer.l"
{ return PERSISTENCE; }
	YY_BREAK
case 135:
YY_RULE_SETUP
#line 164 "lexer.l"
{ return POINT; }
	YY_BREAK
case 136:
YY_RULE_SETUP
#line 165 "lexer.l"
{ return POLYGON; }
	YY_BREAK
case 137:
YY_RULE_SETUP
#line 166 "lexer.l"
{ return PREPARE; }
	YY_BREAK
case 138:
YY_RULE_SETUP
#line 167 "lexer.l"
{ return PRIMARY; }
	YY_BREAK
case 139:
YY_RULE_SETUP
#line 168 "lexer.l"
{ return PROFILES; }
	YY_BREAK
case 140:
YY_RULE_SETUP
#line 169 "lexer.l"
{ return PROPERTIES; }
	YY_BREAK
case 141:
YY_RULE_SETUP
#line 170 "lexer.l"
{ return QUERIES; }
	YY_BREAK
case 142:
YY_RULE_SETUP
#line 171 "lexer.l"
{ return QUERY; }
	YY_BREAK
case 143:
YY_RULE_SETUP
#line 172 "lexer.l"
{ return REAL; }
	YY_BREAK
case 144:
YY_RULE_SETUP
#line 173 "lexer.l"
{ return RECOVER; }
	YY_BREAK
case 145:
YY_RULE_SETUP
#line 174 "lexer.l"
{ return RESTORE; }
	YY_BREAK
case 146:
YY_RULE_SETUP
#line 175 "lexer.l"
{ return RENAME; }
	YY_BREAK
case 147:
YY_RULE_SETUP
#line 176 "lexer.l"
{ return REMOVE; }
	YY_BREAK
case 148:
YY_RULE_SETUP
#line 177 "lexer.l"
{ return RIGHT; }
	YY_BREAK
case 149:
YY_RULE_SETUP
#line 178 "lexer.l"
{ return ROWLIMIT; }
	YY_BREAK
case 150:
YY_RULE_SETUP
#line 179 "lexer.l"
{ return SEARCH; }
	YY_BREAK
case 151:
YY_RULE_SETUP
#line 180 "lexer.l"
{ return SECOND; }
	YY_BREAK
case 152:
YY_RULE_SETUP
#line 181 "lexer.l"
{ return SECONDS; }
	YY_BREAK
case 153:
YY_RULE_SETUP
#line 182 "lexer.l"
{ return SELECT; }
	YY_BREAK
case 154:
YY_RULE_SETUP
#line 183 "lexer.l"
{ return SESSION; }
	YY_BREAK
case 155:
YY_RULE_SETUP
#line 184 "lexer.l"
{ return SET; }
	YY_BREAK
case 156:
YY_RULE_SETUP
#line 185 "lexer.l"
{ return SEGMENT; }
	YY_BREAK
case 157:
YY_RULE_SETUP
#line 186 "lexer.l"
{ return SEGMENTS; }
	YY_BREAK
case 158:
YY_RULE_SETUP
#line 187 "lexer.l"
{ return SHOW; }
	YY_BREAK
case 159:
YY_RULE_SETUP
#line 188 "lexer.l"
{ return SMALLINT; }
	YY_BREAK
case 160:
YY_RULE_SETUP
#line 189 "lexer.l"
{ return SNAPSHOT; }
	YY_BREAK
case 161:
YY_RULE_SETUP
#line 190 "lexer.l"
{ return SNAPSHOTS; }
	YY_BREAK
case 162:
YY_RULE_SETUP
#line 191 "lexer.l"
{ return SPARSE; }
	YY_BREAK
case 163:
YY_RULE_SETUP
#line 192 "lexer.l"
{ return STANDALONE; }
	YY_BREAK
case 164:
YY_RULE_SETUP
#line 193 "lexer.l"
{ return SYSTEM; }
	YY_BREAK
case 165:
YY_RULE_SETUP
#line 194 "lexer.l"
{ return TABLE; }
	YY_BREAK
case 166:
YY_RULE_SETUP
#line 195 "lexer.l"
{ return TABLES; }
	YY_BREAK
case 167:
YY_RULE_SETUP
#line 196 "lexer.l"
{ return TASKS; }
	YY_BREAK
case 168:
YY_RULE_SETUP
#line 197 "lexer.l"
{ return TENSOR; }
	YY_BREAK
case 169:
YY_RULE_SETUP
#line 198 "lexer.l"
{ return TENSORARRAY; }
	YY_BREAK
case 170:
YY_RULE_SETUP
#line 199 "lexer.l"
{ return TEXT; }
	YY_BREAK
case 171:
YY_RULE_SETUP
#line 200 "lexer.l"
{ return THEN; }
	YY_BREAK
case 172:
YY_RULE_SETUP
#line 201 "lexer.l"
{ return TIME; }
	YY_BREAK
case 173:
YY_RULE_SETUP
#line 202 "lexer.l"
{ return TIMESTAMP; }
	YY_BREAK
case 174:
YY_RULE_SETUP
#line 203 "lexer.l"
{ return TINYINT; }
	YY_BREAK
case 175:
YY_RULE_SETUP
#line 204 "lexer.l"
{ return TO; }
	YY_BREAK
case 176:
YY_RULE_SETUP
#line 205 "lexer.l"
{ return TRANSACTION; }
	YY_BREAK
case 177:
YY_RULE_SETUP
#line 206 "lexer.l"
{ return TRANSACTIONS; }
	YY_BREAK
case 178:
YY_RULE_SETUP
#line 207 "lexer.l"
{ return TRUE; }
	YY_BREAK
case 179:
YY_RULE_SETUP
#line 208 "lexer.l"
{ return TUPLE; }
	YY_BREAK
case 180:
YY_RULE_SETUP
#line 209 "lexer.l"
{ return UNION; }
	YY_BREAK
case 181:
YY_RULE_SETUP
#line 210 "lexer.l"
{ return UNIQUE; }
	YY_BREAK
case 182:
YY_RULE_SETUP
#line 211 "lexer.l"
{ return UNSIGNED; }
	YY_BREAK
case 183:
YY_RULE_SETUP
#line 212 "lexer.l"
{ return USING; }
	YY_BREAK
case 184:
YY_RULE_SETUP
#line 213 "lexer.l"
{ return UPDATE; }
	YY_BREAK
case 185:
YY_RULE_SETUP
#line 214 "lexer.l"
{ return UUID; }
	YY_BREAK
case 186:
YY_RULE_SETUP
#line 215 "lexer.l"
{ return USE; }
	YY_BREAK
case 187:
YY_RULE_SETUP
#line 216 "lexer.l"
{ return VALUES; }
	YY_BREAK
case 188:
YY_RULE_SETUP
#line 217 "lexer.l"
{ return VARIABLE; }
	YY_BREAK
case 189:
YY_RULE_SETUP
#line 218 "lexer.l"
{ return VARIABLES; }
	YY_BREAK
case 190:
YY_RULE_SETUP
#line 219 "lexer.l"
{ return VARCHAR; }
	YY_BREAK
case 191:
YY_RULE_SETUP
#line 220 "lexer.l"
{ return VECTOR; }
	YY_BREAK
case 192:
YY_RULE_SETUP
#line 221 "lexer.l"
{ return VIEW; }
	YY_BREAK
case 193:
YY_RULE_SETUP
#line 222 "lexer.l"
{ return WHEN; }
	YY_BREAK
case 194:
YY_RULE_SETUP
#line 223 "lexer.l"
{ return WHERE; }
	YY_BREAK
case 195:
YY_RULE_SETUP
#line 224 "lexer.l"
{ return WITH; }
	YY_BREAK
case 196:
YY_RULE_SETUP
#line 225 "lexer.l"
{ return YEAR; }
	YY_BREAK
case 197:
YY_RULE_SETUP
#line 226 "lexer.l"
{ return YEARS; }
	YY_BREAK
case 198:
YY_RULE_SETUP
#line 228 "lexer.l"
{ return EQUAL; }
	YY_BREAK
case 199:
YY_RULE_SETUP
#line 229 "lexer.l"
{ return NOT_EQ; }
	YY_BREAK
case 200:
YY_RULE_SETUP
#line 230 "lexer.l"
{ return NOT_EQ; }
	YY_BREAK
case 201:
YY_RULE_SETUP
#line 231 "lexer.l"
{ return LESS_EQ; }
	YY_BREAK
case 202:
YY_RULE_SETUP
#line 232 "lexer.l"
{ return GREATER_EQ; }
	YY_BREAK
case 203:
YY_RULE_SETUP
#line 234 "lexer.l"
{ return yytext[0]; }
	YY_BREAK
case 204:
#line 237 "lexer.l"
case 205:
YY_RULE_SETUP
#line 237 "lexer.l"
{
    yylval->double_value = atof(yytext);
    return DOUBLE_VALUE;
}
	YY_BREAK
case 206:
YY_RULE_SETUP
#line 242 "lexer.l"
{
    errno = 0;
    yylval->long_value = strtoll(yytext, nullptr, 0);
    if (errno) {
        return fprintf(stderr, "[SQL-Lexer-Error] Integer cannot be parsed - is it out of range?");
        return 0;
    }
    return LONG_VALUE;
}
	YY_BREAK
case 207:
YY_RULE_SETUP
#line 252 "lexer.l"
{
    // total length - 2 of quota + 1 null char
    long str_len = strlen(yytext) - 1;
    yylval->str_value = (char*)malloc(str_len);
    memset(yylval->str_value, 0, str_len);
    memcpy(yylval->str_value, (char*)(yytext + 1), str_len - 1);
    return IDENTIFIER;
}
	YY_BREAK
case 208:
YY_RULE_SETUP
#line 261 "lexer.l"
{
    yylval->str_value = strdup(yytext);
    return IDENTIFIER;
}
	YY_BREAK
case 209:
YY_RULE_SETUP
#line 266 "lexer.l"
{ BEGIN SINGLE_QUOTED_STRING; string_buffer.clear(); string_buffer.str(""); }  // Clear strbuf manually, see #170
	YY_BREAK
case 210:
YY_RULE_SETUP
#line 267 "lexer.l"
{ string_buffer << '\''; }
	YY_BREAK
case 211:
/* rule 211 can match eol */
YY_RULE_SETUP
#line 268 "lexer.l"
{ string_buffer << yytext; }
	YY_BREAK
case 212:
YY_RULE_SETUP
#line 269 "lexer.l"
{ BEGIN INITIAL; yylval->str_value = strdup(string_buffer.str().c_str()); return STRING; }
	YY_BREAK
case YY_STATE_EOF(SINGLE_QUOTED_STRING):
#line 270 "lexer.l"
{ fprintf(stderr, "[SQL-Lexer-Error] Unterminated string\n"); return 0; }
	YY_BREAK
case 213:
YY_RULE_SETUP
#line 272 "lexer.l"
{ fprintf(stderr, "[SQL-Lexer-Error] Unknown Character: %c\n", yytext[0]); return 0; }
	YY_BREAK
case 214:
YY_RULE_SETUP
#line 274 "lexer.l"
ECHO;
	YY_BREAK
#line 2924 "lexer.cpp"
case YY_STATE_EOF(INITIAL):
	yyterminate();

	case YY_END_OF_BUFFER:
		{
		/* Amount of text matched not including the EOB char. */
		int yy_amount_of_matched_text = (int) (yy_cp - yyg->yytext_ptr) - 1;

		/* Undo the effects of YY_DO_BEFORE_ACTION. */
		*yy_cp = yyg->yy_hold_char;
		YY_RESTORE_YY_MORE_OFFSET

		if ( YY_CURRENT_BUFFER_LVALUE->yy_buffer_status == YY_BUFFER_NEW )
			{
			/* We're scanning a new file or input source.  It's
			 * possible that this happened because the user
			 * just pointed yyin at a new source and called
			 * yylex().  If so, then we have to assure
			 * consistency between YY_CURRENT_BUFFER and our
			 * globals.  Here is the right place to do so, because
			 * this is the first action (other than possibly a
			 * back-up) that will match for the new input source.
			 */
			yyg->yy_n_chars = YY_CURRENT_BUFFER_LVALUE->yy_n_chars;
/* %if-c-only */
			YY_CURRENT_BUFFER_LVALUE->yy_input_file = yyin;
/* %endif */
/* %if-c++-only */
/* %endif */
			YY_CURRENT_BUFFER_LVALUE->yy_buffer_status = YY_BUFFER_NORMAL;
			}

		/* Note that here we test for yy_c_buf_p "<=" to the position
		 * of the first EOB in the buffer, since yy_c_buf_p will
		 * already have been incremented past the NUL character
		 * (since all states make transitions on EOB to the
		 * end-of-buffer state).  Contrast this with the test
		 * in input().
		 */
		if ( yyg->yy_c_buf_p <= &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[yyg->yy_n_chars] )
			{ /* This was really a NUL. */
			yy_state_type yy_next_state;

			yyg->yy_c_buf_p = yyg->yytext_ptr + yy_amount_of_matched_text;

			yy_current_state = yy_get_previous_state( yyscanner );

			/* Okay, we're now positioned to make the NUL
			 * transition.  We couldn't have
			 * yy_get_previous_state() go ahead and do it
			 * for us because it doesn't know how to deal
			 * with the possibility of jamming (and we don't
			 * want to build jamming into it because then it
			 * will run more slowly).
			 */

			yy_next_state = yy_try_NUL_trans( yy_current_state , yyscanner);

			yy_bp = yyg->yytext_ptr + YY_MORE_ADJ;

			if ( yy_next_state )
				{
				/* Consume the NUL. */
				yy_cp = ++yyg->yy_c_buf_p;
				yy_current_state = yy_next_state;
				goto yy_match;
				}

			else
				{
/* %% [14.0] code to do back-up for compressed tables and set up yy_cp goes here */
				yy_cp = yyg->yy_last_accepting_cpos;
				yy_current_state = yyg->yy_last_accepting_state;
				goto yy_find_action;
				}
			}

		else switch ( yy_get_next_buffer( yyscanner ) )
			{
			case EOB_ACT_END_OF_FILE:
				{
				yyg->yy_did_buffer_switch_on_eof = 0;

				if ( yywrap( yyscanner ) )
					{
					/* Note: because we've taken care in
					 * yy_get_next_buffer() to have set up
					 * yytext, we can now set up
					 * yy_c_buf_p so that if some total
					 * hoser (like flex itself) wants to
					 * call the scanner after we return the
					 * YY_NULL, it'll still work - another
					 * YY_NULL will get returned.
					 */
					yyg->yy_c_buf_p = yyg->yytext_ptr + YY_MORE_ADJ;

					yy_act = YY_STATE_EOF(YY_START);
					goto do_action;
					}

				else
					{
					if ( ! yyg->yy_did_buffer_switch_on_eof )
						YY_NEW_FILE;
					}
				break;
				}

			case EOB_ACT_CONTINUE_SCAN:
				yyg->yy_c_buf_p =
					yyg->yytext_ptr + yy_amount_of_matched_text;

				yy_current_state = yy_get_previous_state( yyscanner );

				yy_cp = yyg->yy_c_buf_p;
				yy_bp = yyg->yytext_ptr + YY_MORE_ADJ;
				goto yy_match;

			case EOB_ACT_LAST_MATCH:
				yyg->yy_c_buf_p =
				&YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[yyg->yy_n_chars];

				yy_current_state = yy_get_previous_state( yyscanner );

				yy_cp = yyg->yy_c_buf_p;
				yy_bp = yyg->yytext_ptr + YY_MORE_ADJ;
				goto yy_find_action;
			}
		break;
		}

	default:
		YY_FATAL_ERROR(
			"fatal flex scanner internal error--no action found" );
	} /* end of action switch */
		} /* end of scanning one token */
	} /* end of user's declarations */
} /* end of yylex */
/* %ok-for-header */

/* %if-c++-only */
/* %not-for-header */
/* %ok-for-header */

/* %endif */

/* yy_get_next_buffer - try to read in a new buffer
 *
 * Returns a code representing an action:
 *	EOB_ACT_LAST_MATCH -
 *	EOB_ACT_CONTINUE_SCAN - continue scanning from current position
 *	EOB_ACT_END_OF_FILE - end of file
 */
/* %if-c-only */
static int yy_get_next_buffer (yyscan_t yyscanner)
/* %endif */
/* %if-c++-only */
/* %endif */
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
	char *dest = YY_CURRENT_BUFFER_LVALUE->yy_ch_buf;
	char *source = yyg->yytext_ptr;
	int number_to_move, i;
	int ret_val;

	if ( yyg->yy_c_buf_p > &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[yyg->yy_n_chars + 1] )
		YY_FATAL_ERROR(
		"fatal flex scanner internal error--end of buffer missed" );

	if ( YY_CURRENT_BUFFER_LVALUE->yy_fill_buffer == 0 )
		{ /* Don't try to fill the buffer, so this is an EOF. */
		if ( yyg->yy_c_buf_p - yyg->yytext_ptr - YY_MORE_ADJ == 1 )
			{
			/* We matched a single character, the EOB, so
			 * treat this as a final EOF.
			 */
			return EOB_ACT_END_OF_FILE;
			}

		else
			{
			/* We matched some text prior to the EOB, first
			 * process it.
			 */
			return EOB_ACT_LAST_MATCH;
			}
		}

	/* Try to read more data. */

	/* First move last chars to start of buffer. */
	number_to_move = (int) (yyg->yy_c_buf_p - yyg->yytext_ptr - 1);

	for ( i = 0; i < number_to_move; ++i )
		*(dest++) = *(source++);

	if ( YY_CURRENT_BUFFER_LVALUE->yy_buffer_status == YY_BUFFER_EOF_PENDING )
		/* don't do the read, it's not guaranteed to return an EOF,
		 * just force an EOF
		 */
		YY_CURRENT_BUFFER_LVALUE->yy_n_chars = yyg->yy_n_chars = 0;

	else
		{
			int num_to_read =
			YY_CURRENT_BUFFER_LVALUE->yy_buf_size - number_to_move - 1;

		while ( num_to_read <= 0 )
			{ /* Not enough room in the buffer - grow it. */

			/* just a shorter name for the current buffer */
			YY_BUFFER_STATE b = YY_CURRENT_BUFFER_LVALUE;

			int yy_c_buf_p_offset =
				(int) (yyg->yy_c_buf_p - b->yy_ch_buf);

			if ( b->yy_is_our_buffer )
				{
				int new_size = b->yy_buf_size * 2;

				if ( new_size <= 0 )
					b->yy_buf_size += b->yy_buf_size / 8;
				else
					b->yy_buf_size *= 2;

				b->yy_ch_buf = (char *)
					/* Include room in for 2 EOB chars. */
					yyrealloc( (void *) b->yy_ch_buf,
							 (yy_size_t) (b->yy_buf_size + 2) , yyscanner );
				}
			else
				/* Can't grow it, we don't own it. */
				b->yy_ch_buf = NULL;

			if ( ! b->yy_ch_buf )
				YY_FATAL_ERROR(
				"fatal error - scanner input buffer overflow" );

			yyg->yy_c_buf_p = &b->yy_ch_buf[yy_c_buf_p_offset];

			num_to_read = YY_CURRENT_BUFFER_LVALUE->yy_buf_size -
						number_to_move - 1;

			}

		if ( num_to_read > YY_READ_BUF_SIZE )
			num_to_read = YY_READ_BUF_SIZE;

		/* Read in more data. */
		YY_INPUT( (&YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[number_to_move]),
			yyg->yy_n_chars, num_to_read );

		YY_CURRENT_BUFFER_LVALUE->yy_n_chars = yyg->yy_n_chars;
		}

	if ( yyg->yy_n_chars == 0 )
		{
		if ( number_to_move == YY_MORE_ADJ )
			{
			ret_val = EOB_ACT_END_OF_FILE;
			yyrestart( yyin  , yyscanner);
			}

		else
			{
			ret_val = EOB_ACT_LAST_MATCH;
			YY_CURRENT_BUFFER_LVALUE->yy_buffer_status =
				YY_BUFFER_EOF_PENDING;
			}
		}

	else
		ret_val = EOB_ACT_CONTINUE_SCAN;

	if ((yyg->yy_n_chars + number_to_move) > YY_CURRENT_BUFFER_LVALUE->yy_buf_size) {
		/* Extend the array by 50%, plus the number we really need. */
		int new_size = yyg->yy_n_chars + number_to_move + (yyg->yy_n_chars >> 1);
		YY_CURRENT_BUFFER_LVALUE->yy_ch_buf = (char *) yyrealloc(
			(void *) YY_CURRENT_BUFFER_LVALUE->yy_ch_buf, (yy_size_t) new_size , yyscanner );
		if ( ! YY_CURRENT_BUFFER_LVALUE->yy_ch_buf )
			YY_FATAL_ERROR( "out of dynamic memory in yy_get_next_buffer()" );
		/* "- 2" to take care of EOB's */
		YY_CURRENT_BUFFER_LVALUE->yy_buf_size = (int) (new_size - 2);
	}

	yyg->yy_n_chars += number_to_move;
	YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[yyg->yy_n_chars] = YY_END_OF_BUFFER_CHAR;
	YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[yyg->yy_n_chars + 1] = YY_END_OF_BUFFER_CHAR;

	yyg->yytext_ptr = &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[0];

	return ret_val;
}

/* yy_get_previous_state - get the state just before the EOB char was reached */

/* %if-c-only */
/* %not-for-header */
    static yy_state_type yy_get_previous_state (yyscan_t yyscanner)
/* %endif */
/* %if-c++-only */
/* %endif */
{
	yy_state_type yy_current_state;
	char *yy_cp;
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;

/* %% [15.0] code to get the start state into yy_current_state goes here */
	yy_current_state = yyg->yy_start;

	for ( yy_cp = yyg->yytext_ptr + YY_MORE_ADJ; yy_cp < yyg->yy_c_buf_p; ++yy_cp )
		{
/* %% [16.0] code to find the next state goes here */
		YY_CHAR yy_c = (*yy_cp ? yy_ec[YY_SC_TO_UI(*yy_cp)] : 1);
		if ( yy_accept[yy_current_state] )
			{
			yyg->yy_last_accepting_state = yy_current_state;
			yyg->yy_last_accepting_cpos = yy_cp;
			}
		while ( yy_chk[yy_base[yy_current_state] + yy_c] != yy_current_state )
			{
			yy_current_state = (int) yy_def[yy_current_state];
			if ( yy_current_state >= 782 )
				yy_c = yy_meta[yy_c];
			}
		yy_current_state = yy_nxt[yy_base[yy_current_state] + yy_c];
		}

	return yy_current_state;
}

/* yy_try_NUL_trans - try to make a transition on the NUL character
 *
 * synopsis
 *	next_state = yy_try_NUL_trans( current_state );
 */
/* %if-c-only */
    static yy_state_type yy_try_NUL_trans  (yy_state_type yy_current_state , yyscan_t yyscanner)
/* %endif */
/* %if-c++-only */
/* %endif */
{
	int yy_is_jam;
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner; /* This var may be unused depending upon options. */
/* %% [17.0] code to find the next state, and perhaps do backing up, goes here */
	char *yy_cp = yyg->yy_c_buf_p;

	YY_CHAR yy_c = 1;
	if ( yy_accept[yy_current_state] )
		{
		yyg->yy_last_accepting_state = yy_current_state;
		yyg->yy_last_accepting_cpos = yy_cp;
		}
	while ( yy_chk[yy_base[yy_current_state] + yy_c] != yy_current_state )
		{
		yy_current_state = (int) yy_def[yy_current_state];
		if ( yy_current_state >= 782 )
			yy_c = yy_meta[yy_c];
		}
	yy_current_state = yy_nxt[yy_base[yy_current_state] + yy_c];
	yy_is_jam = (yy_current_state == 781);

	(void)yyg;
	return yy_is_jam ? 0 : yy_current_state;
}

#ifndef YY_NO_UNPUT
/* %if-c-only */

/* %endif */
#endif

/* %if-c-only */
#ifndef YY_NO_INPUT
#ifdef __cplusplus
    static int yyinput (yyscan_t yyscanner)
#else
    static int input  (yyscan_t yyscanner)
#endif

/* %endif */
/* %if-c++-only */
/* %endif */
{
	int c;
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;

	*yyg->yy_c_buf_p = yyg->yy_hold_char;

	if ( *yyg->yy_c_buf_p == YY_END_OF_BUFFER_CHAR )
		{
		/* yy_c_buf_p now points to the character we want to return.
		 * If this occurs *before* the EOB characters, then it's a
		 * valid NUL; if not, then we've hit the end of the buffer.
		 */
		if ( yyg->yy_c_buf_p < &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[yyg->yy_n_chars] )
			/* This was really a NUL. */
			*yyg->yy_c_buf_p = '\0';

		else
			{ /* need more input */
			int offset = (int) (yyg->yy_c_buf_p - yyg->yytext_ptr);
			++yyg->yy_c_buf_p;

			switch ( yy_get_next_buffer( yyscanner ) )
				{
				case EOB_ACT_LAST_MATCH:
					/* This happens because yy_g_n_b()
					 * sees that we've accumulated a
					 * token and flags that we need to
					 * try matching the token before
					 * proceeding.  But for input(),
					 * there's no matching to consider.
					 * So convert the EOB_ACT_LAST_MATCH
					 * to EOB_ACT_END_OF_FILE.
					 */

					/* Reset buffer status. */
					yyrestart( yyin , yyscanner);

					/*FALLTHROUGH*/

				case EOB_ACT_END_OF_FILE:
					{
					if ( yywrap( yyscanner ) )
						return 0;

					if ( ! yyg->yy_did_buffer_switch_on_eof )
						YY_NEW_FILE;
#ifdef __cplusplus
					return yyinput(yyscanner);
#else
					return input(yyscanner);
#endif
					}

				case EOB_ACT_CONTINUE_SCAN:
					yyg->yy_c_buf_p = yyg->yytext_ptr + offset;
					break;
				}
			}
		}

	c = *(unsigned char *) yyg->yy_c_buf_p;	/* cast for 8-bit char's */
	*yyg->yy_c_buf_p = '\0';	/* preserve yytext */
	yyg->yy_hold_char = *++yyg->yy_c_buf_p;

/* %% [19.0] update BOL and yylineno */

	return c;
}
/* %if-c-only */
#endif	/* ifndef YY_NO_INPUT */
/* %endif */

/** Immediately switch to a different input stream.
 * @param input_file A readable stream.
 * @param yyscanner The scanner object.
 * @note This function does not reset the start condition to @c INITIAL .
 */
/* %if-c-only */
    void yyrestart  (FILE * input_file , yyscan_t yyscanner)
/* %endif */
/* %if-c++-only */
/* %endif */
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;

	if ( ! YY_CURRENT_BUFFER ){
        yyensure_buffer_stack (yyscanner);
		YY_CURRENT_BUFFER_LVALUE =
            yy_create_buffer( yyin, YY_BUF_SIZE , yyscanner);
	}

	yy_init_buffer( YY_CURRENT_BUFFER, input_file , yyscanner);
	yy_load_buffer_state( yyscanner );
}

/* %if-c++-only */
/* %endif */

/** Switch to a different input buffer.
 * @param new_buffer The new input buffer.
 * @param yyscanner The scanner object.
 */
/* %if-c-only */
    void yy_switch_to_buffer  (YY_BUFFER_STATE  new_buffer , yyscan_t yyscanner)
/* %endif */
/* %if-c++-only */
/* %endif */
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;

	/* TODO. We should be able to replace this entire function body
	 * with
	 *		yypop_buffer_state();
	 *		yypush_buffer_state(new_buffer);
     */
	yyensure_buffer_stack (yyscanner);
	if ( YY_CURRENT_BUFFER == new_buffer )
		return;

	if ( YY_CURRENT_BUFFER )
		{
		/* Flush out information for old buffer. */
		*yyg->yy_c_buf_p = yyg->yy_hold_char;
		YY_CURRENT_BUFFER_LVALUE->yy_buf_pos = yyg->yy_c_buf_p;
		YY_CURRENT_BUFFER_LVALUE->yy_n_chars = yyg->yy_n_chars;
		}

	YY_CURRENT_BUFFER_LVALUE = new_buffer;
	yy_load_buffer_state( yyscanner );

	/* We don't actually know whether we did this switch during
	 * EOF (yywrap()) processing, but the only time this flag
	 * is looked at is after yywrap() is called, so it's safe
	 * to go ahead and always set it.
	 */
	yyg->yy_did_buffer_switch_on_eof = 1;
}

/* %if-c-only */
static void yy_load_buffer_state  (yyscan_t yyscanner)
/* %endif */
/* %if-c++-only */
/* %endif */
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
	yyg->yy_n_chars = YY_CURRENT_BUFFER_LVALUE->yy_n_chars;
	yyg->yytext_ptr = yyg->yy_c_buf_p = YY_CURRENT_BUFFER_LVALUE->yy_buf_pos;
/* %if-c-only */
	yyin = YY_CURRENT_BUFFER_LVALUE->yy_input_file;
/* %endif */
/* %if-c++-only */
/* %endif */
	yyg->yy_hold_char = *yyg->yy_c_buf_p;
}

/** Allocate and initialize an input buffer state.
 * @param file A readable stream.
 * @param size The character buffer size in bytes. When in doubt, use @c YY_BUF_SIZE.
 * @param yyscanner The scanner object.
 * @return the allocated buffer state.
 */
/* %if-c-only */
    YY_BUFFER_STATE yy_create_buffer  (FILE * file, int  size , yyscan_t yyscanner)
/* %endif */
/* %if-c++-only */
/* %endif */
{
	YY_BUFFER_STATE b;
    
	b = (YY_BUFFER_STATE) yyalloc( sizeof( struct yy_buffer_state ) , yyscanner );
	if ( ! b )
		YY_FATAL_ERROR( "out of dynamic memory in yy_create_buffer()" );

	b->yy_buf_size = size;

	/* yy_ch_buf has to be 2 characters longer than the size given because
	 * we need to put in 2 end-of-buffer characters.
	 */
	b->yy_ch_buf = (char *) yyalloc( (yy_size_t) (b->yy_buf_size + 2) , yyscanner );
	if ( ! b->yy_ch_buf )
		YY_FATAL_ERROR( "out of dynamic memory in yy_create_buffer()" );

	b->yy_is_our_buffer = 1;

	yy_init_buffer( b, file , yyscanner);

	return b;
}

/* %if-c++-only */
/* %endif */

/** Destroy the buffer.
 * @param b a buffer created with yy_create_buffer()
 * @param yyscanner The scanner object.
 */
/* %if-c-only */
    void yy_delete_buffer (YY_BUFFER_STATE  b , yyscan_t yyscanner)
/* %endif */
/* %if-c++-only */
/* %endif */
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;

	if ( ! b )
		return;

	if ( b == YY_CURRENT_BUFFER ) /* Not sure if we should pop here. */
		YY_CURRENT_BUFFER_LVALUE = (YY_BUFFER_STATE) 0;

	if ( b->yy_is_our_buffer )
		yyfree( (void *) b->yy_ch_buf , yyscanner );

	yyfree( (void *) b , yyscanner );
}

/* Initializes or reinitializes a buffer.
 * This function is sometimes called more than once on the same buffer,
 * such as during a yyrestart() or at EOF.
 */
/* %if-c-only */
    static void yy_init_buffer  (YY_BUFFER_STATE  b, FILE * file , yyscan_t yyscanner)
/* %endif */
/* %if-c++-only */
/* %endif */

{
	int oerrno = errno;
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;

	yy_flush_buffer( b , yyscanner);

/* %if-c-only */
	b->yy_input_file = file;
/* %endif */
/* %if-c++-only */
/* %endif */
	b->yy_fill_buffer = 1;

    /* If b is the current buffer, then yy_init_buffer was _probably_
     * called from yyrestart() or through yy_get_next_buffer.
     * In that case, we don't want to reset the lineno or column.
     */
    if (b != YY_CURRENT_BUFFER){
        b->yy_bs_lineno = 1;
        b->yy_bs_column = 0;
    }

/* %if-c-only */

        b->yy_is_interactive = 0;
    
/* %endif */
/* %if-c++-only */
/* %endif */
	errno = oerrno;
}

/** Discard all buffered characters. On the next scan, YY_INPUT will be called.
 * @param b the buffer state to be flushed, usually @c YY_CURRENT_BUFFER.
 * @param yyscanner The scanner object.
 */
/* %if-c-only */
    void yy_flush_buffer (YY_BUFFER_STATE  b , yyscan_t yyscanner)
/* %endif */
/* %if-c++-only */
/* %endif */
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
	if ( ! b )
		return;

	b->yy_n_chars = 0;

	/* We always need two end-of-buffer characters.  The first causes
	 * a transition to the end-of-buffer state.  The second causes
	 * a jam in that state.
	 */
	b->yy_ch_buf[0] = YY_END_OF_BUFFER_CHAR;
	b->yy_ch_buf[1] = YY_END_OF_BUFFER_CHAR;

	b->yy_buf_pos = &b->yy_ch_buf[0];

	b->yy_at_bol = 1;
	b->yy_buffer_status = YY_BUFFER_NEW;

	if ( b == YY_CURRENT_BUFFER )
		yy_load_buffer_state( yyscanner );
}

/* %if-c-or-c++ */
/** Pushes the new state onto the stack. The new state becomes
 *  the current state. This function will allocate the stack
 *  if necessary.
 *  @param new_buffer The new state.
 *  @param yyscanner The scanner object.
 */
/* %if-c-only */
void yypush_buffer_state (YY_BUFFER_STATE new_buffer , yyscan_t yyscanner)
/* %endif */
/* %if-c++-only */
/* %endif */
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
	if (new_buffer == NULL)
		return;

	yyensure_buffer_stack(yyscanner);

	/* This block is copied from yy_switch_to_buffer. */
	if ( YY_CURRENT_BUFFER )
		{
		/* Flush out information for old buffer. */
		*yyg->yy_c_buf_p = yyg->yy_hold_char;
		YY_CURRENT_BUFFER_LVALUE->yy_buf_pos = yyg->yy_c_buf_p;
		YY_CURRENT_BUFFER_LVALUE->yy_n_chars = yyg->yy_n_chars;
		}

	/* Only push if top exists. Otherwise, replace top. */
	if (YY_CURRENT_BUFFER)
		yyg->yy_buffer_stack_top++;
	YY_CURRENT_BUFFER_LVALUE = new_buffer;

	/* copied from yy_switch_to_buffer. */
	yy_load_buffer_state( yyscanner );
	yyg->yy_did_buffer_switch_on_eof = 1;
}
/* %endif */

/* %if-c-or-c++ */
/** Removes and deletes the top of the stack, if present.
 *  The next element becomes the new top.
 *  @param yyscanner The scanner object.
 */
/* %if-c-only */
void yypop_buffer_state (yyscan_t yyscanner)
/* %endif */
/* %if-c++-only */
/* %endif */
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
	if (!YY_CURRENT_BUFFER)
		return;

	yy_delete_buffer(YY_CURRENT_BUFFER , yyscanner);
	YY_CURRENT_BUFFER_LVALUE = NULL;
	if (yyg->yy_buffer_stack_top > 0)
		--yyg->yy_buffer_stack_top;

	if (YY_CURRENT_BUFFER) {
		yy_load_buffer_state( yyscanner );
		yyg->yy_did_buffer_switch_on_eof = 1;
	}
}
/* %endif */

/* %if-c-or-c++ */
/* Allocates the stack if it does not exist.
 *  Guarantees space for at least one push.
 */
/* %if-c-only */
static void yyensure_buffer_stack (yyscan_t yyscanner)
/* %endif */
/* %if-c++-only */
/* %endif */
{
	yy_size_t num_to_alloc;
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;

	if (!yyg->yy_buffer_stack) {

		/* First allocation is just for 2 elements, since we don't know if this
		 * scanner will even need a stack. We use 2 instead of 1 to avoid an
		 * immediate realloc on the next call.
         */
      num_to_alloc = 1; /* After all that talk, this was set to 1 anyways... */
		yyg->yy_buffer_stack = (struct yy_buffer_state**)yyalloc
								(num_to_alloc * sizeof(struct yy_buffer_state*)
								, yyscanner);
		if ( ! yyg->yy_buffer_stack )
			YY_FATAL_ERROR( "out of dynamic memory in yyensure_buffer_stack()" );

		memset(yyg->yy_buffer_stack, 0, num_to_alloc * sizeof(struct yy_buffer_state*));

		yyg->yy_buffer_stack_max = num_to_alloc;
		yyg->yy_buffer_stack_top = 0;
		return;
	}

	if (yyg->yy_buffer_stack_top >= (yyg->yy_buffer_stack_max) - 1){

		/* Increase the buffer to prepare for a possible push. */
		yy_size_t grow_size = 8 /* arbitrary grow size */;

		num_to_alloc = yyg->yy_buffer_stack_max + grow_size;
		yyg->yy_buffer_stack = (struct yy_buffer_state**)yyrealloc
								(yyg->yy_buffer_stack,
								num_to_alloc * sizeof(struct yy_buffer_state*)
								, yyscanner);
		if ( ! yyg->yy_buffer_stack )
			YY_FATAL_ERROR( "out of dynamic memory in yyensure_buffer_stack()" );

		/* zero only the new slots.*/
		memset(yyg->yy_buffer_stack + yyg->yy_buffer_stack_max, 0, grow_size * sizeof(struct yy_buffer_state*));
		yyg->yy_buffer_stack_max = num_to_alloc;
	}
}
/* %endif */

/* %if-c-only */
/** Setup the input buffer state to scan directly from a user-specified character buffer.
 * @param base the character buffer
 * @param size the size in bytes of the character buffer
 * @param yyscanner The scanner object.
 * @return the newly allocated buffer state object.
 */
YY_BUFFER_STATE yy_scan_buffer  (char * base, yy_size_t  size , yyscan_t yyscanner)
{
	YY_BUFFER_STATE b;
    
	if ( size < 2 ||
	     base[size-2] != YY_END_OF_BUFFER_CHAR ||
	     base[size-1] != YY_END_OF_BUFFER_CHAR )
		/* They forgot to leave room for the EOB's. */
		return NULL;

	b = (YY_BUFFER_STATE) yyalloc( sizeof( struct yy_buffer_state ) , yyscanner );
	if ( ! b )
		YY_FATAL_ERROR( "out of dynamic memory in yy_scan_buffer()" );

	b->yy_buf_size = (int) (size - 2);	/* "- 2" to take care of EOB's */
	b->yy_buf_pos = b->yy_ch_buf = base;
	b->yy_is_our_buffer = 0;
	b->yy_input_file = NULL;
	b->yy_n_chars = b->yy_buf_size;
	b->yy_is_interactive = 0;
	b->yy_at_bol = 1;
	b->yy_fill_buffer = 0;
	b->yy_buffer_status = YY_BUFFER_NEW;

	yy_switch_to_buffer( b , yyscanner );

	return b;
}
/* %endif */

/* %if-c-only */
/** Setup the input buffer state to scan a string. The next call to yylex() will
 * scan from a @e copy of @a str.
 * @param yystr a NUL-terminated string to scan
 * @param yyscanner The scanner object.
 * @return the newly allocated buffer state object.
 * @note If you want to scan bytes that may contain NUL values, then use
 *       yy_scan_bytes() instead.
 */
YY_BUFFER_STATE yy_scan_string (const char * yystr , yyscan_t yyscanner)
{
    
	return yy_scan_bytes( yystr, (int) strlen(yystr) , yyscanner);
}
/* %endif */

/* %if-c-only */
/** Setup the input buffer state to scan the given bytes. The next call to yylex() will
 * scan from a @e copy of @a bytes.
 * @param yybytes the byte buffer to scan
 * @param _yybytes_len the number of bytes in the buffer pointed to by @a bytes.
 * @param yyscanner The scanner object.
 * @return the newly allocated buffer state object.
 */
YY_BUFFER_STATE yy_scan_bytes  (const char * yybytes, int  _yybytes_len , yyscan_t yyscanner)
{
	YY_BUFFER_STATE b;
	char *buf;
	yy_size_t n;
	int i;
    
	/* Get memory for full buffer, including space for trailing EOB's. */
	n = (yy_size_t) (_yybytes_len + 2);
	buf = (char *) yyalloc( n , yyscanner );
	if ( ! buf )
		YY_FATAL_ERROR( "out of dynamic memory in yy_scan_bytes()" );

	for ( i = 0; i < _yybytes_len; ++i )
		buf[i] = yybytes[i];

	buf[_yybytes_len] = buf[_yybytes_len+1] = YY_END_OF_BUFFER_CHAR;

	b = yy_scan_buffer( buf, n , yyscanner);
	if ( ! b )
		YY_FATAL_ERROR( "bad buffer in yy_scan_bytes()" );

	/* It's okay to grow etc. this buffer, and we should throw it
	 * away when we're done.
	 */
	b->yy_is_our_buffer = 1;

	return b;
}
/* %endif */

#ifndef YY_EXIT_FAILURE
#define YY_EXIT_FAILURE 2
#endif

/* %if-c-only */
static void yynoreturn yy_fatal_error (const char* msg , yyscan_t yyscanner)
{
	struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
	(void)yyg;
	fprintf( stderr, "%s\n", msg );
	exit( YY_EXIT_FAILURE );
}
/* %endif */
/* %if-c++-only */
/* %endif */

/* Redefine yyless() so it works in section 3 code. */

#undef yyless
#define yyless(n) \
	do \
		{ \
		/* Undo effects of setting up yytext. */ \
        int yyless_macro_arg = (n); \
        YY_LESS_LINENO(yyless_macro_arg);\
		yytext[yyleng] = yyg->yy_hold_char; \
		yyg->yy_c_buf_p = yytext + yyless_macro_arg; \
		yyg->yy_hold_char = *yyg->yy_c_buf_p; \
		*yyg->yy_c_buf_p = '\0'; \
		yyleng = yyless_macro_arg; \
		} \
	while ( 0 )

/* Accessor  methods (get/set functions) to struct members. */

/* %if-c-only */
/* %if-reentrant */

/** Get the user-defined data for this scanner.
 * @param yyscanner The scanner object.
 */
YY_EXTRA_TYPE yyget_extra  (yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    return yyextra;
}

/* %endif */

/** Get the current line number.
 * @param yyscanner The scanner object.
 */
int yyget_lineno  (yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;

        if (! YY_CURRENT_BUFFER)
            return 0;
    
    return yylineno;
}

/** Get the current column number.
 * @param yyscanner The scanner object.
 */
int yyget_column  (yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;

        if (! YY_CURRENT_BUFFER)
            return 0;
    
    return yycolumn;
}

/** Get the input stream.
 * @param yyscanner The scanner object.
 */
FILE *yyget_in  (yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    return yyin;
}

/** Get the output stream.
 * @param yyscanner The scanner object.
 */
FILE *yyget_out  (yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    return yyout;
}

/** Get the length of the current token.
 * @param yyscanner The scanner object.
 */
int yyget_leng  (yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    return yyleng;
}

/** Get the current token.
 * @param yyscanner The scanner object.
 */

char *yyget_text  (yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    return yytext;
}

/* %if-reentrant */

/** Set the user-defined data. This data is never touched by the scanner.
 * @param user_defined The data to be associated with this scanner.
 * @param yyscanner The scanner object.
 */
void yyset_extra (YY_EXTRA_TYPE  user_defined , yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    yyextra = user_defined ;
}

/* %endif */

/** Set the current line number.
 * @param _line_number line number
 * @param yyscanner The scanner object.
 */
void yyset_lineno (int  _line_number , yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;

        /* lineno is only valid if an input buffer exists. */
        if (! YY_CURRENT_BUFFER )
           YY_FATAL_ERROR( "yyset_lineno called with no buffer" );
    
    yylineno = _line_number;
}

/** Set the current column.
 * @param _column_no column number
 * @param yyscanner The scanner object.
 */
void yyset_column (int  _column_no , yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;

        /* column is only valid if an input buffer exists. */
        if (! YY_CURRENT_BUFFER )
           YY_FATAL_ERROR( "yyset_column called with no buffer" );
    
    yycolumn = _column_no;
}

/** Set the input stream. This does not discard the current
 * input buffer.
 * @param _in_str A readable stream.
 * @param yyscanner The scanner object.
 * @see yy_switch_to_buffer
 */
void yyset_in (FILE *  _in_str , yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    yyin = _in_str ;
}

void yyset_out (FILE *  _out_str , yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    yyout = _out_str ;
}

int yyget_debug  (yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    return yy_flex_debug;
}

void yyset_debug (int  _bdebug , yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    yy_flex_debug = _bdebug ;
}

/* %endif */

/* %if-reentrant */
/* Accessor methods for yylval and yylloc */

/* %if-bison-bridge */

YYSTYPE * yyget_lval  (yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    return yylval;
}

void yyset_lval (YYSTYPE *  yylval_param , yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    yylval = yylval_param;
}

YYLTYPE *yyget_lloc  (yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    return yylloc;
}
    
void yyset_lloc (YYLTYPE *  yylloc_param , yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    yylloc = yylloc_param;
}
    
/* %endif */

/* User-visible API */

/* yylex_init is special because it creates the scanner itself, so it is
 * the ONLY reentrant function that doesn't take the scanner as the last argument.
 * That's why we explicitly handle the declaration, instead of using our macros.
 */
int yylex_init(yyscan_t* ptr_yy_globals)
{
    if (ptr_yy_globals == NULL){
        errno = EINVAL;
        return 1;
    }

    *ptr_yy_globals = (yyscan_t) yyalloc ( sizeof( struct yyguts_t ), NULL );

    if (*ptr_yy_globals == NULL){
        errno = ENOMEM;
        return 1;
    }

    /* By setting to 0xAA, we expose bugs in yy_init_globals. Leave at 0x00 for releases. */
    memset(*ptr_yy_globals,0x00,sizeof(struct yyguts_t));

    return yy_init_globals ( *ptr_yy_globals );
}

/* yylex_init_extra has the same functionality as yylex_init, but follows the
 * convention of taking the scanner as the last argument. Note however, that
 * this is a *pointer* to a scanner, as it will be allocated by this call (and
 * is the reason, too, why this function also must handle its own declaration).
 * The user defined value in the first argument will be available to yyalloc in
 * the yyextra field.
 */
int yylex_init_extra( YY_EXTRA_TYPE yy_user_defined, yyscan_t* ptr_yy_globals )
{
    struct yyguts_t dummy_yyguts;

    yyset_extra (yy_user_defined, &dummy_yyguts);

    if (ptr_yy_globals == NULL){
        errno = EINVAL;
        return 1;
    }

    *ptr_yy_globals = (yyscan_t) yyalloc ( sizeof( struct yyguts_t ), &dummy_yyguts );

    if (*ptr_yy_globals == NULL){
        errno = ENOMEM;
        return 1;
    }

    /* By setting to 0xAA, we expose bugs in
    yy_init_globals. Leave at 0x00 for releases. */
    memset(*ptr_yy_globals,0x00,sizeof(struct yyguts_t));

    yyset_extra (yy_user_defined, *ptr_yy_globals);

    return yy_init_globals ( *ptr_yy_globals );
}

/* %endif if-c-only */

/* %if-c-only */
static int yy_init_globals (yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
    /* Initialization is the same as for the non-reentrant scanner.
     * This function is called from yylex_destroy(), so don't allocate here.
     */

    yyg->yy_buffer_stack = NULL;
    yyg->yy_buffer_stack_top = 0;
    yyg->yy_buffer_stack_max = 0;
    yyg->yy_c_buf_p = NULL;
    yyg->yy_init = 0;
    yyg->yy_start = 0;

    yyg->yy_start_stack_ptr = 0;
    yyg->yy_start_stack_depth = 0;
    yyg->yy_start_stack =  NULL;

/* Defined in main.c */
#ifdef YY_STDINIT
    yyin = stdin;
    yyout = stdout;
#else
    yyin = NULL;
    yyout = NULL;
#endif

    /* For future reference: Set errno on error, since we are called by
     * yylex_init()
     */
    return 0;
}
/* %endif */

/* %if-c-only SNIP! this currently causes conflicts with the c++ scanner */
/* yylex_destroy is for both reentrant and non-reentrant scanners. */
int yylex_destroy  (yyscan_t yyscanner)
{
    struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;

    /* Pop the buffer stack, destroying each element. */
	while(YY_CURRENT_BUFFER){
		yy_delete_buffer( YY_CURRENT_BUFFER , yyscanner );
		YY_CURRENT_BUFFER_LVALUE = NULL;
		yypop_buffer_state(yyscanner);
	}

	/* Destroy the stack itself. */
	yyfree(yyg->yy_buffer_stack , yyscanner);
	yyg->yy_buffer_stack = NULL;

    /* Destroy the start condition stack. */
        yyfree( yyg->yy_start_stack , yyscanner );
        yyg->yy_start_stack = NULL;

    /* Reset the globals. This is important in a non-reentrant scanner so the next time
     * yylex() is called, initialization will occur. */
    yy_init_globals( yyscanner);

/* %if-reentrant */
    /* Destroy the main struct (reentrant only). */
    yyfree ( yyscanner , yyscanner );
    yyscanner = NULL;
/* %endif */
    return 0;
}
/* %endif */

/*
 * Internal utility routines.
 */

#ifndef yytext_ptr
static void yy_flex_strncpy (char* s1, const char * s2, int n , yyscan_t yyscanner)
{
	struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
	(void)yyg;

	int i;
	for ( i = 0; i < n; ++i )
		s1[i] = s2[i];
}
#endif

#ifdef YY_NEED_STRLEN
static int yy_flex_strlen (const char * s , yyscan_t yyscanner)
{
	int n;
	for ( n = 0; s[n]; ++n )
		;

	return n;
}
#endif

void *yyalloc (yy_size_t  size , yyscan_t yyscanner)
{
	struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
	(void)yyg;
	return malloc(size);
}

void *yyrealloc  (void * ptr, yy_size_t  size , yyscan_t yyscanner)
{
	struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
	(void)yyg;

	/* The cast to (char *) in the following accommodates both
	 * implementations that use char* generic pointers, and those
	 * that use void* generic pointers.  It works with the latter
	 * because both ANSI C and C++ allow castless assignment from
	 * any pointer type to void*, and deal with argument conversions
	 * as though doing an assignment.
	 */
	return realloc(ptr, size);
}

void yyfree (void * ptr , yyscan_t yyscanner)
{
	struct yyguts_t * yyg = (struct yyguts_t*)yyscanner;
	(void)yyg;
	free( (char *) ptr );	/* see yyrealloc() for (char *) cast */
}

/* %if-tables-serialization definitions */
/* %define-yytables   The name for this specific scanner's tables. */
#define YYTABLES_NAME "yytables"
/* %endif */

/* %ok-for-header */

#line 274 "lexer.l"


int yyerror(const char *msg) {
    fprintf(stderr, "[Why here?] %s\n",msg); return 0;
}

