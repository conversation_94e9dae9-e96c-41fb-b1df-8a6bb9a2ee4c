#line 2 "search_lexer.cpp"

#line 4 "search_lexer.cpp"

#define  YY_INT_ALIGNED short int

/* A lexical scanner generated by flex */

/* %not-for-header */
/* %if-c-only */
/* %if-not-reentrant */
/* %endif */
/* %endif */
/* %ok-for-header */

#define FLEX_SCANNER
#define YY_FLEX_MAJOR_VERSION 2
#define YY_FLEX_MINOR_VERSION 6
#define YY_FLEX_SUBMINOR_VERSION 4
#if YY_FLEX_SUBMINOR_VERSION > 0
#define FLEX_BETA
#endif

/* %if-c++-only */
    /* The c++ scanner is a mess. The FlexLexer.h header file relies on the
     * following macro. This is required in order to pass the c++-multiple-scanners
     * test in the regression suite. We get reports that it breaks inheritance.
     * We will address this in a future release of flex, or omit the C++ scanner
     * altogether.
     */
    #define yyFlexLexer SearchScannerInfinitySyntaxFlexLexer
/* %endif */

/* %if-c-only */
/* %endif */

#ifdef yyalloc
#define SearchScannerInfinitySyntaxalloc_ALREADY_DEFINED
#else
#define yyalloc SearchScannerInfinitySyntaxalloc
#endif

#ifdef yyrealloc
#define SearchScannerInfinitySyntaxrealloc_ALREADY_DEFINED
#else
#define yyrealloc SearchScannerInfinitySyntaxrealloc
#endif

#ifdef yyfree
#define SearchScannerInfinitySyntaxfree_ALREADY_DEFINED
#else
#define yyfree SearchScannerInfinitySyntaxfree
#endif

/* %if-c-only */
/* %endif */

/* First, we deal with  platform-specific or compiler-specific issues. */

/* begin standard C headers. */
/* %if-c-only */
/* %endif */

/* %if-tables-serialization */
/* %endif */
/* end standard C headers. */

/* %if-c-or-c++ */
/* flex integer type definitions */

#ifndef FLEXINT_H
#define FLEXINT_H

/* C99 systems have <inttypes.h>. Non-C99 systems may or may not. */

#if defined (__STDC_VERSION__) && __STDC_VERSION__ >= 199901L

/* C99 says to define __STDC_LIMIT_MACROS before including stdint.h,
 * if you want the limit (max/min) macros for int types. 
 */
#ifndef __STDC_LIMIT_MACROS
#define __STDC_LIMIT_MACROS 1
#endif

#include <inttypes.h>
typedef int8_t flex_int8_t;
typedef uint8_t flex_uint8_t;
typedef int16_t flex_int16_t;
typedef uint16_t flex_uint16_t;
typedef int32_t flex_int32_t;
typedef uint32_t flex_uint32_t;
#else
typedef signed char flex_int8_t;
typedef short int flex_int16_t;
typedef int flex_int32_t;
typedef unsigned char flex_uint8_t; 
typedef unsigned short int flex_uint16_t;
typedef unsigned int flex_uint32_t;

/* Limits of integral types. */
#ifndef INT8_MIN
#define INT8_MIN               (-128)
#endif
#ifndef INT16_MIN
#define INT16_MIN              (-32767-1)
#endif
#ifndef INT32_MIN
#define INT32_MIN              (-2147483647-1)
#endif
#ifndef INT8_MAX
#define INT8_MAX               (127)
#endif
#ifndef INT16_MAX
#define INT16_MAX              (32767)
#endif
#ifndef INT32_MAX
#define INT32_MAX              (2147483647)
#endif
#ifndef UINT8_MAX
#define UINT8_MAX              (255U)
#endif
#ifndef UINT16_MAX
#define UINT16_MAX             (65535U)
#endif
#ifndef UINT32_MAX
#define UINT32_MAX             (4294967295U)
#endif

#ifndef SIZE_MAX
#define SIZE_MAX               (~(size_t)0)
#endif

#endif /* ! C99 */

#endif /* ! FLEXINT_H */

/* %endif */

/* begin standard C++ headers. */
/* %if-c++-only */
#include <iostream>
#include <errno.h>
#include <cstdlib>
#include <cstdio>
#include <cstring>
/* end standard C++ headers. */
/* %endif */

/* TODO: this is always defined, so inline it */
#define yyconst const

#if defined(__GNUC__) && __GNUC__ >= 3
#define yynoreturn __attribute__((__noreturn__))
#else
#define yynoreturn
#endif

/* %not-for-header */
/* Returned upon end-of-file. */
#define YY_NULL 0
/* %ok-for-header */

/* %not-for-header */
/* Promotes a possibly negative, possibly signed char to an
 *   integer in range [0..255] for use as an array index.
 */
#define YY_SC_TO_UI(c) ((YY_CHAR) (c))
/* %ok-for-header */

/* %if-reentrant */
/* %endif */

/* %if-not-reentrant */

/* %endif */

/* Enter a start condition.  This macro really ought to take a parameter,
 * but we do it the disgusting crufty way forced on us by the ()-less
 * definition of BEGIN.
 */
#define BEGIN (yy_start) = 1 + 2 *
/* Translate the current start state into a value that can be later handed
 * to BEGIN to return to the state.  The YYSTATE alias is for lex
 * compatibility.
 */
#define YY_START (((yy_start) - 1) / 2)
#define YYSTATE YY_START
/* Action number for EOF rule of a given start state. */
#define YY_STATE_EOF(state) (YY_END_OF_BUFFER + state + 1)
/* Special action meaning "start processing a new file". */
#define YY_NEW_FILE yyrestart( yyin  )
#define YY_END_OF_BUFFER_CHAR 0

/* Size of default input buffer. */
#ifndef YY_BUF_SIZE
#ifdef __ia64__
/* On IA-64, the buffer size is 16k, not 8k.
 * Moreover, YY_BUF_SIZE is 2*YY_READ_BUF_SIZE in the general case.
 * Ditto for the __ia64__ case accordingly.
 */
#define YY_BUF_SIZE 32768
#else
#define YY_BUF_SIZE 16384
#endif /* __ia64__ */
#endif

/* The state buf must be large enough to hold one state per character in the main buffer.
 */
#define YY_STATE_BUF_SIZE   ((YY_BUF_SIZE + 2) * sizeof(yy_state_type))

#ifndef YY_TYPEDEF_YY_BUFFER_STATE
#define YY_TYPEDEF_YY_BUFFER_STATE
typedef struct yy_buffer_state *YY_BUFFER_STATE;
#endif

#ifndef YY_TYPEDEF_YY_SIZE_T
#define YY_TYPEDEF_YY_SIZE_T
typedef size_t yy_size_t;
#endif

/* %if-not-reentrant */
extern int yyleng;
/* %endif */

/* %if-c-only */
/* %if-not-reentrant */
/* %endif */
/* %endif */

#define EOB_ACT_CONTINUE_SCAN 0
#define EOB_ACT_END_OF_FILE 1
#define EOB_ACT_LAST_MATCH 2
    
    #define YY_LESS_LINENO(n)
    #define YY_LINENO_REWIND_TO(ptr)
    
/* Return all but the first "n" matched characters back to the input stream. */
#define yyless(n) \
	do \
		{ \
		/* Undo effects of setting up yytext. */ \
        int yyless_macro_arg = (n); \
        YY_LESS_LINENO(yyless_macro_arg);\
		*yy_cp = (yy_hold_char); \
		YY_RESTORE_YY_MORE_OFFSET \
		(yy_c_buf_p) = yy_cp = yy_bp + yyless_macro_arg - YY_MORE_ADJ; \
		YY_DO_BEFORE_ACTION; /* set up yytext again */ \
		} \
	while ( 0 )
#define unput(c) yyunput( c, (yytext_ptr)  )

#ifndef YY_STRUCT_YY_BUFFER_STATE
#define YY_STRUCT_YY_BUFFER_STATE
struct yy_buffer_state
	{
/* %if-c-only */
/* %endif */

/* %if-c++-only */
	std::streambuf* yy_input_file;
/* %endif */

	char *yy_ch_buf;		/* input buffer */
	char *yy_buf_pos;		/* current position in input buffer */

	/* Size of input buffer in bytes, not including room for EOB
	 * characters.
	 */
	int yy_buf_size;

	/* Number of characters read into yy_ch_buf, not including EOB
	 * characters.
	 */
	int yy_n_chars;

	/* Whether we "own" the buffer - i.e., we know we created it,
	 * and can realloc() it to grow it, and should free() it to
	 * delete it.
	 */
	int yy_is_our_buffer;

	/* Whether this is an "interactive" input source; if so, and
	 * if we're using stdio for input, then we want to use getc()
	 * instead of fread(), to make sure we stop fetching input after
	 * each newline.
	 */
	int yy_is_interactive;

	/* Whether we're considered to be at the beginning of a line.
	 * If so, '^' rules will be active on the next match, otherwise
	 * not.
	 */
	int yy_at_bol;

    int yy_bs_lineno; /**< The line count. */
    int yy_bs_column; /**< The column count. */

	/* Whether to try to fill the input buffer when we reach the
	 * end of it.
	 */
	int yy_fill_buffer;

	int yy_buffer_status;

#define YY_BUFFER_NEW 0
#define YY_BUFFER_NORMAL 1
	/* When an EOF's been seen but there's still some text to process
	 * then we mark the buffer as YY_EOF_PENDING, to indicate that we
	 * shouldn't try reading from the input source any more.  We might
	 * still have a bunch of tokens to match, though, because of
	 * possible backing-up.
	 *
	 * When we actually see the EOF, we change the status to "new"
	 * (via yyrestart()), so that the user can continue scanning by
	 * just pointing yyin at a new input file.
	 */
#define YY_BUFFER_EOF_PENDING 2

	};
#endif /* !YY_STRUCT_YY_BUFFER_STATE */

/* %if-c-only Standard (non-C++) definition */
/* %not-for-header */
/* %if-not-reentrant */
/* %endif */
/* %ok-for-header */

/* %endif */

/* We provide macros for accessing buffer states in case in the
 * future we want to put the buffer states in a more general
 * "scanner state".
 *
 * Returns the top of the stack, or NULL.
 */
#define YY_CURRENT_BUFFER ( (yy_buffer_stack) \
                          ? (yy_buffer_stack)[(yy_buffer_stack_top)] \
                          : NULL)
/* Same as previous macro, but useful when we know that the buffer stack is not
 * NULL or when we need an lvalue. For internal use only.
 */
#define YY_CURRENT_BUFFER_LVALUE (yy_buffer_stack)[(yy_buffer_stack_top)]

/* %if-c-only Standard (non-C++) definition */
/* %if-not-reentrant */
/* %not-for-header */
/* %ok-for-header */

/* %endif */
/* %endif */

void *yyalloc ( yy_size_t  );
void *yyrealloc ( void *, yy_size_t  );
void yyfree ( void *  );

#define yy_new_buffer yy_create_buffer
#define yy_set_interactive(is_interactive) \
	{ \
	if ( ! YY_CURRENT_BUFFER ){ \
        yyensure_buffer_stack (); \
		YY_CURRENT_BUFFER_LVALUE =    \
            yy_create_buffer( yyin, YY_BUF_SIZE ); \
	} \
	YY_CURRENT_BUFFER_LVALUE->yy_is_interactive = is_interactive; \
	}
#define yy_set_bol(at_bol) \
	{ \
	if ( ! YY_CURRENT_BUFFER ){\
        yyensure_buffer_stack (); \
		YY_CURRENT_BUFFER_LVALUE =    \
            yy_create_buffer( yyin, YY_BUF_SIZE ); \
	} \
	YY_CURRENT_BUFFER_LVALUE->yy_at_bol = at_bol; \
	}
#define YY_AT_BOL() (YY_CURRENT_BUFFER_LVALUE->yy_at_bol)

/* %% [1.0] yytext/yyin/yyout/yy_state_type/yylineno etc. def's & init go here */
/* Begin user sect3 */
#define YY_SKIP_YYWRAP

#define FLEX_DEBUG
typedef flex_uint8_t YY_CHAR;

#define yytext_ptr yytext

#include <FlexLexer.h>

int yyFlexLexer::yywrap() { return 1; }
int yyFlexLexer::yylex()
	{
	LexerError( "yyFlexLexer::yylex invoked but %option yyclass used" );
	return 0;
	}

#define YY_DECL int infinity::SearchScannerInfinitySyntax::yylex()

/* %% [1.5] DFA */

/* %if-c-only Standard (non-C++) definition */
/* %endif */

/* Done after the current pattern has been matched and before the
 * corresponding action - sets up yytext.
 */
#define YY_DO_BEFORE_ACTION \
	(yytext_ptr) = yy_bp; \
/* %% [2.0] code to fiddle yytext and yyleng for yymore() goes here \ */\
	yyleng = (int) (yy_cp - yy_bp); \
	(yy_hold_char) = *yy_cp; \
	*yy_cp = '\0'; \
/* %% [3.0] code to copy yytext_ptr to yytext[] goes here, if %array \ */\
	(yy_c_buf_p) = yy_cp;
/* %% [4.0] data tables for the DFA and the user's section 1 definitions go here */
#define YY_NUM_RULES 20
#define YY_END_OF_BUFFER 21
/* This struct is not used in this scanner,
   but its presence is necessary. */
struct yy_trans_info
	{
	flex_int32_t yy_verify;
	flex_int32_t yy_nxt;
	};
static const flex_int16_t yy_accept[56] =
    {   0,
        0,    0,    0,    0,    0,    0,   21,   20,    1,   11,
       16,   12,    5,    6,    7,   11,   11,   11,   20,   20,
       20,   20,   20,   20,   13,   15,   14,   17,   19,   18,
        1,   11,    0,    0,    0,    0,   11,   11,    3,    0,
        8,   10,    0,    0,   13,   14,   17,   18,    2,    4,
        9,    8,    0,    8,    0
    } ;

static const YY_CHAR yy_ec[256] =
    {   0,
        1,    1,    1,    1,    1,    1,    1,    1,    2,    2,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    3,    4,    5,    4,    4,    4,    4,    6,    7,
        8,    9,    4,    4,    4,   10,    4,   11,   11,   11,
       11,   11,   11,   11,   11,   11,   11,   12,    4,    4,
        4,    4,    9,    4,   13,    4,    4,   14,    4,    4,
        4,    4,    4,    4,    4,    4,    4,   15,   16,    4,
        4,   17,    4,   18,    4,    4,    4,    4,    4,    4,
        4,   19,    4,   20,    4,    4,    4,    4,    4,    4,

        4,    4,    4,    4,    4,    4,    4,    4,    4,    4,
        4,    4,    4,    4,    4,    4,    4,    4,    4,    4,
        4,    4,    4,    4,    4,   21,    1,   22,   22,   22,
       22,   22,   22,   22,   22,   22,   22,   22,   22,   22,
       22,   22,   22,   22,   22,   22,   22,   22,   22,   22,
       22,   22,   22,   22,   22,   22,   22,   22,   22,   22,
       22,   22,   22,   22,   22,   22,   22,   22,   22,   22,
       22,   22,   22,   22,   22,   22,   22,   22,   22,   22,
       22,   22,   22,   22,   22,   22,   22,   22,   22,   22,
       22,    1,    1,   23,   23,   23,   23,   23,   23,   23,

       23,   23,   23,   23,   23,   23,   23,   23,   23,   23,
       23,   23,   23,   23,   23,   23,   23,   23,   23,   23,
       23,   23,   23,   24,   24,   24,   24,   24,   24,   24,
       24,   24,   24,   24,   24,   24,   24,   24,   24,   25,
       25,   25,   25,   25,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1
    } ;

static const YY_CHAR yy_meta[26] =
    {   0,
        1,    1,    2,    3,    4,    5,    2,    2,    2,    3,
        3,    2,    3,    3,    3,    3,    3,    3,    6,    2,
        2,    1,    3,    3,    3
    } ;

static const flex_int16_t yy_base[64] =
    {   0,
        0,    0,   20,   21,   23,   24,  116,  117,   28,   25,
      117,  117,  117,  117,  117,   36,   17,   19,    0,   27,
      104,   92,   91,   90,    0,  117,    0,    0,  117,    0,
       43,   96,    0,   88,   87,   86,   38,   39,   92,   95,
       52,   87,   59,   56,    0,  117,    0,  117,   62,   43,
       45,   36,   19,   24,  117,   63,   69,   73,   78,   84,
       88,   94,   99
    } ;

static const flex_int16_t yy_def[64] =
    {   0,
       55,    1,   56,   56,   57,   57,   55,   55,   55,   58,
       55,   55,   55,   55,   55,   58,   16,   16,   59,   55,
       55,   55,   55,   55,   60,   55,   61,   62,   55,   63,
       55,   16,   59,   55,   55,   55,   16,   16,   16,   55,
       55,   55,   55,   55,   60,   55,   62,   55,   16,   16,
       55,   55,   55,   55,    0,   55,   55,   55,   55,   55,
       55,   55,   55
    } ;

static const flex_int16_t yy_nxt[143] =
    {   0,
        8,    9,    9,   10,   11,   12,   13,   14,    8,   10,
       10,   15,   16,   10,   17,   18,   10,   10,   19,   20,
       21,    8,   22,   23,   24,   26,   26,   29,   29,   31,
       31,   32,   38,   32,   54,   39,   40,   41,   27,   27,
       32,   30,   30,   33,   31,   31,   54,   34,   35,   36,
       37,   49,   32,   32,   33,   51,   50,   32,   34,   35,
       36,   52,   41,   25,   25,   25,   25,   25,   25,   28,
       28,   28,   28,   28,   28,   32,   32,   53,   32,   32,
       32,   32,   32,   32,   45,   45,   45,   45,   46,   46,
       46,   46,   46,   46,   47,   47,   47,   42,   47,   48,

       48,   48,   48,   48,   48,   51,   32,   44,   43,   32,
       32,   44,   43,   32,   42,   55,    7,   55,   55,   55,
       55,   55,   55,   55,   55,   55,   55,   55,   55,   55,
       55,   55,   55,   55,   55,   55,   55,   55,   55,   55,
       55,   55
    } ;

static const flex_int16_t yy_chk[143] =
    {   0,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    3,    4,    5,    6,    9,
        9,   17,   17,   18,   54,   18,   20,   20,    3,    4,
       53,    5,    6,   10,   31,   31,   52,   10,   10,   10,
       16,   37,   37,   38,   16,   51,   38,   50,   16,   16,
       16,   41,   41,   56,   56,   56,   56,   56,   56,   57,
       57,   57,   57,   57,   57,   58,   49,   44,   58,   59,
       43,   59,   59,   59,   60,   60,   60,   60,   61,   61,
       61,   61,   61,   61,   62,   62,   62,   42,   62,   63,

       63,   63,   63,   63,   63,   40,   39,   36,   35,   34,
       32,   24,   23,   22,   21,    7,   55,   55,   55,   55,
       55,   55,   55,   55,   55,   55,   55,   55,   55,   55,
       55,   55,   55,   55,   55,   55,   55,   55,   55,   55,
       55,   55
    } ;

static const flex_int16_t yy_rule_linenum[20] =
    {   0,
       65,   67,   69,   71,   73,   75,   77,   79,   80,   82,
       84,   86,   87,   88,   89,   92,   93,   94,   95
    } ;

/* The intent behind this definition is that it'll catch
 * any uses of REJECT which flex missed.
 */
#define REJECT reject_used_but_not_detected
#define yymore() yymore_used_but_not_detected
#define YY_MORE_ADJ 0
#define YY_RESTORE_YY_MORE_OFFSET
#line 1 "search_lexer.l"
#line 2 "search_lexer.l"
#include <string>
#include <sstream>
#include <iostream>
#include <cstdlib>

/* Implementation of yyFlexScanner */
#define SearchScannerDerived SearchScannerInfinitySyntax
#include "search_scanner_derived.h"
#undef SearchScannerDerived
#undef  YY_DECL
#define YY_DECL int infinity::SearchScannerInfinitySyntax::yylex(infinity::SearchParser::semantic_type * const lval, infinity::SearchParser::location_type *loc)

/* typedef to make the returns for the tokens shorter */
using token = infinity::SearchParser::token;

/* define yyterminate as this instead of NULL */
#define yyterminate() return( token::END )

/* msvc2010 requires that we exclude this header file. */
#define YY_NO_UNISTD_H

/* update location on matching */
#define YY_USER_ACTION loc->step(); loc->columns(yyleng);

/* for temporary storage of quoted string */
static thread_local std::stringstream string_buffer;

#line 577 "search_lexer.cpp"
#define YY_NO_INPUT 1

#line 580 "search_lexer.cpp"

#define INITIAL 0
#define SINGLE_QUOTED_STRING 1
#define DOUBLE_QUOTED_STRING 2

#ifndef YY_NO_UNISTD_H
/* Special case for "unistd.h", since it is non-ANSI. We include it way
 * down here because we want the user's section 1 to have been scanned first.
 * The user has a chance to override it with an option.
 */
/* %if-c-only */
/* %endif */
/* %if-c++-only */
#include <unistd.h>
/* %endif */
#endif

#ifndef YY_EXTRA_TYPE
#define YY_EXTRA_TYPE void *
#endif

/* %if-c-only Reentrant structure and macros (non-C++). */
/* %if-reentrant */
/* %if-c-only */
/* %endif */
/* %if-reentrant */
/* %endif */
/* %endif End reentrant structures and macros. */
/* %if-bison-bridge */
/* %endif */
/* %not-for-header */
/* %ok-for-header */

/* %endif */

#ifndef yytext_ptr
static void yy_flex_strncpy ( char *, const char *, int );
#endif

#ifdef YY_NEED_STRLEN
static int yy_flex_strlen ( const char * );
#endif

#ifndef YY_NO_INPUT
/* %if-c-only Standard (non-C++) definition */
/* %not-for-header */
/* %ok-for-header */

/* %endif */
#endif

/* %if-c-only */
/* %endif */

/* Amount of stuff to slurp up with each read. */
#ifndef YY_READ_BUF_SIZE
#ifdef __ia64__
/* On IA-64, the buffer size is 16k, not 8k */
#define YY_READ_BUF_SIZE 16384
#else
#define YY_READ_BUF_SIZE 8192
#endif /* __ia64__ */
#endif

/* Copy whatever the last rule matched to the standard output. */
#ifndef ECHO
/* %if-c-only Standard (non-C++) definition */
/* %endif */
/* %if-c++-only C++ definition */
#define ECHO LexerOutput( yytext, yyleng )
/* %endif */
#endif

/* Gets input and stuffs it into "buf".  number of characters read, or YY_NULL,
 * is returned in "result".
 */
#ifndef YY_INPUT
#define YY_INPUT(buf,result,max_size) \
/* %% [5.0] fread()/read() definition of YY_INPUT goes here unless we're doing C++ \ */\
\
/* %if-c++-only C++ definition \ */\
	if ( (int)(result = LexerInput( (char *) buf, max_size )) < 0 ) \
		YY_FATAL_ERROR( "input in flex scanner failed" );
/* %endif */

#endif

/* No semi-colon after return; correct usage is to write "yyterminate();" -
 * we don't want an extra ';' after the "return" because that will cause
 * some compilers to complain about unreachable statements.
 */
#ifndef yyterminate
#define yyterminate() return YY_NULL
#endif

/* Number of entries by which start-condition stack grows. */
#ifndef YY_START_STACK_INCR
#define YY_START_STACK_INCR 25
#endif

/* Report a fatal error. */
#ifndef YY_FATAL_ERROR
/* %if-c-only */
/* %endif */
/* %if-c++-only */
#define YY_FATAL_ERROR(msg) LexerError( msg )
/* %endif */
#endif

/* %if-tables-serialization structures and prototypes */
/* %not-for-header */
/* %ok-for-header */

/* %not-for-header */
/* %tables-yydmap generated elements */
/* %endif */
/* end tables serialization structures and prototypes */

/* %ok-for-header */

/* Default declaration of generated scanner - a define so the user can
 * easily add parameters.
 */
#ifndef YY_DECL
#define YY_DECL_IS_OURS 1
/* %if-c-only Standard (non-C++) definition */
/* %endif */
/* %if-c++-only C++ definition */
#define YY_DECL int yyFlexLexer::yylex()
/* %endif */
#endif /* !YY_DECL */

/* Code executed at the beginning of each rule, after yytext and yyleng
 * have been set up.
 */
#ifndef YY_USER_ACTION
#define YY_USER_ACTION
#endif

/* Code executed at the end of each rule. */
#ifndef YY_BREAK
#define YY_BREAK /*LINTED*/break;
#endif

/* %% [6.0] YY_RULE_SETUP definition goes here */
#define YY_RULE_SETUP \
	YY_USER_ACTION

/* %not-for-header */
/** The main scanner function which does all the work.
 */
YY_DECL
{
	yy_state_type yy_current_state;
	char *yy_cp, *yy_bp;
	int yy_act;
    
	if ( !(yy_init) )
		{
		(yy_init) = 1;

#ifdef YY_USER_INIT
		YY_USER_INIT;
#endif

		if ( ! (yy_start) )
			(yy_start) = 1;	/* first start state */

		if ( ! yyin )
/* %if-c-only */
/* %endif */
/* %if-c++-only */
			yyin.rdbuf(std::cin.rdbuf());
/* %endif */

		if ( ! yyout )
/* %if-c-only */
/* %endif */
/* %if-c++-only */
			yyout.rdbuf(std::cout.rdbuf());
/* %endif */

		if ( ! YY_CURRENT_BUFFER ) {
			yyensure_buffer_stack ();
			YY_CURRENT_BUFFER_LVALUE =
				yy_create_buffer( yyin, YY_BUF_SIZE );
		}

		yy_load_buffer_state(  );
		}

	{
/* %% [7.0] user's declarations go here */
#line 57 "search_lexer.l"

          /** Code executed at the beginning of yylex **/
#line 60 "search_lexer.l"
            yylval = lval;

            /* Note: special characters in pattern shall be double-quoted or escaped with backslash: ' ()^"~*?:\\' */


#line 783 "search_lexer.cpp"

	while ( /*CONSTCOND*/1 )		/* loops until end-of-file is reached */
		{
/* %% [8.0] yymore()-related code goes here */
		yy_cp = (yy_c_buf_p);

		/* Support of yytext. */
		*yy_cp = (yy_hold_char);

		/* yy_bp points to the position in yy_ch_buf of the start of
		 * the current run.
		 */
		yy_bp = yy_cp;

/* %% [9.0] code to set up and find next match goes here */
		yy_current_state = (yy_start);
yy_match:
		do
			{
			YY_CHAR yy_c = yy_ec[YY_SC_TO_UI(*yy_cp)] ;
			if ( yy_accept[yy_current_state] )
				{
				(yy_last_accepting_state) = yy_current_state;
				(yy_last_accepting_cpos) = yy_cp;
				}
			while ( yy_chk[yy_base[yy_current_state] + yy_c] != yy_current_state )
				{
				yy_current_state = (int) yy_def[yy_current_state];
				if ( yy_current_state >= 56 )
					yy_c = yy_meta[yy_c];
				}
			yy_current_state = yy_nxt[yy_base[yy_current_state] + yy_c];
			++yy_cp;
			}
		while ( yy_current_state != 55 );
		yy_cp = (yy_last_accepting_cpos);
		yy_current_state = (yy_last_accepting_state);

yy_find_action:
/* %% [10.0] code to find the action number goes here */
		yy_act = yy_accept[yy_current_state];

		YY_DO_BEFORE_ACTION;

/* %% [11.0] code for yylineno update goes here */

do_action:	/* This label is used only to access EOF actions. */

/* %% [12.0] debug code goes here */
		if ( yy_flex_debug )
			{
			if ( yy_act == 0 )
				std::cerr << "--scanner backing up\n";
			else if ( yy_act < 20 )
				std::cerr << "--accepting rule at line " << yy_rule_linenum[yy_act] <<
				         "(\"" << yytext << "\")\n";
			else if ( yy_act == 20 )
				std::cerr << "--accepting default rule (\"" << yytext << "\")\n";
			else if ( yy_act == 21 )
				std::cerr << "--(end of buffer or a NUL)\n";
			else
				std::cerr << "--EOF (start condition " << YY_START << ")\n";
			}

		switch ( yy_act )
	{ /* beginning of action switch */
/* %% [13.0] actions go here */
			case 0: /* must back up */
			/* undo the effects of YY_DO_BEFORE_ACTION */
			*yy_cp = (yy_hold_char);
			yy_cp = (yy_last_accepting_cpos);
			yy_current_state = (yy_last_accepting_state);
			goto yy_find_action;

case 1:
/* rule 1 can match eol */
YY_RULE_SETUP
#line 65 "search_lexer.l"
/* ignore \t\n and space */;
	YY_BREAK
case 2:
YY_RULE_SETUP
#line 67 "search_lexer.l"
{ return token::AND; }
	YY_BREAK
case 3:
YY_RULE_SETUP
#line 69 "search_lexer.l"
{ return token::OR; }
	YY_BREAK
case 4:
YY_RULE_SETUP
#line 71 "search_lexer.l"
{ return token::NOT; }
	YY_BREAK
case 5:
YY_RULE_SETUP
#line 73 "search_lexer.l"
{ return token::LPAREN; }
	YY_BREAK
case 6:
YY_RULE_SETUP
#line 75 "search_lexer.l"
{ return token::RPAREN; }
	YY_BREAK
case 7:
YY_RULE_SETUP
#line 77 "search_lexer.l"
{ return token::OP_COLON; }
	YY_BREAK
case 8:
#line 80 "search_lexer.l"
case 9:
YY_RULE_SETUP
#line 80 "search_lexer.l"
{ yylval->build(std::strtof(yytext+1, NULL)); return token::CARAT; }
	YY_BREAK
case 10:
YY_RULE_SETUP
#line 82 "search_lexer.l"
{ yylval->build(std::strtoul(yytext+1, NULL, 10)); return token::TILDE; }
	YY_BREAK
case 11:
YY_RULE_SETUP
#line 84 "search_lexer.l"
{ yylval->build<InfString>(InfString(yytext, false)); return token::STRING; }  // https://stackoverflow.com/questions/9611682/flexlexer-support-for-unicode
	YY_BREAK
case 12:
YY_RULE_SETUP
#line 86 "search_lexer.l"
{ BEGIN SINGLE_QUOTED_STRING; string_buffer.clear(); string_buffer.str(""); }  // Clear strbuf manually, see #170
	YY_BREAK
case 13:
/* rule 13 can match eol */
YY_RULE_SETUP
#line 87 "search_lexer.l"
{ string_buffer << yytext; }
	YY_BREAK
case 14:
/* rule 14 can match eol */
YY_RULE_SETUP
#line 88 "search_lexer.l"
{ string_buffer << yytext; }
	YY_BREAK
case 15:
YY_RULE_SETUP
#line 89 "search_lexer.l"
{ BEGIN INITIAL; yylval->build<InfString>(InfString(std::move(string_buffer).str(), true)); return token::STRING; }
	YY_BREAK
case YY_STATE_EOF(SINGLE_QUOTED_STRING):
#line 90 "search_lexer.l"
{ std::cerr << "[Lucene-Lexer-Error] Unterminated string" << std::endl; return 0; }
	YY_BREAK
case 16:
YY_RULE_SETUP
#line 92 "search_lexer.l"
{ BEGIN DOUBLE_QUOTED_STRING; string_buffer.clear(); string_buffer.str(""); }  // Clear strbuf manually, see #170
	YY_BREAK
case 17:
/* rule 17 can match eol */
YY_RULE_SETUP
#line 93 "search_lexer.l"
{ string_buffer << yytext; }
	YY_BREAK
case 18:
/* rule 18 can match eol */
YY_RULE_SETUP
#line 94 "search_lexer.l"
{ string_buffer << yytext; }
	YY_BREAK
case 19:
YY_RULE_SETUP
#line 95 "search_lexer.l"
{ BEGIN INITIAL; yylval->build<InfString>(InfString(std::move(string_buffer).str(), true)); return token::STRING; }
	YY_BREAK
case YY_STATE_EOF(DOUBLE_QUOTED_STRING):
#line 96 "search_lexer.l"
{ std::cerr << "[Lucene-Lexer-Error] Unterminated string" << std::endl; return 0; }
	YY_BREAK
case 20:
YY_RULE_SETUP
#line 98 "search_lexer.l"
ECHO;
	YY_BREAK
#line 968 "search_lexer.cpp"
case YY_STATE_EOF(INITIAL):
	yyterminate();

	case YY_END_OF_BUFFER:
		{
		/* Amount of text matched not including the EOB char. */
		int yy_amount_of_matched_text = (int) (yy_cp - (yytext_ptr)) - 1;

		/* Undo the effects of YY_DO_BEFORE_ACTION. */
		*yy_cp = (yy_hold_char);
		YY_RESTORE_YY_MORE_OFFSET

		if ( YY_CURRENT_BUFFER_LVALUE->yy_buffer_status == YY_BUFFER_NEW )
			{
			/* We're scanning a new file or input source.  It's
			 * possible that this happened because the user
			 * just pointed yyin at a new source and called
			 * yylex().  If so, then we have to assure
			 * consistency between YY_CURRENT_BUFFER and our
			 * globals.  Here is the right place to do so, because
			 * this is the first action (other than possibly a
			 * back-up) that will match for the new input source.
			 */
			(yy_n_chars) = YY_CURRENT_BUFFER_LVALUE->yy_n_chars;
/* %if-c-only */
/* %endif */
/* %if-c++-only */
			YY_CURRENT_BUFFER_LVALUE->yy_input_file = yyin.rdbuf();
/* %endif */
			YY_CURRENT_BUFFER_LVALUE->yy_buffer_status = YY_BUFFER_NORMAL;
			}

		/* Note that here we test for yy_c_buf_p "<=" to the position
		 * of the first EOB in the buffer, since yy_c_buf_p will
		 * already have been incremented past the NUL character
		 * (since all states make transitions on EOB to the
		 * end-of-buffer state).  Contrast this with the test
		 * in input().
		 */
		if ( (yy_c_buf_p) <= &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars)] )
			{ /* This was really a NUL. */
			yy_state_type yy_next_state;

			(yy_c_buf_p) = (yytext_ptr) + yy_amount_of_matched_text;

			yy_current_state = yy_get_previous_state(  );

			/* Okay, we're now positioned to make the NUL
			 * transition.  We couldn't have
			 * yy_get_previous_state() go ahead and do it
			 * for us because it doesn't know how to deal
			 * with the possibility of jamming (and we don't
			 * want to build jamming into it because then it
			 * will run more slowly).
			 */

			yy_next_state = yy_try_NUL_trans( yy_current_state );

			yy_bp = (yytext_ptr) + YY_MORE_ADJ;

			if ( yy_next_state )
				{
				/* Consume the NUL. */
				yy_cp = ++(yy_c_buf_p);
				yy_current_state = yy_next_state;
				goto yy_match;
				}

			else
				{
/* %% [14.0] code to do back-up for compressed tables and set up yy_cp goes here */
				yy_cp = (yy_last_accepting_cpos);
				yy_current_state = (yy_last_accepting_state);
				goto yy_find_action;
				}
			}

		else switch ( yy_get_next_buffer(  ) )
			{
			case EOB_ACT_END_OF_FILE:
				{
				(yy_did_buffer_switch_on_eof) = 0;

				if ( yywrap(  ) )
					{
					/* Note: because we've taken care in
					 * yy_get_next_buffer() to have set up
					 * yytext, we can now set up
					 * yy_c_buf_p so that if some total
					 * hoser (like flex itself) wants to
					 * call the scanner after we return the
					 * YY_NULL, it'll still work - another
					 * YY_NULL will get returned.
					 */
					(yy_c_buf_p) = (yytext_ptr) + YY_MORE_ADJ;

					yy_act = YY_STATE_EOF(YY_START);
					goto do_action;
					}

				else
					{
					if ( ! (yy_did_buffer_switch_on_eof) )
						YY_NEW_FILE;
					}
				break;
				}

			case EOB_ACT_CONTINUE_SCAN:
				(yy_c_buf_p) =
					(yytext_ptr) + yy_amount_of_matched_text;

				yy_current_state = yy_get_previous_state(  );

				yy_cp = (yy_c_buf_p);
				yy_bp = (yytext_ptr) + YY_MORE_ADJ;
				goto yy_match;

			case EOB_ACT_LAST_MATCH:
				(yy_c_buf_p) =
				&YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars)];

				yy_current_state = yy_get_previous_state(  );

				yy_cp = (yy_c_buf_p);
				yy_bp = (yytext_ptr) + YY_MORE_ADJ;
				goto yy_find_action;
			}
		break;
		}

	default:
		YY_FATAL_ERROR(
			"fatal flex scanner internal error--no action found" );
	} /* end of action switch */
		} /* end of scanning one token */
	} /* end of user's declarations */
} /* end of yylex */
/* %ok-for-header */

/* %if-c++-only */
/* %not-for-header */
/* The contents of this function are C++ specific, so the () macro is not used.
 * This constructor simply maintains backward compatibility.
 * DEPRECATED
 */
yyFlexLexer::yyFlexLexer( std::istream* arg_yyin, std::ostream* arg_yyout ):
	yyin(arg_yyin ? arg_yyin->rdbuf() : std::cin.rdbuf()),
	yyout(arg_yyout ? arg_yyout->rdbuf() : std::cout.rdbuf())
{
	ctor_common();
}

/* The contents of this function are C++ specific, so the () macro is not used.
 */
yyFlexLexer::yyFlexLexer( std::istream& arg_yyin, std::ostream& arg_yyout ):
	yyin(arg_yyin.rdbuf()),
	yyout(arg_yyout.rdbuf())
{
	ctor_common();
}

/* The contents of this function are C++ specific, so the () macro is not used.
 */
void yyFlexLexer::ctor_common()
{
	yy_c_buf_p = 0;
	yy_init = 0;
	yy_start = 0;
	yy_flex_debug = 0;
	yylineno = 1;	// this will only get updated if %option yylineno

	yy_did_buffer_switch_on_eof = 0;

	yy_looking_for_trail_begin = 0;
	yy_more_flag = 0;
	yy_more_len = 0;
	yy_more_offset = yy_prev_more_offset = 0;

	yy_start_stack_ptr = yy_start_stack_depth = 0;
	yy_start_stack = NULL;

	yy_buffer_stack = NULL;
	yy_buffer_stack_top = 0;
	yy_buffer_stack_max = 0;

	yy_state_buf = 0;

}

/* The contents of this function are C++ specific, so the () macro is not used.
 */
yyFlexLexer::~yyFlexLexer()
{
	delete [] yy_state_buf;
	yyfree( yy_start_stack  );
	yy_delete_buffer( YY_CURRENT_BUFFER );
	yyfree( yy_buffer_stack  );
}

/* The contents of this function are C++ specific, so the () macro is not used.
 */
void yyFlexLexer::switch_streams( std::istream& new_in, std::ostream& new_out )
{
	// was if( new_in )
	yy_delete_buffer( YY_CURRENT_BUFFER );
	yy_switch_to_buffer( yy_create_buffer( new_in, YY_BUF_SIZE  ) );

	// was if( new_out )
	yyout.rdbuf(new_out.rdbuf());
}

/* The contents of this function are C++ specific, so the () macro is not used.
 */
void yyFlexLexer::switch_streams( std::istream* new_in, std::ostream* new_out )
{
	if( ! new_in ) {
		new_in = &yyin;
	}

	if ( ! new_out ) {
		new_out = &yyout;
	}

	switch_streams(*new_in, *new_out);
}

#ifdef YY_INTERACTIVE
int yyFlexLexer::LexerInput( char* buf, int /* max_size */ )
#else
int yyFlexLexer::LexerInput( char* buf, int max_size )
#endif
{
	if ( yyin.eof() || yyin.fail() )
		return 0;

#ifdef YY_INTERACTIVE
	yyin.get( buf[0] );

	if ( yyin.eof() )
		return 0;

	if ( yyin.bad() )
		return -1;

	return 1;

#else
	(void) yyin.read( buf, max_size );

	if ( yyin.bad() )
		return -1;
	else
		return yyin.gcount();
#endif
}

void yyFlexLexer::LexerOutput( const char* buf, int size )
{
	(void) yyout.write( buf, size );
}
/* %ok-for-header */

/* %endif */

/* yy_get_next_buffer - try to read in a new buffer
 *
 * Returns a code representing an action:
 *	EOB_ACT_LAST_MATCH -
 *	EOB_ACT_CONTINUE_SCAN - continue scanning from current position
 *	EOB_ACT_END_OF_FILE - end of file
 */
/* %if-c-only */
/* %endif */
/* %if-c++-only */
int yyFlexLexer::yy_get_next_buffer()
/* %endif */
{
    	char *dest = YY_CURRENT_BUFFER_LVALUE->yy_ch_buf;
	char *source = (yytext_ptr);
	int number_to_move, i;
	int ret_val;

	if ( (yy_c_buf_p) > &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars) + 1] )
		YY_FATAL_ERROR(
		"fatal flex scanner internal error--end of buffer missed" );

	if ( YY_CURRENT_BUFFER_LVALUE->yy_fill_buffer == 0 )
		{ /* Don't try to fill the buffer, so this is an EOF. */
		if ( (yy_c_buf_p) - (yytext_ptr) - YY_MORE_ADJ == 1 )
			{
			/* We matched a single character, the EOB, so
			 * treat this as a final EOF.
			 */
			return EOB_ACT_END_OF_FILE;
			}

		else
			{
			/* We matched some text prior to the EOB, first
			 * process it.
			 */
			return EOB_ACT_LAST_MATCH;
			}
		}

	/* Try to read more data. */

	/* First move last chars to start of buffer. */
	number_to_move = (int) ((yy_c_buf_p) - (yytext_ptr) - 1);

	for ( i = 0; i < number_to_move; ++i )
		*(dest++) = *(source++);

	if ( YY_CURRENT_BUFFER_LVALUE->yy_buffer_status == YY_BUFFER_EOF_PENDING )
		/* don't do the read, it's not guaranteed to return an EOF,
		 * just force an EOF
		 */
		YY_CURRENT_BUFFER_LVALUE->yy_n_chars = (yy_n_chars) = 0;

	else
		{
			int num_to_read =
			YY_CURRENT_BUFFER_LVALUE->yy_buf_size - number_to_move - 1;

		while ( num_to_read <= 0 )
			{ /* Not enough room in the buffer - grow it. */

			/* just a shorter name for the current buffer */
			YY_BUFFER_STATE b = YY_CURRENT_BUFFER_LVALUE;

			int yy_c_buf_p_offset =
				(int) ((yy_c_buf_p) - b->yy_ch_buf);

			if ( b->yy_is_our_buffer )
				{
				int new_size = b->yy_buf_size * 2;

				if ( new_size <= 0 )
					b->yy_buf_size += b->yy_buf_size / 8;
				else
					b->yy_buf_size *= 2;

				b->yy_ch_buf = (char *)
					/* Include room in for 2 EOB chars. */
					yyrealloc( (void *) b->yy_ch_buf,
							 (yy_size_t) (b->yy_buf_size + 2)  );
				}
			else
				/* Can't grow it, we don't own it. */
				b->yy_ch_buf = NULL;

			if ( ! b->yy_ch_buf )
				YY_FATAL_ERROR(
				"fatal error - scanner input buffer overflow" );

			(yy_c_buf_p) = &b->yy_ch_buf[yy_c_buf_p_offset];

			num_to_read = YY_CURRENT_BUFFER_LVALUE->yy_buf_size -
						number_to_move - 1;

			}

		if ( num_to_read > YY_READ_BUF_SIZE )
			num_to_read = YY_READ_BUF_SIZE;

		/* Read in more data. */
		YY_INPUT( (&YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[number_to_move]),
			(yy_n_chars), num_to_read );

		YY_CURRENT_BUFFER_LVALUE->yy_n_chars = (yy_n_chars);
		}

	if ( (yy_n_chars) == 0 )
		{
		if ( number_to_move == YY_MORE_ADJ )
			{
			ret_val = EOB_ACT_END_OF_FILE;
			yyrestart( yyin  );
			}

		else
			{
			ret_val = EOB_ACT_LAST_MATCH;
			YY_CURRENT_BUFFER_LVALUE->yy_buffer_status =
				YY_BUFFER_EOF_PENDING;
			}
		}

	else
		ret_val = EOB_ACT_CONTINUE_SCAN;

	if (((yy_n_chars) + number_to_move) > YY_CURRENT_BUFFER_LVALUE->yy_buf_size) {
		/* Extend the array by 50%, plus the number we really need. */
		int new_size = (yy_n_chars) + number_to_move + ((yy_n_chars) >> 1);
		YY_CURRENT_BUFFER_LVALUE->yy_ch_buf = (char *) yyrealloc(
			(void *) YY_CURRENT_BUFFER_LVALUE->yy_ch_buf, (yy_size_t) new_size  );
		if ( ! YY_CURRENT_BUFFER_LVALUE->yy_ch_buf )
			YY_FATAL_ERROR( "out of dynamic memory in yy_get_next_buffer()" );
		/* "- 2" to take care of EOB's */
		YY_CURRENT_BUFFER_LVALUE->yy_buf_size = (int) (new_size - 2);
	}

	(yy_n_chars) += number_to_move;
	YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars)] = YY_END_OF_BUFFER_CHAR;
	YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars) + 1] = YY_END_OF_BUFFER_CHAR;

	(yytext_ptr) = &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[0];

	return ret_val;
}

/* yy_get_previous_state - get the state just before the EOB char was reached */

/* %if-c-only */
/* %not-for-header */
/* %endif */
/* %if-c++-only */
    yy_state_type yyFlexLexer::yy_get_previous_state()
/* %endif */
{
	yy_state_type yy_current_state;
	char *yy_cp;
    
/* %% [15.0] code to get the start state into yy_current_state goes here */
	yy_current_state = (yy_start);

	for ( yy_cp = (yytext_ptr) + YY_MORE_ADJ; yy_cp < (yy_c_buf_p); ++yy_cp )
		{
/* %% [16.0] code to find the next state goes here */
		YY_CHAR yy_c = (*yy_cp ? yy_ec[YY_SC_TO_UI(*yy_cp)] : 1);
		if ( yy_accept[yy_current_state] )
			{
			(yy_last_accepting_state) = yy_current_state;
			(yy_last_accepting_cpos) = yy_cp;
			}
		while ( yy_chk[yy_base[yy_current_state] + yy_c] != yy_current_state )
			{
			yy_current_state = (int) yy_def[yy_current_state];
			if ( yy_current_state >= 56 )
				yy_c = yy_meta[yy_c];
			}
		yy_current_state = yy_nxt[yy_base[yy_current_state] + yy_c];
		}

	return yy_current_state;
}

/* yy_try_NUL_trans - try to make a transition on the NUL character
 *
 * synopsis
 *	next_state = yy_try_NUL_trans( current_state );
 */
/* %if-c-only */
/* %endif */
/* %if-c++-only */
    yy_state_type yyFlexLexer::yy_try_NUL_trans( yy_state_type yy_current_state )
/* %endif */
{
	int yy_is_jam;
    /* %% [17.0] code to find the next state, and perhaps do backing up, goes here */
	char *yy_cp = (yy_c_buf_p);

	YY_CHAR yy_c = 1;
	if ( yy_accept[yy_current_state] )
		{
		(yy_last_accepting_state) = yy_current_state;
		(yy_last_accepting_cpos) = yy_cp;
		}
	while ( yy_chk[yy_base[yy_current_state] + yy_c] != yy_current_state )
		{
		yy_current_state = (int) yy_def[yy_current_state];
		if ( yy_current_state >= 56 )
			yy_c = yy_meta[yy_c];
		}
	yy_current_state = yy_nxt[yy_base[yy_current_state] + yy_c];
	yy_is_jam = (yy_current_state == 55);

		return yy_is_jam ? 0 : yy_current_state;
}

#ifndef YY_NO_UNPUT
/* %if-c-only */
/* %endif */
/* %if-c++-only */
    void yyFlexLexer::yyunput( int c, char* yy_bp)
/* %endif */
{
	char *yy_cp;
    
    yy_cp = (yy_c_buf_p);

	/* undo effects of setting up yytext */
	*yy_cp = (yy_hold_char);

	if ( yy_cp < YY_CURRENT_BUFFER_LVALUE->yy_ch_buf + 2 )
		{ /* need to shift things up to make room */
		/* +2 for EOB chars. */
		int number_to_move = (yy_n_chars) + 2;
		char *dest = &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[
					YY_CURRENT_BUFFER_LVALUE->yy_buf_size + 2];
		char *source =
				&YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[number_to_move];

		while ( source > YY_CURRENT_BUFFER_LVALUE->yy_ch_buf )
			*--dest = *--source;

		yy_cp += (int) (dest - source);
		yy_bp += (int) (dest - source);
		YY_CURRENT_BUFFER_LVALUE->yy_n_chars =
			(yy_n_chars) = (int) YY_CURRENT_BUFFER_LVALUE->yy_buf_size;

		if ( yy_cp < YY_CURRENT_BUFFER_LVALUE->yy_ch_buf + 2 )
			YY_FATAL_ERROR( "flex scanner push-back overflow" );
		}

	*--yy_cp = (char) c;

/* %% [18.0] update yylineno here */

	(yytext_ptr) = yy_bp;
	(yy_hold_char) = *yy_cp;
	(yy_c_buf_p) = yy_cp;
}
/* %if-c-only */
/* %endif */
#endif

/* %if-c-only */
/* %endif */
/* %if-c++-only */
    int yyFlexLexer::yyinput()
/* %endif */
{
	int c;
    
	*(yy_c_buf_p) = (yy_hold_char);

	if ( *(yy_c_buf_p) == YY_END_OF_BUFFER_CHAR )
		{
		/* yy_c_buf_p now points to the character we want to return.
		 * If this occurs *before* the EOB characters, then it's a
		 * valid NUL; if not, then we've hit the end of the buffer.
		 */
		if ( (yy_c_buf_p) < &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars)] )
			/* This was really a NUL. */
			*(yy_c_buf_p) = '\0';

		else
			{ /* need more input */
			int offset = (int) ((yy_c_buf_p) - (yytext_ptr));
			++(yy_c_buf_p);

			switch ( yy_get_next_buffer(  ) )
				{
				case EOB_ACT_LAST_MATCH:
					/* This happens because yy_g_n_b()
					 * sees that we've accumulated a
					 * token and flags that we need to
					 * try matching the token before
					 * proceeding.  But for input(),
					 * there's no matching to consider.
					 * So convert the EOB_ACT_LAST_MATCH
					 * to EOB_ACT_END_OF_FILE.
					 */

					/* Reset buffer status. */
					yyrestart( yyin );

					/*FALLTHROUGH*/

				case EOB_ACT_END_OF_FILE:
					{
					if ( yywrap(  ) )
						return 0;

					if ( ! (yy_did_buffer_switch_on_eof) )
						YY_NEW_FILE;
#ifdef __cplusplus
					return yyinput();
#else
					return input();
#endif
					}

				case EOB_ACT_CONTINUE_SCAN:
					(yy_c_buf_p) = (yytext_ptr) + offset;
					break;
				}
			}
		}

	c = *(unsigned char *) (yy_c_buf_p);	/* cast for 8-bit char's */
	*(yy_c_buf_p) = '\0';	/* preserve yytext */
	(yy_hold_char) = *++(yy_c_buf_p);

/* %% [19.0] update BOL and yylineno */

	return c;
}
/* %if-c-only */
/* %endif */

/** Immediately switch to a different input stream.
 * @param input_file A readable stream.
 * 
 * @note This function does not reset the start condition to @c INITIAL .
 */
/* %if-c-only */
/* %endif */
/* %if-c++-only */
    void yyFlexLexer::yyrestart( std::istream& input_file )
/* %endif */
{
    
	if ( ! YY_CURRENT_BUFFER ){
        yyensure_buffer_stack ();
		YY_CURRENT_BUFFER_LVALUE =
            yy_create_buffer( yyin, YY_BUF_SIZE );
	}

	yy_init_buffer( YY_CURRENT_BUFFER, input_file );
	yy_load_buffer_state(  );
}

/* %if-c++-only */
/** Delegate to the new version that takes an istream reference.
 * @param input_file A readable stream.
 * 
 * @note This function does not reset the start condition to @c INITIAL .
 */
void yyFlexLexer::yyrestart( std::istream* input_file )
{
	if( ! input_file ) {
		input_file = &yyin;
	}
	yyrestart( *input_file );
}
/* %endif */

/** Switch to a different input buffer.
 * @param new_buffer The new input buffer.
 * 
 */
/* %if-c-only */
/* %endif */
/* %if-c++-only */
    void yyFlexLexer::yy_switch_to_buffer( YY_BUFFER_STATE new_buffer )
/* %endif */
{
    
	/* TODO. We should be able to replace this entire function body
	 * with
	 *		yypop_buffer_state();
	 *		yypush_buffer_state(new_buffer);
     */
	yyensure_buffer_stack ();
	if ( YY_CURRENT_BUFFER == new_buffer )
		return;

	if ( YY_CURRENT_BUFFER )
		{
		/* Flush out information for old buffer. */
		*(yy_c_buf_p) = (yy_hold_char);
		YY_CURRENT_BUFFER_LVALUE->yy_buf_pos = (yy_c_buf_p);
		YY_CURRENT_BUFFER_LVALUE->yy_n_chars = (yy_n_chars);
		}

	YY_CURRENT_BUFFER_LVALUE = new_buffer;
	yy_load_buffer_state(  );

	/* We don't actually know whether we did this switch during
	 * EOF (yywrap()) processing, but the only time this flag
	 * is looked at is after yywrap() is called, so it's safe
	 * to go ahead and always set it.
	 */
	(yy_did_buffer_switch_on_eof) = 1;
}

/* %if-c-only */
/* %endif */
/* %if-c++-only */
    void yyFlexLexer::yy_load_buffer_state()
/* %endif */
{
    	(yy_n_chars) = YY_CURRENT_BUFFER_LVALUE->yy_n_chars;
	(yytext_ptr) = (yy_c_buf_p) = YY_CURRENT_BUFFER_LVALUE->yy_buf_pos;
/* %if-c-only */
/* %endif */
/* %if-c++-only */
	yyin.rdbuf(YY_CURRENT_BUFFER_LVALUE->yy_input_file);
/* %endif */
	(yy_hold_char) = *(yy_c_buf_p);
}

/** Allocate and initialize an input buffer state.
 * @param file A readable stream.
 * @param size The character buffer size in bytes. When in doubt, use @c YY_BUF_SIZE.
 * 
 * @return the allocated buffer state.
 */
/* %if-c-only */
/* %endif */
/* %if-c++-only */
    YY_BUFFER_STATE yyFlexLexer::yy_create_buffer( std::istream& file, int size )
/* %endif */
{
	YY_BUFFER_STATE b;
    
	b = (YY_BUFFER_STATE) yyalloc( sizeof( struct yy_buffer_state )  );
	if ( ! b )
		YY_FATAL_ERROR( "out of dynamic memory in yy_create_buffer()" );

	b->yy_buf_size = size;

	/* yy_ch_buf has to be 2 characters longer than the size given because
	 * we need to put in 2 end-of-buffer characters.
	 */
	b->yy_ch_buf = (char *) yyalloc( (yy_size_t) (b->yy_buf_size + 2)  );
	if ( ! b->yy_ch_buf )
		YY_FATAL_ERROR( "out of dynamic memory in yy_create_buffer()" );

	b->yy_is_our_buffer = 1;

	yy_init_buffer( b, file );

	return b;
}

/* %if-c++-only */
/** Delegate creation of buffers to the new version that takes an istream reference.
 * @param file A readable stream.
 * @param size The character buffer size in bytes. When in doubt, use @c YY_BUF_SIZE.
 * 
 * @return the allocated buffer state.
 */
	YY_BUFFER_STATE yyFlexLexer::yy_create_buffer( std::istream* file, int size )
{
	return yy_create_buffer( *file, size );
}
/* %endif */

/** Destroy the buffer.
 * @param b a buffer created with yy_create_buffer()
 * 
 */
/* %if-c-only */
/* %endif */
/* %if-c++-only */
    void yyFlexLexer::yy_delete_buffer( YY_BUFFER_STATE b )
/* %endif */
{
    
	if ( ! b )
		return;

	if ( b == YY_CURRENT_BUFFER ) /* Not sure if we should pop here. */
		YY_CURRENT_BUFFER_LVALUE = (YY_BUFFER_STATE) 0;

	if ( b->yy_is_our_buffer )
		yyfree( (void *) b->yy_ch_buf  );

	yyfree( (void *) b  );
}

/* Initializes or reinitializes a buffer.
 * This function is sometimes called more than once on the same buffer,
 * such as during a yyrestart() or at EOF.
 */
/* %if-c-only */
/* %endif */
/* %if-c++-only */
    void yyFlexLexer::yy_init_buffer( YY_BUFFER_STATE b, std::istream& file )
/* %endif */

{
	int oerrno = errno;
    
	yy_flush_buffer( b );

/* %if-c-only */
/* %endif */
/* %if-c++-only */
	b->yy_input_file = file.rdbuf();
/* %endif */
	b->yy_fill_buffer = 1;

    /* If b is the current buffer, then yy_init_buffer was _probably_
     * called from yyrestart() or through yy_get_next_buffer.
     * In that case, we don't want to reset the lineno or column.
     */
    if (b != YY_CURRENT_BUFFER){
        b->yy_bs_lineno = 1;
        b->yy_bs_column = 0;
    }

/* %if-c-only */
/* %endif */
/* %if-c++-only */
	b->yy_is_interactive = 0;
/* %endif */
	errno = oerrno;
}

/** Discard all buffered characters. On the next scan, YY_INPUT will be called.
 * @param b the buffer state to be flushed, usually @c YY_CURRENT_BUFFER.
 * 
 */
/* %if-c-only */
/* %endif */
/* %if-c++-only */
    void yyFlexLexer::yy_flush_buffer( YY_BUFFER_STATE b )
/* %endif */
{
    	if ( ! b )
		return;

	b->yy_n_chars = 0;

	/* We always need two end-of-buffer characters.  The first causes
	 * a transition to the end-of-buffer state.  The second causes
	 * a jam in that state.
	 */
	b->yy_ch_buf[0] = YY_END_OF_BUFFER_CHAR;
	b->yy_ch_buf[1] = YY_END_OF_BUFFER_CHAR;

	b->yy_buf_pos = &b->yy_ch_buf[0];

	b->yy_at_bol = 1;
	b->yy_buffer_status = YY_BUFFER_NEW;

	if ( b == YY_CURRENT_BUFFER )
		yy_load_buffer_state(  );
}

/* %if-c-or-c++ */
/** Pushes the new state onto the stack. The new state becomes
 *  the current state. This function will allocate the stack
 *  if necessary.
 *  @param new_buffer The new state.
 *  
 */
/* %if-c-only */
/* %endif */
/* %if-c++-only */
void yyFlexLexer::yypush_buffer_state (YY_BUFFER_STATE new_buffer)
/* %endif */
{
    	if (new_buffer == NULL)
		return;

	yyensure_buffer_stack();

	/* This block is copied from yy_switch_to_buffer. */
	if ( YY_CURRENT_BUFFER )
		{
		/* Flush out information for old buffer. */
		*(yy_c_buf_p) = (yy_hold_char);
		YY_CURRENT_BUFFER_LVALUE->yy_buf_pos = (yy_c_buf_p);
		YY_CURRENT_BUFFER_LVALUE->yy_n_chars = (yy_n_chars);
		}

	/* Only push if top exists. Otherwise, replace top. */
	if (YY_CURRENT_BUFFER)
		(yy_buffer_stack_top)++;
	YY_CURRENT_BUFFER_LVALUE = new_buffer;

	/* copied from yy_switch_to_buffer. */
	yy_load_buffer_state(  );
	(yy_did_buffer_switch_on_eof) = 1;
}
/* %endif */

/* %if-c-or-c++ */
/** Removes and deletes the top of the stack, if present.
 *  The next element becomes the new top.
 *  
 */
/* %if-c-only */
/* %endif */
/* %if-c++-only */
void yyFlexLexer::yypop_buffer_state (void)
/* %endif */
{
    	if (!YY_CURRENT_BUFFER)
		return;

	yy_delete_buffer(YY_CURRENT_BUFFER );
	YY_CURRENT_BUFFER_LVALUE = NULL;
	if ((yy_buffer_stack_top) > 0)
		--(yy_buffer_stack_top);

	if (YY_CURRENT_BUFFER) {
		yy_load_buffer_state(  );
		(yy_did_buffer_switch_on_eof) = 1;
	}
}
/* %endif */

/* %if-c-or-c++ */
/* Allocates the stack if it does not exist.
 *  Guarantees space for at least one push.
 */
/* %if-c-only */
/* %endif */
/* %if-c++-only */
void yyFlexLexer::yyensure_buffer_stack(void)
/* %endif */
{
	yy_size_t num_to_alloc;
    
	if (!(yy_buffer_stack)) {

		/* First allocation is just for 2 elements, since we don't know if this
		 * scanner will even need a stack. We use 2 instead of 1 to avoid an
		 * immediate realloc on the next call.
         */
      num_to_alloc = 1; /* After all that talk, this was set to 1 anyways... */
		(yy_buffer_stack) = (struct yy_buffer_state**)yyalloc
								(num_to_alloc * sizeof(struct yy_buffer_state*)
								);
		if ( ! (yy_buffer_stack) )
			YY_FATAL_ERROR( "out of dynamic memory in yyensure_buffer_stack()" );

		memset((yy_buffer_stack), 0, num_to_alloc * sizeof(struct yy_buffer_state*));

		(yy_buffer_stack_max) = num_to_alloc;
		(yy_buffer_stack_top) = 0;
		return;
	}

	if ((yy_buffer_stack_top) >= ((yy_buffer_stack_max)) - 1){

		/* Increase the buffer to prepare for a possible push. */
		yy_size_t grow_size = 8 /* arbitrary grow size */;

		num_to_alloc = (yy_buffer_stack_max) + grow_size;
		(yy_buffer_stack) = (struct yy_buffer_state**)yyrealloc
								((yy_buffer_stack),
								num_to_alloc * sizeof(struct yy_buffer_state*)
								);
		if ( ! (yy_buffer_stack) )
			YY_FATAL_ERROR( "out of dynamic memory in yyensure_buffer_stack()" );

		/* zero only the new slots.*/
		memset((yy_buffer_stack) + (yy_buffer_stack_max), 0, grow_size * sizeof(struct yy_buffer_state*));
		(yy_buffer_stack_max) = num_to_alloc;
	}
}
/* %endif */

/* %if-c-only */
/* %endif */

/* %if-c-only */
/* %endif */

/* %if-c-only */
/* %endif */

/* %if-c-only */
/* %endif */
/* %if-c++-only */
    void yyFlexLexer::yy_push_state( int _new_state )
/* %endif */
{
    	if ( (yy_start_stack_ptr) >= (yy_start_stack_depth) )
		{
		yy_size_t new_size;

		(yy_start_stack_depth) += YY_START_STACK_INCR;
		new_size = (yy_size_t) (yy_start_stack_depth) * sizeof( int );

		if ( ! (yy_start_stack) )
			(yy_start_stack) = (int *) yyalloc( new_size  );

		else
			(yy_start_stack) = (int *) yyrealloc(
					(void *) (yy_start_stack), new_size  );

		if ( ! (yy_start_stack) )
			YY_FATAL_ERROR( "out of memory expanding start-condition stack" );
		}

	(yy_start_stack)[(yy_start_stack_ptr)++] = YY_START;

	BEGIN(_new_state);
}

/* %if-c-only */
/* %endif */
/* %if-c++-only */
    void yyFlexLexer::yy_pop_state()
/* %endif */
{
    	if ( --(yy_start_stack_ptr) < 0 )
		YY_FATAL_ERROR( "start-condition stack underflow" );

	BEGIN((yy_start_stack)[(yy_start_stack_ptr)]);
}

/* %if-c-only */
/* %endif */
/* %if-c++-only */
    int yyFlexLexer::yy_top_state()
/* %endif */
{
    	return (yy_start_stack)[(yy_start_stack_ptr) - 1];
}

#ifndef YY_EXIT_FAILURE
#define YY_EXIT_FAILURE 2
#endif

/* %if-c-only */
/* %endif */
/* %if-c++-only */
void yyFlexLexer::LexerError( const char* msg )
{
    	std::cerr << msg << std::endl;
	exit( YY_EXIT_FAILURE );
}
/* %endif */

/* Redefine yyless() so it works in section 3 code. */

#undef yyless
#define yyless(n) \
	do \
		{ \
		/* Undo effects of setting up yytext. */ \
        int yyless_macro_arg = (n); \
        YY_LESS_LINENO(yyless_macro_arg);\
		yytext[yyleng] = (yy_hold_char); \
		(yy_c_buf_p) = yytext + yyless_macro_arg; \
		(yy_hold_char) = *(yy_c_buf_p); \
		*(yy_c_buf_p) = '\0'; \
		yyleng = yyless_macro_arg; \
		} \
	while ( 0 )

/* Accessor  methods (get/set functions) to struct members. */

/* %if-c-only */
/* %if-reentrant */
/* %endif */
/* %if-reentrant */
/* %endif */
/* %endif */

/* %if-reentrant */
/* %if-bison-bridge */
/* %endif */
/* %endif if-c-only */

/* %if-c-only */
/* %endif */

/* %if-c-only SNIP! this currently causes conflicts with the c++ scanner */
/* %if-reentrant */
/* %endif */
/* %endif */

/*
 * Internal utility routines.
 */

#ifndef yytext_ptr
static void yy_flex_strncpy (char* s1, const char * s2, int n )
{
		
	int i;
	for ( i = 0; i < n; ++i )
		s1[i] = s2[i];
}
#endif

#ifdef YY_NEED_STRLEN
static int yy_flex_strlen (const char * s )
{
	int n;
	for ( n = 0; s[n]; ++n )
		;

	return n;
}
#endif

void *yyalloc (yy_size_t  size )
{
			return malloc(size);
}

void *yyrealloc  (void * ptr, yy_size_t  size )
{
		
	/* The cast to (char *) in the following accommodates both
	 * implementations that use char* generic pointers, and those
	 * that use void* generic pointers.  It works with the latter
	 * because both ANSI C and C++ allow castless assignment from
	 * any pointer type to void*, and deal with argument conversions
	 * as though doing an assignment.
	 */
	return realloc(ptr, size);
}

void yyfree (void * ptr )
{
			free( (char *) ptr );	/* see yyrealloc() for (char *) cast */
}

/* %if-tables-serialization definitions */
/* %define-yytables   The name for this specific scanner's tables. */
#define YYTABLES_NAME "yytables"
/* %endif */

/* %ok-for-header */

#line 98 "search_lexer.l"


