---
sidebar_position: 1
slug: /http_api_reference
---

# HTTP API Reference
import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';

A complete reference for Infinity's Python APIs.

---

## SCHEMA MANAGEMENT

---

### Create database

**POST** `/databases/{database_name}`

Creates a database by its name. If the database already exists, the action taken depends on the `"create_option"` parameter.

#### Request

- Method: POST
- URL: `/database/{database_name}`
- Headers:
  - `accept: application/json`
  - `content-Type: application/json`
- Body:
  - `"create_option"`: `enum<string>`

##### Request example

```shell
curl --request POST \
     --url http://localhost:23820/databases/{database_name} \
     --header 'accept: application/json' \
     --header 'content-type: application/json' \
     --data ' {"create_option": "ignore_if_exists"} '
```

##### Request parameters

- `database_name`: (*Path parameter*)  
  The name of the database, which must adhere to the following requirements:  
  - Permitted characters include:
    - English letters (a-z, A-Z)
    - Digits (0-9)
    - "_" (underscore)
  - Must begin with an English letter or underscore.
  - Maximum 65,535 characters.
  - Case-insensitive.
- `"create_option"`: (*Body parameter*), `enum<string>`, *Optional*  
  - `"error"`: (Default) Raise an error if a database with the same name exists.
  - `"ignore_if_exists"`: Ignore the database creation request and keep the existing database.

#### Response

<Tabs
  defaultValue="s200"
  values={[
    {label: 'Status code 200', value: 's200'},
    {label: 'Status code 500', value: 's500'},
  ]}>
  <TabItem value="s200">

The response includes a JSON object like the following:

```shell
{
    "error_code": 0 
}
```

- `"error_code"`: `integer`  
  `0`: The operation succeeds.


</TabItem>
  <TabItem value="s500">

The response includes a JSON object like the following:

```shell
{
    "error_code": 3016,
    "error_message": "Duplicate database: {database_name}."
}
```

- `"error_code"`: `integer`  
  A non-zero value indicates a specific error condition.
- `"error_message"`: `string`  
  When `error_code` is non-zero, `"error_message"` provides additional details about the error.

</TabItem>
</Tabs>

---

### Drop database

**DELETE** `/databases/{database_name}`

Deletes a database by its name. If the database does not exist, the action taken depends on the `"drop_option"` parameter.

#### Request

- Method: DELETE
- URL: `/database/{database_name}`
- Headers:
  - `accept: application/json`
  - `content-Type: application/json`
- Body:
  - `"drop_option"`: `enum<string>`

##### Request example

```shell
curl --request DELETE \
     --url http://localhost:23820/databases/{database_name} \
     --header 'accept: application/json' \
     --header 'content-type: application/json' \
     --data ' {"drop_option": "error"} '
```

##### Request parameter

- `database_name`: (*Path parameter*)  
  The name of the database to delete.
- `"drop_option"`: (*Body parameter*), `enum<string>`, *Optional*  
  - `"error"`: (Default) Raise an error if the specified database does not exist.
  - `"ignore_if_not_exists"`: Ignore the operation and proceed regardless, if the specified database does not exist.

#### Response

<Tabs
  defaultValue="s200"
  values={[
    {label: 'Status code 200', value: 's200'},
    {label: 'Status code 500', value: 's500'},
  ]}>
  <TabItem value="s200">

The response includes a JSON object like the following:

```shell
{
    "error_code": 0
}
```

- `"error_code"`: `integer`  
  `0`: The operation succeeds.

</TabItem>
  <TabItem value="s500">

The response includes a JSON object like the following:

```shell
{
    "error_code": 3021,
    "error_message": "{database_name} doesn't exist."
}
```

- `"error_code"`: `integer`  
  A non-zero value indicates a specific error condition.
- `"error_message"`: `string`  
  When `error_code` is non-zero, `"error_message"` provides additional details about the error.

</TabItem>
</Tabs>

---

### List databases

**GET** `/databases`

Retrieves a list of all available databases within the Infinity system.

#### Request

- Method: GET
- URL: `/databases`
- Headers: `accept: application/json`

##### Request example

```shell
curl --request GET \
     --url http://localhost:23820/databases \
     --header 'accept: application/json'
```

#### Response

##### Status code 200

The response includes a JSON object like the following:

```shell
{
    "error_code": 0,
    "databases": [
        "default_db",
        "my_db"
    ]  
}
```

- `"error_code"`: `integer`  
  `0`: The operation succeeds.
- `databases`: `string[]`  
  An array of strings representing the names of the databases in the system.

---

### Show database

**GET** `/databases/{database_name}`

Shows detailed information about a specified database.

#### Request

- Method: GET
- URL: `/database/{database_name}`
- Headers: `accept: application/json`

##### Request example

```shell
curl --request GET \
     --url http://localhost:23820/databases/{database_name} \
     --header 'accept: application/json'
```

##### Request parameter

- `database_name`: (*Path parameter*)  
  The name of the database to retrieve.

#### Response

<Tabs
  defaultValue="s200"
  values={[
    {label: 'Status code 200', value: 's200'},
    {label: 'Status code 500', value: 's500'},
  ]}>
  <TabItem value="s200">

The response includes a JSON object like the following:

```shell
{
    "error_code": 0,
    "database_name": "default_db"
    "storage_directory": "/var/infinity/data/nIHniKeHIB_db_default"
    "table_count": "4"
}
```

- `"error_code"`: `integer`  
  `0`: The operation succeeds.
- `database_name`: `string`  
  The name of the database.
- `storage_directory`: `string`  
  The directory path where the database is stored.
- `table_count`: `integer`  
  The number of tables present in the database.

</TabItem>
  <TabItem value="s500">

The response includes a JSON object like the following:

```shell
{
    "error_code": 3021,
    "error_message": "{database_name} doesn't exist."
}
```

- `"error_code"`: `integer`  
  A non-zero value indicates a specific error condition.
- `"error_message"`: `string`  
  When `error_code` is non-zero, `"error_message"` provides additional details about the error.

</TabItem>
</Tabs>

---

### Create table

**POST** `/databases/{database_name}/tables/{table_name}`

Creates a table with a specified name and defined fields (columns) within a given database. If the table already exists, the action taken depends on the `"create_option"` parameter.

#### Request

- Method: POST
- URL: `/databases/{database_name}/tables/{table_name}`
- Headers:
  - `accept: application/json`
  - `content-Type: application/json`
- Body:
  - `"create_option"`: `enum<string>`
  - `"fields"`: `object[]`

##### Request example

```shell
curl --request POST \
     --url http://localhost:23820/databases/{database_name}/tables/{table_name} \
     --header 'accept: application/json' \
     --header 'content-type: application/json' \
     --data ' {
         "create_option": "ignore_if_exists",
         "fields": [
             {
                  "name": "name",
                  "type": "varchar",
                  "comment": "name column"
             },
             {
                  "name": "score",
                  "type": "float",
                  "default": 3.0
             },
             {
                  "name": "dense_column",
                  "type": "vector,8,float",
                  "default": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]
             },
             {
                  "name": "fulltext_column",
                  "type": "varchar",
                  "default": ""
             },
             {
                  "name": "sparse_column",
                  "type": "sparse,128,float,int",
                  "default": {"10":1.1, "20":2.2, "30": 3.3}
             },
             {
                  "name": "tensor_column",
                  "type": "tensor,4,float",
                  "default": [[1.0, 0.0, 0.0, 0.0], [1.1, 0.0, 0.0, 0.0]]
             },  
             {
                  "name": "multivector_column",
                  "type": "multivector,4,float",
                  "default": [[0.0, 0.0, 0.0, 0.0], [0.0, 0.0, 0.0, 0.0]]
             },    
             {
                  "name": "tensorarray_column",
                  "type": "tensorarray,2,float",
                  "default": [[[1.0, 1.0], [1.0, 1.0]], [[1.0, 1.0]]]
             }          
        ]
    } '
```

##### Request parameters

- `database_name`: (*Path parameter*)  
  The name of the database.
- `table_name`: (*Path parameter*)  
  The name of the table to create, which must adhere to the following requirements:  
  - Permitted characters include:
    - English letters (a-z, A-Z)
    - Digits (0-9)
    - "_" (underscore)
  - Must begin with an English letter or underscore.
  - Maximum 65,535 characters.
  - Case-insensitive.
- `"create_option"`: (*Body parameter*), `enum<string>`, *Optional*  
  - `"error"`: (Default) Raise an error if a table with the same name exists.
  - `"ignore_if_exists"`: Ignore the table creation request and keep the existing table.
- `field`: (*Body parameter*), `object[]`, *Required*
  - `"name"`: `string`, *Required*  
    A non-empty string indicating the name of the column to create, which must adhere to the following requirements:  
    - Permitted characters include:
      - English letters (a-z, A-Z)
      - Digits (0-9)
      - "_" (underscore)
    - Must begin with an English letter or underscore.
    - Maximum 65,535 characters.
    - Case-insensitive.
  - `"type"`: `string`, *Required*  
    The data type of the column.  
    - Numeric:  
      - `"int8"`
      - `"int16"`
      - `"int"`/`"int32"`/`"integer"`
      - `"int64"`
      - `"float"`/`"float32"`
      - `"double"`/`"float64"`
      - `"float16"`
      - `"bfloat16"`
    - String: `"varchar"`
    - Dense vector: e.g., `"vector,128,float"`
      - `vector`: The column is a dense vector column.
      - The second item in the string: The dimension of dense vectors.
      - The third item in the string: The element type of dense vectors. Can be:
        - `"int8"`
        - `"int16"`
        - `"int"`/`"int32"`/`"integer"`
        - `"int64"`
        - `"float"`/`"float32"`
        - `"double"`/`"float64"`
        - `"float16"`
        - `"bfloat16"`
    - Sparse vector: e.g., `"sparse,128,float,int"`
      - `sparse`: The column is a sparse vector column.
      - The second item in the string: The dimension of sparse vectors.
      - The third item in the string: The element type of sparse vectors. Can be:
        - `"int8"`
        - `"int16"`
        - `"int"`/`"int32"`/`"integer"`
        - `"int64"`
        - `"float"`/`"float32"`
        - `"double"`/`"float64"`
        - `"float16"`
        - `"bfloat16"`
      - The fourth item in the string: The data type of the sparse vector indices. Can be:
        - `int8`
        - `int16`
        - `int`/`int32`/`integer`
        - `int64`
    - Tensor vector: e.g., `"tensor,4,float"`
      - `tensor`: The column is a tensor column.
      - The second item in the string: The dimension of each vector unit in a tensor.
      - The third item in the string: The element type of the tensors. Can be:
        - `"int8"`
        - `"int16"`
        - `"int"`/`"int32"`/`"integer"`
        - `"int64"`
        - `"float"`/`"float32"`
        - `"double"`/`"float64"`
        - `"float16"`
        - `"bfloat16"`
    - Tensor array: e.g., `"tensorarray,6,float"`
      - `tensorarray`: The column is a tensor-array column.
      - The second item in the string: The dimension of each vector unit in a tensor array.
      - The third item in the string: The element type of tensor arrays. Can be:
        - `"int8"`
        - `"int16"`
        - `"int"`/`"int32"`/`"integer"`
        - `"int64"`
        - `"float"`/`"float32"`
        - `"double"`/`"float64"`
        - `"float16"`
        - `"bfloat16"`
    - Multivector: e.g., `"multivector,128,float"`
      - `multivector`: The column is a multi-vector column.
      - The second item in the string: The dimension of each vector unit in a multi-vector.
      - The third item in the string: The element type of multi-vectors. Can be:
        - `"int8"`
        - `"int16"`
        - `"int"`/`"int32"`/`"integer"`
        - `"int64"`
        - `"float"`/`"float32"`
        - `"double"`/`"float64"`
        - `"float16"`
        - `"bfloat16"`
    - Array: e.g., `"array,varchar"`, `"array,array,varchar"`
      - `array`: The column is an array column.
      - followed by the element type of the array. Can be recursive.
      - for example, `"array,varchar"` is a one-dimensional array of strings, and `"array,array,varchar"` is a two-dimensional array of strings.
  - `"default"`: `Any`, *Optional*  
    The default value for unspecified cells in that column.
  - `"comment"`: `string`, *Optional*
    User provided text to describe the column.

#### Response

<Tabs
  defaultValue="s200"
  values={[
    {label: 'Status code 200', value: 's200'},
    {label: 'Status code 500', value: 's500'},
  ]}>
  <TabItem value="s200">

The response includes a JSON object like the following:

```shell
{
    "error_code": 0
}
```

- `"error_code"`: `integer`  
  `0`: The operation succeeds.

</TabItem>
  <TabItem value="s500">

The response includes a JSON object like the following:

```shell
{
    "error_code": 3017,
    "error_message": "Duplicate table: {table_name} in {database_name}."
}
```

- `"error_code"`: `integer`  
  A non-zero value indicates a specific error condition.
- `"error_message"`: `string`  
  When `error_code` is non-zero, `"error_message"` provides additional details about the error.

</TabItem>
</Tabs>

---

### Drop table

**DELETE** `/databases/{database_name}/tables/{table_name}`

Deletes a table from a specified database. If the table does not exist, the action taken depends on the `"drop_option"` parameter.

#### Request

- Method: DELETE
- URL: `/databases/{database_name}/tables/{table_name}`
- Headers:
  - `accept: application/json`
  - `content-Type: application/json`
- Body:
  - `"drop_option"`: `enum<string>`

##### Request example

```shell
curl --request DELETE \
     --url http://localhost:23820/databases/{database_name}/tables/{table_name} \
     --header 'accept: application/json' \
     --header 'content-type: application/json' \
     --data ' {"drop_option": "ignore_if_not_exists"} '
```

##### Request parameters

- `database_name`: (*Path parameter*)  
  The name of the database.
- `table_name`: (*Path parameter*)  
  The name of the table to delete.
- `"drop_option"`: (*Body parameter*), `enum<string>`, *Optional*  
  - `"error"`: (Default) Raise an error if the specified table does not exist.
  - `"ignore_if_not_exists"`: Ignore the operation and proceed regardless, if the specified database does not exist.

#### Response

<Tabs
  defaultValue="s200"
  values={[
    {label: 'Status code 200', value: 's200'},
    {label: 'Status code 500', value: 's500'},
  ]}>
  <TabItem value="s200">

The response includes a JSON object like the following:

```shell
{
    "error_code": 0
}
```

- `"error_code"`: `integer`  
  `0`: The operation succeeds.

</TabItem>
  <TabItem value="s500">

The response includes a JSON object like the following:

```shell
{
    "error_code": 3022,
    "error_message": "Table {table_name} doesn't exist in {database_name}."
}
```

- `"error_code"`: `integer`  
  A non-zero value indicates a specific error condition.
- `"error_message"`: `string`  
  When `error_code` is non-zero, `"error_message"` provides additional details about the error.

</TabItem>
</Tabs>

---

### List tables

**GET** `/databases/{database_name}/tables`

Retrieves a list of all available tables in a specified database.

#### Request

- Method: GET
- URL: `/databases/{database_name}/tables`
- Headers: `accept: application/json`

##### Request example

```shell
curl --request GET \
     --url http://localhost:23820/databases/{database_name}/tables \
     --header 'accept: application/json'
```

##### Request parameters

- `database_name`: (*Path parameter*)  
  The name of the database.

#### Response

##### Status code 200

The response includes a JSON object like the following:

```shell
{
    "error_code": 0,
    "tables": 
    [
         "table_1",
         "table_2",
         "table_n"
    ]
}
```

- `"error_code"`: `integer`  
  `0`: The operation succeeds.

---

### Show table

**GET** `/databases/{database_name}/tables/{table_name}`

Shows detailed information about a specified table within a given database.

#### Request

- Method: GET
- URL: `/databases/{database_name}/tables/{table_name}`
- Headers: `accept: application/json`

##### Request example

```shell
curl --request GET \
     --url http://localhost:23820/databases/{database_name}/tables/{table_name} \
     --header 'accept: application/json'
```

##### Request parameters

- `database_name`: (*Path parameter*)  
  The name of the database.
- `table_name`: (*Path parameter*)  
  The name of the table.

#### Response

<Tabs
  defaultValue="s200"
  values={[
    {label: 'Status code 200', value: 's200'},
    {label: 'Status code 500', value: 's500'},
  ]}>
  <TabItem value="s200">

The response includes a JSON object like the following:

```shell
{
    "error_code": 0,
    "database_name": "default_db",
    "table_name": "my_table",
    "storage_directory": "/var/infinity/data/nIHniKeHIB_db_default/h1abZcWuBs_table_my_table",
    "column_count" : 3,
    "segment_count" : 1,
    "row_count" : 5
}
```

- `"error_code"`: `integer`  
  `0`: The operation succeeds.

</TabItem>
  <TabItem value="s500">

A `500` HTTP status code indicates an error condition. The response includes a JSON object like the following:

```shell
{
    "error_code": 3022,
    "error_message": "Table {table_name} doesn't exist in {database_name}."
}
```

- `"error_code"`: `integer`  
  A non-zero value indicates a specific error condition.
- `"error_message"`: `string`  
  When `error_code` is non-zero, `"error_message"` provides additional details about the error.

</TabItem>
</Tabs>

---

### Show table columns

**GET** `/databases/{database_name}/tables/{table_name}/columns`

Shows the column information about a specified table.

#### Request

- Method: GET
- URL: `/databases/{database_name}/tables/{table_name}/columns`
- Headers: `accept: application/json`

##### Request example

```shell
curl --request GET \
     --url http://localhost:23820/databases/{database_name}/tables/{table_name}/columns \
     --header 'accept: application/json'
```

##### Request parameters

- `database_name`: (*Path parameter*)  
  The name of the database.
- `table_name`: (*Path parameter*)  
  The name of the table.

#### Response

<Tabs
  defaultValue="s200"
  values={[
    {label: 'Status code 200', value: 's200'},
    {label: 'Status code 500', value: 's500'},
  ]}>
  <TabItem value="s200">

The response includes a JSON object like the following:

```shell
{
    "error_code": 0,
    "columns": 
    [
        {
             "column_name": "name",
             "column_type": "Varchar",
             "constraints": "",
             "default": "Null"
        },
        {
             "column_name": "score",
             "column_type": "Float",
             "constraints": "",
             "default": "Null",
        }
        {
            "column_name": "vector_column",
            "column_type": "Embedding(float,8)",
            "constraints": "",
            "default": "Null"
        },
    ]
}
```

- `"error_code"`: `integer`  
  `0`: The operation succeeds.

</TabItem>
  <TabItem value="s500">

The response includes a JSON object like the following:

```shell
{
    "error_code": 3022,
    "error_message": "Table {table_name} doesn't exist in {database_name}."
}
```

- `"error_code"`: `integer`  
  A non-zero value indicates a specific error condition.
- `"error_message"`: `string`  
  When `error_code` is non-zero, `"error_message"` provides additional details about the error.

</TabItem>
</Tabs>

---

### Create index

**POST** `/databases/{database_name}/tables/{table_name}/indexes/{index_name}`

Creates an index on a specified table. If an index with the same name exists, the action taken depends on the `"create_option"` parameter.

#### Request

- Method: POST
- URL: `/databases/{database_name}/tables/{table_name}/indexes/{index_name}`
- Headers:
  - `accept: application/json`
  - `content-Type: application/json`
- Body:
  - `"fields"`: `string[]`
  - `"index"`: `object`
  - `"create_option"`: `enum<string>`

##### Request example

- Creates an HNSW index on a dense vector or a multivector column:

```shell
curl --request POST \
     --url http://localhost:23820/databases/{database_name}/tables/{table_name}/indexes/{index_name} \
     --header 'accept: application/json' \
     --header 'content-type: application/json' \
     --data ' 
     {
          "fields": 
          [
              "dense_column"
          ],
          "index": 
          {
              "type": "Hnsw",
              "M": "16",
              "ef_construction": "50",
              "metric": "l2"
          },
          "create_option": "ignore_if_exists"
     } '
```

- Creates a full-text index on a full-text column:

```shell
curl --request POST \
     --url http://localhost:23820/databases/{database_name}/tables/{table_name}/indexes/{index_name} \
     --header 'accept: application/json' \
     --header 'content-type: application/json' \
     --data ' 
     {
          "fields": 
          [
              "fulltext_column"
          ],
          "index": 
          {
              "type": "fulltext",
              "analyzer": "chinese"
          },
          "create_option": "ignore_if_exists"
     } '
```

- Creates a BMP index on a sparse column

```shell
curl --request POST \
     --url http://localhost:23820/databases/{database_name}/tables/{table_name}/indexes/{index_name} \
     --header 'accept: application/json' \
     --header 'content-type: application/json' \
     --data ' 
     {
          "fields": 
          [
              "sparse_column"
          ],
          "index": 
          {
              "type": "BMP",
              "block_size": "16",
              "compress_type": "raw"
          },
          "create_option": "ignore_if_exists"
     } '
```

- Creates a secondary index on a varchar column

```shell
curl --request POST \
     --url http://localhost:23820/databases/{database_name}/tables/{table_name}/indexes/{index_name} \
     --header 'accept: application/json' \
     --header 'content-type: application/json' \
     --data ' 
     {
          "fields": 
          [
              "varchar_column"
          ],
          "index": 
          {
              "type": "Secondary"
          },
          "create_option": "ignore_if_exists"
     } '
```

##### Request parameters

- `database_name`: (*Path parameter*)  
  The name of the database.
- `table_name`: (*Path parameter*)  
  The name of the table.  
- `index_name`: (*Path parameter*)  
  The name of the index to create, which must adhere to the following requirements:  
  - Permitted characters include:
    - English letters (a-z, A-Z)
    - Digits (0-9)
    - "_" (underscore)
  - Must begin with an English letter or underscore.
  - Maximum 65,535 characters.
  - Case-insensitive.
- `"fields"`: (*Body parameter*), `string[]`, *Required*  
  A non-empty list of strings indicating the names of the columns to build index on. For now, you are only allowed to create an index on one column.
- `"index"`: (*Body parameter*), `dict[string, string]`  
  - Parameter settings for an HNSW index:
    - `"type"`: `"hnsw"`
    - `"M"`: *Optional* - Defaults to`"16"`.
    - `"ef_construction"`: *Optional* - Defaults to`"50"`.
    - `"metric"` *Required* - The distance metric to use in similarity search.
      - `"ip"`: Inner product.
      - `"l2"`: Euclidean distance.
      - `"cosine"`: Cosine similarity.
    - `"encode"`: *Optional*
      - `"plain"`: (Default) Plain encoding.
      - `"lvq"`: Locally-adaptive vector quantization. Works with float vector element only.
  - Parameter settings for an IVF index:
    - `"metric"` *Required* - The distance metric to use in similarity search.
      - `"ip"`: Inner product.
      - `"l2"`: Euclidean distance.
      - `"cosine"`: Cosine similarity.
    - `"storage_type"`: *Optional*
      - `"plain"`: (Default) Plain storage.
      - `"scalar_quantization"`: Scalar quantization.
      - `"product_quantization"`: Product quantization.
    - `"plain_storage_data_type"`: *Optional* for plain storage.
      - `"int8"`: default value for `int8` embeddings.
      - `"uint8"`: default value for `uint8` embeddings.
      - `"float32"`: default value for floating-point embeddings.
      - `"float16"`: for floating-point embeddings.
      - `"bfloat16"`: for floating-point embeddings.
    - `"scalar_quantization_bits"`: *Required* for scalar quantization. Must be either `4` or `8`.
    - `"product_quantization_subspace_num"`: *Required* for product quantization. Must be divisor of the embedding dimension.
    - `"product_quantization_subspace_bits"`: *Required* for product quantization. Must be in the range `[4, 16]`.
  - Parameter settings for a full-text index:  
    - `"type"`: `"fulltext"`
    - `"ANALYZER"`: *Optional*
      - `"standard"`: (Default) Standard analyzer, segmented by tokens, lowercase processing, provides stemming outputs. Use `-` to specify stemmer for languages, `English` is the default stemmer: `"standard-english"` and `"standard"` have the same stemmer setting. Supported language stemmer includes: `Danish`, `Dutch`, `English`, `Finnish`, `French`, `German`, `Hungarian`, `Italian`, `Norwegian`, `Porter`, `Portuguese`, `Romanian`,`Russian`,`Spanish`,`Swedish`,`Turkish`.
      - `"rag"`: Multilingual RAG analyzer imported from [RAGFlow](https://github.com/infiniflow/ragflow/blob/main/rag/nlp/rag_tokenizer.py), supporting `Chinese` and `English`. Use `-fine` to output the fine-grained analyzer results.
      - `"chinese"`: Simplified Chinese. Use `-fine` to output the fine-grained analyzer results.
      - `"ik"`: Bilingual analyzer imported from [ik-analyzer](https://github.com/infinilabs/analysis-ik), supporting `Chinese` and `English`. Use `-fine` to output the fine-grained analyzer results.
      - `"traditional"`: Traditional Chinese.
      - `"japanese"`: Japanese.
      - `"korean"`: Korean.
      - `"ngram"`: [N-gram](https://en.wikipedia.org/wiki/N-gram).
      - `"keyword"`: "noop" analyzer used for columns containing keywords only.
  - Parameter settings for a secondary index:  
    - `"type"`: `"secondary"`
  - Parameter settings for a BMP index:  
    - `"type"`: `"bmp"`
    - `block_size`: *Optional* - The size of the block in a BMP index. Range: `"1"` ~ `"256"`. Defaults to `"16"`.
    - `"compress_type"`: *Optional*  
      - `"compress"`: (Default) Store the block-max index in sparse format. Works best with small block size situations.
      - `"raw"`: Store the block-max index without compression.
- `"create_option"`: (*Body parameter*), `enum<string>`, *Optional*  
  - `"error"`: (Default) Raise an error if a table with the same name exists.
  - `"ignore_if_exists"`: Ignore the table creation request and keep the existing table.

#### Response

<Tabs
  defaultValue="s200"
  values={[
    {label: 'Status code 200', value: 's200'},
    {label: 'Status code 500', value: 's500'},
  ]}>
  <TabItem value="s200">

The response includes a JSON object like the following:

```shell
{
    "error_code": 0
}
```

- `"error_code"`: `integer`  
  `0`: The operation succeeds.

</TabItem>
  <TabItem value="s500">

The response includes a JSON object like the following:

```shell
{
    "error_code": 3018,
    "error_message": "Duplicate index: {index} in {table_name}."
}
```

- `"error_code"`: `integer`  
  A non-zero value indicates a specific error condition.
- `"error_message"`: `string`  
  When `error_code` is non-zero, `"error_message"` provides additional details about the error.

</TabItem>
</Tabs>

---

### Drop index

**DELETE** `/databases/{database_name}/tables/{table_name}/indexes/{index_name}`

Deletes an index by its name.

#### Request

- Method: DELETE
- URL: `/databases/{database_name}/tables/{table_name}/indexes/{index_name}`
- Headers:
  - `accept: application/json`
  - `content-Type: application/json`
- Body:
  - `"drop_option"`: `enum<string>`

##### Request example

```shell
curl --request DELETE \
     --url http://localhost:23820/databases/{database_name}/tables/{table_name}/indexes/{index_name} \
     --header 'accept: application/json' \
     --header 'content-type: application/json' \
     --data ' {"drop_option": "ignore_if_not_exists"} '
```

##### Request parameters

- `database_name`: (*Path parameter*)  
  The name of the database.
- `table_name`: (*Path parameter*)  
  The name of the table.  
- `index_name`: (*Path parameter*)  
  The name of the index to delete.
- `"drop_option"`: (*Body parameter*), `enum<string>`, *Optional*  
  - `"error"`: (Default) Raise an error if the specified index does not exist.
  - `"ignore_if_not_exists"`: Ignore the operation and proceed regardless, if the specified index does not exist.

#### Response

<Tabs
  defaultValue="s200"
  values={[
    {label: 'Status code 200', value: 's200'},
    {label: 'Status code 500', value: 's500'},
  ]}>
  <TabItem value="s200">

The response includes a JSON object like the following:

```shell
{
    "error_code": 0
}
```

- `"error_code"`: `integer`  
  `0`: The operation succeeds.

</TabItem>
  <TabItem value="s500">

The response includes a JSON object like the following:

```shell
{
    "error_code": 3018,
    "error_message": "Index {index_name} doesn't exist in {table_name}."
}
```

- `"error_code"`: `integer`  
  A non-zero value indicates a specific error condition.
- `"error_message"`: `string`  
  When `error_code` is non-zero, `"error_message"` provides additional details about the error.

</TabItem>
</Tabs>

---

### List indexes

**GET** `/databases/{database_name}/tables/{table_name}/indexes`

Retrieves a list of all indexes created on a given table.

#### Request

- Method: GET
- URL: `/databases/{database_name}/tables/{table_name}/indexes`
- Headers: `accept: application/json`

##### Request example

```shell
curl --request GET \
     --url http://localhost:23820/databases/{database_name}/tables/{table_name}/indexes \
     --header 'accept: application/json'
```

##### Request parameters

- `database_name`: (*Path parameter*)  
  The name of the database.
- `table_name`: (*Path parameter*)  
  The name of the table.

#### Response

##### Status code 200

The response includes a JSON object like the following:

```shell
{
    "error_code": 0,
    "indexes": 
    [
        {
            "columns": "vector_column_1",
            "index_name": "idx1",
            "index_type": "HNSW"
        },
        {
            "columns": "vector_column_2",
            "index_name": "idx2",
            "index_type": "HNSW"
        }
    ]
}
```

- `"error_code"`: `integer`  
  `0`: The operation succeeds.

---

### Show index

**GET** `/databases/{database_name}/tables/{table_name}/indexes/{index_name}`

Shows detailed information about a specified index.

#### Request

- Method: GET
- URL: `/databases/{database_name}/tables/{table_name}/indexes/{index_name}`
- Headers: `accept: application/json`

##### Request example

```shell
curl --request GET \
     --url http://localhost:23820/databases/{database_name}/tables/{table_name}/indexes/{index_name} \
     --header 'accept: application/json'
```

##### Request parameters

- `database_name`: (*Path parameter*)  
  The name of the database.
- `table_name`: (*Path parameter*)  
  The name of the table.
- `index_name`: (*Path parameter*)  
  The name of the index.


#### Response

<Tabs
  defaultValue="s200"
  values={[
    {label: 'Status code 200', value: 's200'},
    {label: 'Status code 500', value: 's500'},
  ]}>
  <TabItem value="s200">

The response includes a JSON object like the following:

```shell
{
    "error_code": 0,
    "database_name": "default_db",
    "table_name": "test_vector_table",
    "index_name": "idx1",
    "index_column_ids": "2",
    "index_column_names": "vector_column",
    "index_type": "HNSW",
    "other_parameters": "metric = l2, encode_type = plain, M = 16, ef_construction = 50",
    "segment_index_count": "0",
    "storage_directory": "/var/infinity/data/3C1tizeluV_db_default_db/O0wSJ88IrJ_table_test_vector_table/RYurmCbD5h_index_idx1"
    "storage_size": "0B"
}
```

- `"error_code"`: `integer`  
  `0`: The operation succeeds.

</TabItem>
  <TabItem value="s500">

The response includes a JSON object like the following:

```shell
{
    "error_code": 3018,
    "error_message": "Index {index_name} doesn't exist in {table_name}."
}
```

- `"error_code"`: `integer`  
  A non-zero value indicates a specific error condition.
- `"error_message"`: `string`  
  When `error_code` is non-zero, `"error_message"` provides additional details about the error.

</TabItem>
</Tabs>

---

## DATA MANAGEMENT

---

### Import data

**PUT** `/databases/{database_name}/tables/{table_name}`

Imports data from a selected file into a specified table.

#### Request

- Method: PUT
- URL: `/databases/{database_name}/tables/{table_name}`
- Headers:
  - `accept: application/json`
  - `content-Type: application/json`
- Body:
  - `"file_path"`: `string`
  - `"file_type"`: `string`  
  - `"header"`: `boolean`
  - `"delimiter"`: `string`

##### Request example

```shell
curl --request PUT \
     --url http://localhost:23820/databases/{database_name}/tables/{table_name} \
     --header 'accept: application/json' \
     --header 'content-type: application/json' \
     --data ' {
         "file_path":"./filename.csv",
         "file_type":"csv",
         "header": true,
         "delimiter": `\t"
    } '
```

##### Request parameters

- `database_name`: (*Path parameter*)  
  The name of the database.
- `table_name`: (*Path parameter*)  
  The name of the table.
- `"file_path"`: (*Body parameter*)   
  Absolute path to the file to import
- `"file_type"`: (*Body parameter*)  
  The type of the file to import. Supported file types include:
  - `csv`
  - `json`
  - `jsonl`
  - `parquet`
- `"header"`: (*Body parameter*), `boolean`, *Optional*  
  Whether to display table header or not. Works with **.csv** files only:
  - `True`: Display table header.
  - `False`: (Default) Do not display table header.
- `"delimiter"`: (*Body parameter*), `string`, *Optional*, Defaults to ","  
  Delimiter to separate columns. Works with **.csv** files only.

#### Response

<Tabs
  defaultValue="s200"
  values={[
    {label: 'Status code 200', value: 's200'},
    {label: 'Status code 500', value: 's500'},
  ]}>
  <TabItem value="s200">

The response includes a JSON object like the following:

```shell
{
    "error_code": 0
}
```

- `"error_code"`: `integer`  
  `0`: The operation succeeds.

</TabItem>
  <TabItem value="s500">

The response includes a JSON object like the following:

```shell
{
    "error_code": 3032,
    "error_message": "Not supported file type: docx"
}
```

- `"error_code"`: `integer`  
  A non-zero value indicates a specific error condition.
- `"error_message"`: `string`  
  When `error_code` is non-zero, `"error_message"` provides additional details about the error.

</TabItem>
</Tabs>

---

### Export data

**GET** `/databases/{database_name}/tables/{table_name}`

Exports data from a specified table to a specified file.

#### Request

- Method: GET
- URL: `/databases/{database_name}/tables/{table_name}`
- Headers:
  - `accept: application/json`
  - `content-Type: application/json`
- Body:
  - `"file_path"`: `string`
  - `"file_type"`: `string`
  - `"header"`: `boolean`  
  - `"delimiter"`: `string`
  - `"offset"`: `int`
  - `"limit"`: `int`
  - `"row_limit"`: `int`

##### Request example

```shell
curl --request GET \
     --url http://localhost:23820/databases/{database_name}/table/{table_name} \
     --header 'accept: application/json' \
     --header 'content-type: application/json' \
     --data ' {
         "file_path": "/var/infinity/filename.csv",
         "file_type": "csv",
         "header": false, 
         "delimiter": "\t",
         "offset": 2,
         "limit": 6,
         "row_limit": 2
    } '
```

##### Request parameters

- `database_name`: (*Path parameter*)  
  The name of the database.
- `table_name`: (*Path parameter*)  
  The name of the table.
- `"file_path"`: (*Body parameter*)   
  Absolute path to the file for export.
- `"file_type"`: (*Body parameter*)  
  The type of the file for export. Supported file types include:
  - `csv`
  - `jsonl`
  - `parquet`
- `"header"`: (*Body parameter*), `boolean`, *Optional*
  Whether to display table header or not. Works with **.csv** files only:
  - `True`: Display table header.
  - `False`: (Default) Do not display table header.
- `"delimiter"`: (*Body parameter*), `string`, *Optional*, Defaults to ","  
  Delimiter to separate columns. Works with **.csv** files only.
- `"offset"`: (*Body parameter*), `int`, *Optional*  
  Index specifying the starting row for export. Usually used in conjunction with `limit`. If not specified, the file export starts from the first row. 
- `"limit"`: `int`, *Optional*  
  The maximum number of rows to export. Usually used in conjunction with `offset`. If the table's row count exceeds `offset` + `limit`, the excess rows are excluded from the export.
- `"row_limit"`: (*Body parameter*), `int`, *Optional*  
  Used when you have a large table and need to break the output file into multiple parts. This argument sets the row limit for each part. If you specify **test_export_file.csv** as the file name, the exported files will be named **test_export_file.csv**, **test_export_file.csv.part1**, **test_export_file.csv.part2**, and so on.

#### Response

<Tabs
  defaultValue="s200"
  values={[
    {label: 'Status code 200', value: 's200'},
    {label: 'Status code 500', value: 's500'},
  ]}>
  <TabItem value="s200">

The response includes a JSON object like the following:

```shell
{
    "error_code": 0
}
```

- `"error_code"`: `integer`  
  `0`: The operation succeeds.

</TabItem>
  <TabItem value="s500">

The response includes a JSON object like the following:

```shell
{
    "error_code": 7002,
    "error_message": "File already existed: /var/infinity/filename.csv"
}
```

- `"error_code"`: `integer`  
  A non-zero value indicates a specific error condition.
- `"error_message"`: `string`  
  When `error_code` is non-zero, `"error_message"` provides additional details about the error.

</TabItem>
</Tabs>

---

### Insert data

**POST** `/databases/{database_name}/tables/{table_name}/docs`

Inserts rows of data into a specified table.

#### Request

- Method: POST
- URL: `/databases/{database_name}/tables/{table_name}/docs`
- Headers:
  - `accept: application/json`
  - `content-Type: application/json`
- Body:
  - `object[]`

##### Request example

```shell
curl --request POST \
     --url http://localhost:23820/databases/{database_name}/tables/{table_name}/docs \
     --header 'accept: application/json' \
     --header 'content-type: application/json' \
     --data ' 
     [    
          {
               "name": "Jennifer",
               "score": 5.0,
               "fulltext_column": "You have a heart like gold.",
               "dense_column": [1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
               "sparse_column": {"10":1.1, "20":2.2, "30": 3.3},
               "tensor_column": [[1.0, 0.0, 0.0, 0.0], [1.1, 0.0, 0.0, 0.0]],
               "tensorarray_column": [[[1.0, 1.0], [1.0, 1.0]], [[1.0, 1.0]]],
               "multivector_column": [[1.0, 0.0, 0.0, 0.0], [0.0, 0.0, 0.0, 0.0]],
               "array_array_varchar_column": {"array": [{"array": ["hello", "world"]}, {"array": ["!"]}, {"array": [""]}, {"array": []}]}
          },
          {
               "name": "Neil",
               "score": 2.0,
               "fulltext_column": "You're absolutely lucky.",
               "dense_column": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
               "sparse_column": {"10":1.1, "20":2.2, "30": 3.3},
               "tensor_column": [[1.0, 0.0, 0.0, 0.0], [1.1, 0.0, 0.0, 0.0]],
               "tensorarray_column": [[[1.0, 1.0], [1.0, 1.0]], [[1.0, 1.0]]],
               "multivector_column": [[0.0, 0.0, 0.0, 0.0], [0.0, 0.0, 0.0, 0.0]],
               "array_array_varchar_column": {"array": []}
          }
     ] '  
```

##### Request parameters

- `database_name`: (*Path parameter*)  
  The name of the database.
- `table_name`: (*Path parameter*)  
  The name of the table.
- `"file_path"`: (*Body parameter*), `string`, *Required* 
- `Body`: (*Body parameter*),  `object[]`, *Required*  
  Data to insert. Infinity supports inserting multiple rows to a table at one time in the form of `object[]`. Each JSON object corresponds to a row to insert, and each key-value pair it corresponds to a column name and the table cell value.

:::tip NOTE
- When inserting incomplete rows of data, ensure that all un-inserted columns have default values when creating the table. Otherwise, an error will occur.
- You are not allowed to insert both complete and incomplete rows of data in one request. 
:::

:::tip NOTE
Batch row limit: 8,192. You are allowed to insert a maximum of 8,192 rows at once.
:::

#### Response

<Tabs
  defaultValue="s200"
  values={[
    {label: 'Status code 200', value: 's200'},
    {label: 'Status code 500', value: 's500'},
  ]}>
  <TabItem value="s200">

The response includes a JSON object like the following:

```shell
{
    "error_code": 0
}
```

- `"error_code"`: `integer`  
  `0`: The operation succeeds.

</TabItem>
  <TabItem value="s500">

The response includes a JSON object like the following:

```shell
{
    "error_code": 3005,
    "error_message": "Column {column_name} doesn't exist in {table_name}."
}
```

- `"error_code"`: `integer`  
  A non-zero value indicates a specific error condition.
- `"error_message"`: `string`  
  When `error_code` is non-zero, `"error_message"` provides additional details about the error.

</TabItem>
</Tabs>

---

### Delete data

**DELETE** `/databases/{database_name}/tables/{table_name}/docs`

Deletes rows from a table based on the specified condition.

#### Request

- Method: DELETE
- URL: `/databases/{database_name}/tables/{table_name}/docs`
- Headers:
  - `accept: application/json`
  - `content-Type: application/json`
- Body:
  - `"filter"`: `string`

##### Request example

```shell
curl --request DELETE \
     --url http://localhost:23820/databases/{database_name}/tables/{table_name}/docs \
     --header 'accept: application/json' \
     --header 'content-type: application/json' \
     --data ' {"filter": "score >= 4.0 and score <= 5.0"} '
```

```shell
curl --request DELETE \
     --url http://localhost:23820/databases/{database_name}/tables/{table_name}/docs \
     --header 'accept: application/json' \
     --header 'content-type: application/json' \
     --data ' {"filter": "name = '"'Toby'"'"} '
```

##### Request parameters

- `database_name`: (*Path parameter*)  
  The name of the database.
- `table_name`: (*Path parameter*)  
  The name of the table.
- `"filter"`: (*Body parameter*), `string`, *Optional*  
  A string that defines the condition for selecting rows to delete. The parameter can be an expression, a function, or any other form of conditional logic that evaluates to `True` for the rows that should be deleted. If `"filter"` is not specified or set to `Null`, the method will delete all rows in the table.

#### Response

<Tabs
  defaultValue="s200"
  values={[
    {label: 'Status code 200', value: 's200'},
    {label: 'Status code 500', value: 's500'},
  ]}>
  <TabItem value="s200">

The response includes a JSON object like the following:

```shell
{
    "error_code": 0,
    "deleted_rows": 10
}
```

- `"error_code"`: `integer`  
  `0`: The operation succeeds.

</TabItem>
  <TabItem value="s500">

The response includes a JSON object like the following:

```shell
{
    "error_code": 3005,
    "error_message": "Column {column_name} doesn't exist in {table_name}."
}
```

- `"error_code"`: `integer`  
  A non-zero value indicates a specific error condition.
- `"error_message"`: `string`  
  When `error_code` is non-zero, `"error_message"` provides additional details about the error.

</TabItem>
</Tabs>

---

### Update data

**PUT** `/databases/{database_name}/tables/{table_name}/docs`

Searches for rows that match the specified condition and updates them accordingly.

#### Request

- Method: PUT
- URL: `/databases/{database_name}/tables/{table_name}/docs`
- Headers:
  - `accept: application/json`
  - `content-Type: application/json`
- Body:
  - `"update"`: `object`
  - `"filter"`: `string`

##### Request example

The following code updates the score to `1.0` for rows where the `name` field is `Neil`.

```shell
curl --request PUT \
     --url http://localhost:23820/databases/{database_name}/tables/{table_name}/docs \
     --header 'accept: application/json' \
    --header 'content-type: application/json' \
    --data ' 
    {
        "update": {"score": 1.0},
        "filter": "name = '"'Neil'"'"
    } '
```

##### Request parameters

- `database_name`: (*Path parameter*)  
  The name of the database.
- `table_name`: (*Path parameter*)  
  The name of the table.
- `"filter"`: (*Body parameter*), `string`, *Optional*  
  A string that defines the condition for selecting rows to update. The parameter can be an expression, a function, or any other form of conditional logic that evaluates to `true` for the rows that should be updated. If `"filter"` is not specified or set to `"Null"`, the method will update all rows in the table.


#### Response

<Tabs
  defaultValue="s200"
  values={[
    {label: 'Status code 200', value: 's200'},
    {label: 'Status code 500', value: 's500'},
  ]}>
  <TabItem value="s200">

The response includes a JSON object like the following:

```shell
{
    "error_code": 0,
    "update_row_count": 10
}
```

- `"error_code"`: `integer`  
  `0`: The operation succeeds.

</TabItem>
  <TabItem value="s500">

The response includes a JSON object like the following:

```shell
{
    "error_code": 3005,
    "error_message": "Column {column_name} doesn't exist in {table_name}."
}
```

- `"error_code"`: `integer`  
  A non-zero value indicates a specific error condition.
- `"error_message"`: `string`  
  When `error_code` is non-zero, `"error_message"` provides additional details about the error.

</TabItem>
</Tabs>

---

## SEARCH

---

### Search data

**GET** `/databases/{database_name}/tables/{table_name}/docs`

Searches for data in a specified table. The search can range from a simple vector search, sparse vector search, or full-text search to complex hybrid searches involving reranking methods.

#### Request

- Method: GET
- URL: `/databases/{database_name}/tables/{table_name}/docs`
- Headers:
  - `accept: application/json`
  - `content-Type: application/json`
- Body:
  - `"output"`: `string[]`
  - `"highlight"`: `string[]`
  - `"filter"`: `string`
  - `"fusion"`: `object`
  - `"sort"` : `object[]`
  - `"group_by"`: `string[]`
  - `"having"`: `string`
  - `"limit"` : `string`
  - `"offset"` : `string`
  - `"option"` : `object`

##### Request example

```shell
curl --request GET \
     --url http://localhost:23820/databases/{database_name}/tables/{table_name}/docs \
     --header 'accept: application/json' \
    --header 'content-type: application/json' \
    --data ' 
    {
        "output":
        [
            "name",
            "score",
            "body"
        ],
        "highlight":
        [
            "body"
        ],
        "filter": "score >= 4.0 and name = '"'Toby'"'",
        "search":
        [
            {
                "match_method": "dense",
                "fields": "dense_column",
                "query_vector": [1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0],
                "element_type": "float",
                "metric_type": "l2",
                "topn": 2,
                "params": {"ef": "10"}
            },
            {
                "match_method": "text",
                "fields": "body",
                "matching_text": "bloom",
                "topn": 1,
                "params":
                {
                    "default_fields": "body",
                    "operator": "or"
                }
            },
            {
                "match_method": "sparse",
                "fields": "sparse_column",
                "query_vector": {"10":1.1, "20":2.2, "30": 3.3},
                "metric_type": "ip",
                "topn": 3,
                "params": {
                              "alpha": "1.0",
                              "beta": "1.0"
                          }   
            },
            {
                "fusion_method": "rrf",
                "topn": 2,
                "params":{"rank_constant": "60"}
            }
        ]
    } '
```

##### Request parameters

- `database_name`: (*Path parameter*)  
  The name of the database.
- `table_name`: (*Path parameter*)  
  The name of the table.

- `"output"`: (*Body parameter*), `string[]`, *Required*  
  A non-empty list of strings specifying the columns to include in the output. Each string in the list can represent:  
  - A user-defined column name: The name of the column to include in the output, e.g., `"body"`.
  - All user-defined columns: Use a wildcard `"*"` to select all columns.
  - A special system column: system-generated columns include:
    - `_row_id`:  An automatically generated, unique identifier for each row in the table. It serves as a unique key for each row but does not necessarily correspond to the actual row number. When the data in a row is updated, the `_row_id` for that row is also changed to reflect the update.
    - `_score`: A BM25 score used in full-text search.
    - `_similarity`: Used by IP and cosine metrics in dense or sparse vector search.
    - `_distance`: Used by L2 metric in dense vector search.
  - An aggregation function: Apply an aggregation operation on specified columns. Supported aggregation functions include:
    - `count`
    - `min`
    - `max`
    - `sum`
    - `avg`
  - An arithmetic function: Apply an arithmetic operation on specified columns (e.g., `c1+5`).  
  > The list must contain at least one element. Empty lists are not allowed.

- `"highlight"`: (*Body parameter*), `string`, *Optional*
    A list of strings specifying the columns to include words match in match_text clause. The matched words will be embraced by <em> and </em>

- `"filter"`: (*Body parameter*), `string`, *Optional*  
  A string that defines the condition for selecting rows. The parameter can be an expression, a function, or any other form of conditional logic that evaluates to `true` for the rows that should be output.
  Supported filter expressions:
  - Logical combination expressions: 'and', 'or', 'not'
  - Comparison expressions: `<`, `<=`, `>`, `>=`, `=`, `==`, `!=`
  - `in` and `not in` expression
  - `filter_fulltext` expression:
    - Similar to `match_text()`. Usage: 'filter_fulltext(fields, matching_text)' or 'filter_fulltext(fields, matching_text, extra_options)'
    - 'extra_options' is in the format of 'K1=V1;K2=V2;...;KN=VN',
      where each 'K' represents a parameter name and 'V' represents its value
    - Available parameters in 'extra_options':
      - **'minimum_should_match'**: specifies how many clauses in the 'matching_text' should be satisfied at least.
        It can be in the following formats:
        - Positive integer `N`: at least `N` clauses should be satisfied.
        - Negative integer `-N`: at least (total clause count - `N`) clauses should be satisfied.
        - Positive Percentage `N%`: at least `⌊total clause count * N%⌋` clauses should be satisfied.
        - Negative Percentage `-N%`: at least `total clause count - ⌊total clause count * N%⌋` clauses should be satisfied.
        - Combination `K<V`: `K` is positive integer, `V` is in one of four styles described above.
          This means that when `total clause count > K`, the requirement `V` applies, otherwise all the clauses should be satisfied
        - Multiple combinations `K1<V1 K2<V2 ... KN<VN`: several `K<V` strings seperated by spaces, with `K` in the ascending order.
          If `K1 >= total clause count`, all the clauses should be satisfied.
          Otherwise, we find the biggest `V` which is less than the total clause count and apply the correspondent `V`.
      - **'default_field'**
        - If `"fields"` is an empty string, this parameter specifies the default field to search on.


- `"search"`: (*Body parameter*), `object[]`, *Required*  
  A list of JSON objects, each representing a matching or fusion method. Note that if you have specified multiple matching methods, you must specify a fusion method.  
  - `"match_method"`: (*Body parameter*), `enum<string>`, *Required*  
    The matching method for a specified column/field:  
    - `"dense"`: Creates a dense vector search expression to identify the top n most similar rows to the given dense vector. Suitable for working with dense vectors (dense embeddings) or multi-vectors (multiple dense embeddings in one row).  
    - `"sparse"`: Creates a sparse vector search expression to identify the top n most similar rows to the given sparse vector. Suitable for working with sparse vectors (sparse embeddings).
    - `"text"`: Creates a full-text search expression on the specified field(s)/column(s) to identify the top n most similar rows.  
      > Ensure that a full-text index has been successfully built on each column involved before executing a full-text search; otherwise, an error will occur.  

  - `"fields"`: `string`, *Required*  
    A non-empty string indicating the name of the column/field to search on.  
  - `"query_vector"`: `[]/object`, *Optional*  
    The query vector data to compare against. This should be provided as a list or a JSON object.  
    Used *only* when `"match_method"` is set to `"dense"` or `"sparse"`.  
    - Dense query vector example:  `[1.0, 2.2, 3.4, 1.5]`
    - Sparse query vector example: `{"10":1.1, "20":2.2, "30": 3.3}`
  - `"matching_text"`: `string`, *Optional*  
    A non-empty text string to search for. Used *only* when `"match_method"` is set to `"text"`.  
    You can use various search options within the matching text, including:  
    - Single terms: `"blooms"`
    - OR multiple terms: `"Bloom OR filter"` or just `"Bloom filter"`
    - Phrase search: `'"Bloom filter"'`
    - AND multiple terms: `"space AND efficient"`
    - Escaping reserved characters: `"space\:efficient"`
    - Sloppy phrase search: `'"harmful chemical"~10'`
    - Field-specific search: `"title:(quick OR brown) AND body:foobar"`
  - `element_type`: `str`, *Required*  
    Specifies the data type of the embedding vector. Used *only* when `"match_method"` is set to `"dense"`. Commonly used types (values) include:  
    - `"float"`
    - `"uint8"`.
  - `metric_type`: `string`, *Required*  
    The distance metric to use in similarity search. Used *only* when `"match_method"` is set to `"dense"` or `"sparse"`.  
    - When `"match_method"` is set to `"dense"`:  
      - `"ip"`: Inner product.
      - `"l2"`: Euclidean distance.
      - `"cosine"`: Cosine similarity.
    - When `"match_method"` is set to `"sparse"`, only `"ip"` can be used.  
  - `"fusion_method"`: (*Body parameter*), `enum<string>`, *Required when you have specified multiple matching methods*  
    - `"rrf"`: [Reciprocal rank fusion](https://plg.uwaterloo.ca/~gvcormac/cormacksigir09-rrf.pdf)  
      RRF is a method for combining multiple result sets with varying relevance indicators into a single result set. It requires no tuning, and the relevance indicators need not be related to achieve high-quality results. RRF is particularly useful when you are uncertain of the relative importance of each retrieval way.  
      RRF uses the following formula to calculate the score for matching each document:  
      ```python
      score = 0.0
      for q in queries:
          if d in result(q):
              score += 1.0 / ( k + rank( result(q), d ) )
      return score

      # Where
      # k is the ranking constant,
      # q is a query in a set of queries,
      # d is a document in the result set of q,
      # result(q) is the result set of q, and
      # rank( result(q), d ) is the rank of d within the result(q), starting from 1.
      ```

    - `"weighted_sum"`  
      The weighted sum approach assigns different weights to different retrieval ways, allowing you to emphasize specific ways. This is particularly useful when you are certain of each path's relative importance.  
    - `"match_tensor"`  
      Infinity's tensor-based late interaction reranking approach.  

  - `"topn"`: `int`, *Required*  
    An integer indicating the number of nearest neighbours (vector search) or most relevant rows (full-text search) to return.  
  - `"params"`: `object`, *Optional*  
    Additional matching or reranking parameters.
    - For all matching methods, you can provide a index hint when searching, so that the query will run using specified index.
      - For `"match_method" : "dense"`, `"match_method" : "sparse"`, `"match_method" : "tensor"`, use `"index_name" : "idx1"` to specify index, note that if the index specified is not present, an error would occur.
      - For `"match_method" : "text"`, since the query can run on multiple columns, you should specify multiple indexes using `"index_names" : "idx1,idx2..."`, there will be no error when some of specified indexes are not present.
    - If you set `"match_method"` to `"dense"`:  
      - `"ef"`: `str`, Recommended value: one to ten times the value of `topn`.  
        - For example, if you set `topn` to `10`, you can set `"ef"` to `"50"`.
        - If you set `"ef"` too high, search performance may worsen.  
        - If you do not set `"ef"` or set it to a value lower than `topn`, the search uses the `topn` value as the value for `"ef"`.
      - `"threshold"`: `str`, *Optional* A threshold value for the search.
        - For example, if you use the `"cosine"` distance metric and set `"threshold"` to `"0.5"`, the search will return only those rows with a cosine similarity greater than `0.5`.
      - `"nprobe"`: `str`, *Optional* The number of cells to search in the IVF index. The default value is `"1"`.
    - If you set `"match_method"` to `"sparse"`:  
      - `"alpha"`: `str`  
        `"0.0"` ~ `"1.0"` (default: `"1.0"`) - A "Termination Conditions" parameter. The smaller the value, the more aggressive the pruning.
      - `"beta"`: `str`  
        `"0.0"` ~ `"1.0"` (default: `"1.0"`) - A "Query Term Pruning" parameter. The smaller the value, the more aggressive the pruning.

    - If you set `"match_method"` to `"text"`:  
      - **"default_field"**: `str`, *Optional*  
        - If `"fields"` is an empty string, this parameter specifies the default field to search on.  
      - **"operator"**: `str`, *Optional*  
        - If not specified, the search follows Infinity's full-text search syntax, meaning that logical and arithmetic operators, quotation marks and escape characters will function as full-text search operators, such as:
          - AND operator: `AND`
          - OR operator: `OR`
          - NOT operator: `NOT`
          - PAREN operator: `(`, `)`, need to appear in pairs, and can be nested.
          - COLON operator: `:`: Used to specify field-specific search, e.g., `body:foobar` searches for `foobar` in the `body` field.
          - CARAT operator: `^`: Used to boost the importance of a term, e.g., `quick^2 brown` boosts the importance of `quick` by a factor of 2, making it twice as important as `brown`.
          - TILDE operator: `~`: Used for sloppy phrase search, e.g., `"harmful chemical"~10` searches for the phrase `"harmful chemical"` within a tolerable distance of 10 words.
          - SINGLE_QUOTED_STRING: Used to search for a phrase, e.g., `'Bloom filter'`.
          - DOUBLE_QUOTED_STRING: Used to search for a phrase, e.g., `"Bloom filter"`.
          - Escape characters: Used to escape reserved characters, e.g., `space\:efficient`. Starting with a backslash `\` will escape the following characters:
            `' '`, `'('`, `')'`, `'^'`, `'"'`, `'\''`, `'~'`, `'*'`, `'?'`, `':'`, `'\\'`
        - If specified, Infinity's full-text search syntax will not take effect, and the specified operator will be interpolated into `matching_text`.  
          Useful for searching text including code numbers like `"A01-233:BC"`.  
          - `{"operator": "or"}`: Interpolates the `OR` operator between words in `matching_text` to create a new search text.  
            For example, reinterprets `"A01-233:BC"` as `'"A01" OR "-233" OR "BC"'`.  
          - `{"operator": "and"}`: Interpolates the `AND` operator between words in `matching_text` to create a new search text.  
            For example, reinterprets `"A01-233:BC"` as `'"A01" AND "-233" AND "BC"'`.      

    - If you set `"fusion_method"` to `"rrf"`, use a separate JSON to set the following parameter:  
      - `"rank_constant"`: The smoothing constant for RRF reranking, e.g., `{"rank_constant": 60}`. Defaults to `60`.  
    - If you set `"fusion_method"` to `"weighted_sum"`, use a separate JSON to set the following parameter:  
      - `"weights"`: Specifies the weight for each retrieval way. For example, `{"weights": "1,2,0.5"}` sets weights of `1`, `2`, and `0.5` for the first, second, and third retrieval ways, respectively. The default weight of each retrieval way is `1.0`. If `"weight"` is not specified, all retrieval ways will be assigned the default weight of `1.0`.
    - If you set `"fusion_method"` to `"match_tensor"`, use a separate JSON to set the following parameters:  
      - `"fields"`: The name of the tensor column for reranking.
      - `"query_tensor"`: The tensor data to compare against. This should be provided as a list of lists of numerical values.
      - `"element_type"`: The element data type of the query tensor. Usually `"float"`.

- `"sort"` : `object[]`
  Defines how to sort the results.

- `"group_by"`: `string[]`
  Indicates the expression to group by.

- `"having"`: `string`
  Indicates the having condition.

- `"limit"` : `string`
  Indicates the limit row count.

- `"offset"` : `string`
  Indicates the offset position of the limit expression. You must use this parameter together with `limit`.

- `"option"` : `object`
  Indicates some search options.  This parameter must be used in conjunction with `limit`.

#### Response

<Tabs
  defaultValue="s200"
  values={[
    {label: 'Status code 200', value: 's200'},
    {label: 'Status code 500', value: 's500'},
  ]}>
  <TabItem value="s200">

The response includes a JSON object like the following:

```shell
{
    "error_code": 0,
    "output": [
        {
            "name": "Tom",
            "age": 16
        }
    ]
    "total_hits_count": 3
}
```

- `"error_code"`: `integer`  
  `0`: The operation succeeds.
- `"total_hits_count"`: `integer`, Optional
  Available if you set a search option with `"total_hits_count": "true"`

</TabItem>
  <TabItem value="s500">

The response includes a JSON object like the following:

```shell
{
    "error_code": 3005,
    "error_message": "Column {column_name} doesn't exist in {table_name}."
}
```

- `"error_code"`: `integer`  
  A non-zero value indicates a specific error condition.
- `"error_message"`: `string`  
  When `error_code` is non-zero, `"error_message"` provides additional details about the error.

</TabItem>
</Tabs>

---

## ADMINISTRATION

---

### Set variable

**POST** `/variables/{variable_name}`

Assigns a value to a global variable.

#### Request

- Method: POST
- URL: `/variables/{variable_name}`
- Headers:
  - `accept: application/json`
  - `content-Type: application/json`
- Body:
  - `"profile_record_capacity"`: `integer`

##### Request example

```shell
curl --request POST \
     --url http://localhost:23820/variables/{variable_name} \
     --header 'accept: application/json' \
     --header 'content-type: application/json' \
     --data ' { "profile_record_capacity" : 120 } '
```

##### Request parameter

- `variable_name`: (*Path parameter*)  
  The name of the variable, which must adhere to the following requirements:  
  - Permitted characters include:
    - English letters (a-z, A-Z)
    - Digits (0-9)
    - "_" (underscore)
  - Must begin with an English letter or underscore.
  - Maximum 65,535 characters.
  - Case-insensitive.

#### Response

<Tabs
  defaultValue="s200"
  values={[
    {label: 'Status code 200', value: 's200'},
    {label: 'Status code 500', value: 's500'},
  ]}>
  <TabItem value="s200">

The response includes a JSON object like the following:

```shell
{
    "error_code": 0
}
```

- `"error_code"`: `integer`  
  `0`: The operation succeeds.

</TabItem>
  <TabItem value="s500">

The response includes a JSON object like the following:

```shell
{
    "error_code": 3076,
    "error_message": "Invalid command: unknown global variable {variable_name}"
}
```

- `"error_code"`: `integer`  
  A non-zero value indicates a specific error condition.
- `"error_message"`: `string`  
  When `error_code` is non-zero, `"error_message"` provides additional details about the error.

</TabItem>
</Tabs>

---

### Show variables

**GET** `/variables`

Retrieves all global variables in the Infinity system.

#### Request

- Method: GET
- URL: `/variables`
- Headers: `accept: application/json`

##### Request example

```shell
curl --request GET \
     --url http://localhost:23820/variables \
     --header 'accept: application/json'
```

#### Response

##### Status code 200

The response includes a JSON object like the following:

```shell
{
    "error_code":0,
    "active_txn_count":"1",
    "active_wal_filename":"/var/infinity/wal/wal.log",
    "buffer_object_count":"6",
    "buffer_usage":"0B/4.00GB",
    "current_timestamp":"16774",
    "delta_log_count":"1",
    "next_transaction_id":"6",
    "profile_record_capacity":"128",
    "query_count":"0",
    "schedule_policy":"round robin",
    "session_count":"1",
    "total_commit_count":"0",
    "total_rollback_count":"0",
    "unused_buffer_object":"0"
}
```

- `"error_code"`: `integer`  
  `0`: The operation succeeds.

---

### Show variable

**GET** `/variables/{variable_name}`

Retrieves the value of a global variable.

#### Request

- Method: GET
- URL: `/variables/{variable_name}`
- Headers: `accept: application/json`

##### Request example

```shell
curl --request GET \
     --url http://localhost:23820/variables/{variable_name} \
     --header 'accept: application/json'
```

##### Request parameters

- `variable_name`: (*Path parameter*)  
  The name of the variable.

#### Response

<Tabs
  defaultValue="s200"
  values={[
    {label: 'Status code 200', value: 's200'},
    {label: 'Status code 500', value: 's500'},
  ]}>
  <TabItem value="s200">

The response includes a JSON object like the following:

```shell
{
    "error_code": 0,
    "buffer_object_count":"6"
}
```

- `"error_code"`: `integer`  
  `0`: The operation succeeds.

</TabItem>
  <TabItem value="s500">

The response includes a JSON object like the following:

```shell
{
    "error_code": 3027,
    "error_message": "No such system variable {variable_name}."
}
```

- `"error_code"`: `integer`  
  A non-zero value indicates a specific error condition.
- `"error_message"`: `string`  
  When `error_code` is non-zero, `"error_message"` provides additional details about the error.

</TabItem>
</Tabs>

---

### Set config

**PUT** `/configs`

Assigns a value to a configuration parameter.

#### Request

- Method: PUT
- URL: `/configs`
- Headers:
  - `accept: application/json`
  - `content-Type: application/json`
- Body: `object`

##### Request example

```shell
curl --request POST \
     --url http://localhost:23820/configs \
     --header 'accept: application/json' \
     --header 'content-type: application/json' \
     --data ' { "log_level": "trace" } '
```

##### Request parameter

- `Body`:  (*Body parameter*),  `object`, *Required*  
  The configuration parameters to update, where each key (`string`) in the JSON object represents an entry, and the corresponding value (`string`) represents its value. 


#### Response

<Tabs
  defaultValue="s200"
  values={[
    {label: 'Status code 200', value: 's200'},
    {label: 'Status code 500', value: 's500'},
  ]}>
  <TabItem value="s200">

The response includes a JSON object like the following:

```shell
{
    "error_code": 0
}
```

- `"error_code"`: `integer`  
  `0`: The operation succeeds.

</TabItem>
  <TabItem value="s500">

The response includes a JSON object like the following:

```shell
{
    "error_code": 3028,
    "error_message": "log level value range is trace, debug, info, warning, error, critical"
}
```

- `"error_code"`: `integer`  
  A non-zero value indicates a specific error condition.
- `"error_message"`: `string`  
  When `error_code` is non-zero, `"error_message"` provides additional details about the error.

</TabItem>
</Tabs>

---

### Show configs

**GET** `/configs`

Retrieves the values of all configuration parameters.

#### Request

- Method: GET
- URL: `/configs`
- Headers: `accept: application/json`

##### Request example

```shell
curl --request GET \
     --url http://localhost:23820/configs \
     --header 'accept: application/json'
```

#### Response

##### Status code 200

The response includes a JSON object like the following:

```shell
{
    "buffer_manager_size":"4294967296",
    "cleanup_interval":"10",
    "client_port":"23817",
    "compact_interval":"10",
    "connection_pool_size":"256",
    "cpu_limit":"16",
    "data_dir":"/var/infinity/data",
    "error_code":0,
    "checkpoint_interval":"30",
    "http_port":"23820",
    "log_dir":"/var/infinity/log",
    "log_file_max_size":"1073741824",
    "log_file_rotate_count":"8",
    "log_filename":"infinity.log",
    "log_level":"Info",
    "log_to_stdout":"False",
    "mem_index_capacity":"65536",
    "optimize_interval":"10",
    "postgres_port":"5432",
    "resource_dir":"/var/infinity/resource",
    "server_address":"0.0.0.0",
    "temp_dir":"/var/infinity/tmp",
    "time_zone":"UTC+8",
    "version":"0.6.0",
    "wal_compact_threshold":"1073741824",
    "wal_dir":"/var/infinity/wal",
    "wal_flush":"FlushAtOnce"
}
```

---

### Show config

**GET** `/configs/{config_name}`

Retrieves the value of a configuration parameter.

#### Request

- Method: GET
- URL: `/configs/{config_name}`
- Headers: `accept: application/json`

##### Request example

```shell
curl --request GET \
     --url http://localhost:23820/configs/{config_name} \
     --header 'accept: application/json'
```

##### Request parameters

- `config_name`: (*Path parameter*)  
  The name of the configuration parameter.

#### Response

<Tabs
  defaultValue="s200"
  values={[
    {label: 'Status code 200', value: 's200'},
    {label: 'Status code 500', value: 's500'},
  ]}>
  <TabItem value="s200">

The response includes a JSON object like the following:

```shell
{
    "error_code": 0,
    "version":"0.6.0"
}
```

- `"error_code"`: `integer`  
  `0`: The operation succeeds.

</TabItem>
  <TabItem value="s500">

A `500` HTTP status code indicates an error condition. The response includes a JSON object like the following:

```shell
{
    "error_code": 1008,
    "error_message": "Attempt to get option: {config_name} which doesn't exist."
}
```

- `"error_code"`: `integer`  
  A non-zero value indicates a specific error condition.
- `"error_message"`: `string`  
  When `error_code` is non-zero, `"error_message"` provides additional details about the error.

</TabItem>
</Tabs>

---

### Show buffer

**GET** `/instance/buffer`

List the cached buffer objects in database.

#### Request

- Method: GET
- URL: `/instance/buffer`
- Headers: `accept: application/json`

##### Request example

```shell
curl --request GET \
     --url http://localhost:23820/instance/buffer \
     --header 'accept: application/json'
```

#### Response

<Tabs
  defaultValue="s200"
  values={[
    {label: 'Status code 200', value: 's200'},
    {label: 'Status code 500', value: 's500'},
  ]}>
  <TabItem value="s200">

The response includes a JSON object like the following:

```shell
{
    "buffer": [
        {
            "buffered_type": "Persistent",
            "path": "/var/infinity/data/nlAn5spku9_db_default_db/sNzUhKy3er_table_table1/seg_0/blk_0/0.col",
            "size": "32768",
            "status": "Freed",
            "type": "data"
        },
        {
            "buffered_type": "Persistent",
            "path": "/var/infinity/data/nlAn5spku9_db_default_db/sNzUhKy3er_table_table1/seg_0/blk_0/2.col",
            "size": "131072",
            "status": "Freed",
            "type": "data"
        },
        {
            "buffered_type": "Persistent",
            "path": "/var/infinity/data/nlAn5spku9_db_default_db/sNzUhKy3er_table_table1/seg_0/blk_0/version",
            "size": "65536",
            "status": "Freed",
            "type": "version data"
        }
    ],
    "error_code": 0
}
```

- `"error_code"`: `integer`
  `0`: The operation succeeds.

</TabItem>
  <TabItem value="s500">

A `500` HTTP status code indicates an error condition. The response includes a JSON object like the following:

```shell
{
    "error_code": 2005,
    "error_message": "Not support in maintenance mode"
}
```

- `"error_code"`: `integer`
  A non-zero value indicates a specific error condition.
- `"error_message"`: `string`
  When `error_code` is non-zero, `"error_message"` provides additional details about the error.

</TabItem>
</Tabs>

---

### Show profiles

**GET** `/instance/profiles`

When set session variable 'profile', Infinity will record query profile information. This command is to list recorded queries profile.

#### Request

- Method: GET
- URL: `/instance/profiles`
- Headers: `accept: application/json`

##### Request example

```shell
curl --request GET \
     --url http://localhost:23820/instance/profiles \
     --header 'accept: application/json'
```

#### Response

<Tabs
  defaultValue="s200"
  values={[
    {label: 'Status code 200', value: 's200'},
    {label: 'Status code 500', value: 's500'},
  ]}>
  <TabItem value="s200">

The response includes a JSON object like the following:

```shell
{
    "error_code": 0,
    "profiles": [
        {
            "command parsing": "250us",
            "commit": "0ns",
            "execution": "380us",
            "logical plan building": "133us",
            "physical plan building": "44us",
            "pipeline building": "38us",
            "plan optimizing": "1000ns",
            "record_no": "0",
            "rollback": "35us",
            "task building": "70us",
            "total_cost": "951us"
        }
    ]
}
```

- `"error_code"`: `integer`
  `0`: The operation succeeds.

</TabItem>
  <TabItem value="s500">

A `500` HTTP status code indicates an error condition. The response includes a JSON object like the following:

```shell
{
    "error_code": 2005,
    "error_message": "Not support in maintenance mode"
}
```

- `"error_code"`: `integer`
  A non-zero value indicates a specific error condition.
- `"error_message"`: `string`
  When `error_code` is non-zero, `"error_message"` provides additional details about the error.

</TabItem>
</Tabs>

---

### Show queries

**GET** `/instance/queries`

Show running queries, only valid when config: record_running_query is set.

#### Request

- Method: GET
- URL: `/instance/queries`
- Headers: `accept: application/json`

##### Request example

```shell
curl --request GET \
     --url http://localhost:23820/instance/queries \
     --header 'accept: application/json'
```

#### Response

<Tabs
  defaultValue="s200"
  values={[
    {label: 'Status code 200', value: 's200'},
    {label: 'Status code 500', value: 's500'},
  ]}>
  <TabItem value="s200">

The response includes a JSON object like the following:

```shell
{
    "error_code": 0,
    "queries": [
        {
            "query_id": "1928",
            "query_kind": "COPY",
            "session_id": "63",
            "start_time": "21:22:49:708:989:0",
            "time_consumption": "2s"
        }
    ]
}
```

- `"error_code"`: `integer`
  `0`: The operation succeeds.

</TabItem>
  <TabItem value="s500">

A `500` HTTP status code indicates an error condition. The response includes a JSON object like the following:

```shell
{
    "error_code": 2005,
    "error_message": "Not support in maintenance mode"
}
```

- `"error_code"`: `integer`
  A non-zero value indicates a specific error condition.
- `"error_message"`: `string`
  When `error_code` is non-zero, `"error_message"` provides additional details about the error.

</TabItem>
</Tabs>

---

### Show transactions

**GET** `/instance/transactions`

Shows running transactions on database.

#### Request

- Method: GET
- URL: `/instance/transactions`
- Headers: `accept: application/json`

##### Request example

```shell
curl --request GET \
     --url http://localhost:23820/instance/transactions \
     --header 'accept: application/json'
```

#### Response

<Tabs
  defaultValue="s200"
  values={[
    {label: 'Status code 200', value: 's200'},
    {label: 'Status code 500', value: 's500'},
  ]}>
  <TabItem value="s200">

The response includes a JSON object like the following:

```shell
{
    "error_code":0,
    "transactions": [
        {
            "transaction_id":"27275",
            "transaction_text":""
        },
        {
            "transaction_id":"27274",
            "transaction_text":""
        }
    ]
}
```

- `"error_code"`: `integer`
  `0`: The operation succeeds.

</TabItem>
  <TabItem value="s500">

A `500` HTTP status code indicates an error condition. The response includes a JSON object like the following:

```shell
{
    "error_code": 2005,
    "error_message": "Not support in maintenance mode"
}
```

- `"error_code"`: `integer`
  A non-zero value indicates a specific error condition.
- `"error_message"`: `string`
  When `error_code` is non-zero, `"error_message"` provides additional details about the error.

</TabItem>
</Tabs>

---

### Show transaction

**GET** `/instance/transactions/{trasaction_id}`

Shows running transactions on database by transaction_id.

#### Request

- Method: GET
- URL: `/instance/transactions`
- Headers: `accept: application/json`

##### Request example

```shell
curl --request GET \
     --url http://localhost:23820/instance/transactions/{transaction_id} \
     --header 'accept: application/json'
```

##### Request parameters

- `transaction_id`: (*Path parameter*)
  The id of the running transaction.

#### Response

<Tabs
  defaultValue="s200"
  values={[
    {label: 'Status code 200', value: 's200'},
    {label: 'Status code 500', value: 's500'},
  ]}>
  <TabItem value="s200">

The response includes a JSON object like the following:

```shell
{
    "error_code":0,
    "transaction": {
        "transaction_id":"27275",
        "transaction_text":""
    }
}
```

- `"error_code"`: `integer`
  `0`: The operation succeeds.

</TabItem>
  <TabItem value="s500">

A `500` HTTP status code indicates an error condition. The response includes a JSON object like the following:

```shell
{
    "error_code": 2005,
    "error_message": "Not support in maintenance mode"
}
```

- `"error_code"`: `integer`
  A non-zero value indicates a specific error condition.
- `"error_message"`: `string`
  When `error_code` is non-zero, `"error_message"` provides additional details about the error.

</TabItem>
</Tabs>

---

### Show logs

**GET** `/instance/logs`

Shows logs.

#### Request

- Method: GET
- URL: `/instance/logs`
- Headers: `accept: application/json`

##### Request example

```shell
curl --request GET \
    --url http://localhost:23821/instance/logs \
    --header 'accept: application/json'
```

#### Response

##### Status code 200

The response includes a JSON object like the following:

```shell
{
    "error_code":0,
    "logs":[
        {
            "command_type":"CHECKPOINT",
            "commit_ts":"1",
            "text":"catalog path: catalog/FULL.82192.json\nmax commit ts: 82192\nis checkpoint: 1\n",
            "transaction_id":"82195"
        }
    ]
}
```

- `"error_code"`: `integer`  
  `0`: The operation succeeds.

---


### Show checkpoints

**GET** `/instance/global_checkpoint`

Shows delta logs.

#### Request

- Method: GET
- URL: `/instance/global_checkpoint`
- Headers: `accept: application/json`

##### Request example

```shell
curl --request GET \
    --url http://localhost:23821/instance/global_checkpoint \
    --header 'accept: application/json'
```

#### Response

##### Status code 200

The response includes a JSON object like the following:

```shell
{
    "error_code":0,
    "checkpoint":[
        {
            "file_path":"/var/infinity/data/catalog/FULL.82192.json",
            "max_commit_timestamp":"82192"
        }
    ]
}
```

- `"error_code"`: `integer`  
  `0`: The operation succeeds.

---

### Show objects

**GET** `/instance/objects`

Shows persistence objects.

#### Request

- Method: GET
- URL: `/instance/objects`
- Headers: `accept: application/json`

##### Request example

```shell
curl --request GET \
    --url http://localhost:23821/instance/objects \
    --header 'accept: application/json'
```

#### Response

##### Status code 200

The response includes a JSON object like the following:

```shell
{
    "error_code": 0, 
    "objects": [
        {
            "deleted_ranges": "", 
            "name": "01927642-6491-79d8-8891-5f91d7960cdf", 
            "parts": "4", 
            "reference_count": "0", 
            "size": "262236"
        }, 
        {
            "deleted_ranges": "[0, 426120) ", 
            "name": "019275ce-9eaf-7950-8bf0-b451cb15e6e1", 
            "parts": "0", 
            "reference_count": "0", 
            "size": "0"
        }, 
        {
            "deleted_ranges": "", 
            "name": "01927641-793c-7a30-b20c-b9089b6c0a9b", 
            "parts": "9", 
            "reference_count": "0", 
            "size": "426188"
        }, 
        {
            "deleted_ranges": "[0, 65722552) ", 
            "name": "019275cd-7310-7b40-a46a-cb12db4a391b", 
            "parts": "0", 
            "reference_count": "0", 
            "size": "0"
        }
    ]
}
```

- `"error_code"`: `integer`  
  `0`: The operation succeeds.

---

### Show object

**GET** `/instance/objects/{object_name}`

Shows persistence object by its name.

##### Request example

```shell
curl --request GET \
    --url http://localhost:23820/instance/objects/{object_name} \
    --header 'accept: application/json'
```

##### Request parameters

- `object_name`: Required (*Path parameter*)
  The object name.

#### Response

<Tabs
  defaultValue="s200"
  values={[
    {label: 'Status code 200', value: 's200'},
    {label: 'Status code 500', value: 's500'},
  ]}>
  <TabItem value="s200">

The response includes a JSON object like the following:

```shell
{
    "error_code": 0, 
    "object": [
        {
            "end": "426120", 
            "start": "0"
        }
    ]
}
```

- `"error_code"`: `integer`  
  `0`: The operation succeeds.

</TabItem>
  <TabItem value="s500">

A `500` HTTP status code indicates an error condition. The response includes a JSON object like the following:

```shell
{
    "error_code": 7008, 
    "error_message": "File: {object_name} isn't found@src/executor/operator/physical_show.cpp:5631@src/scheduler/fragment_context.cpp:1417"
}
```

- `"error_code"`: `integer`  
  A non-zero value indicates a specific error condition.
- `"error_message"`: `string`  
  When `error_code` is non-zero, `"error_message"` provides additional details about the error.

</TabItem>
</Tabs>

---

### Show files

**GET** `/instance/files`

Shows files in persistent manager.

#### Request

- Method: GET
- URL: `/instance/files`
- Headers: `accept: application/json`

##### Request example

```shell
curl --request GET \
    --url http://localhost:23821/instance/files \
    --header 'accept: application/json'
```

#### Response

##### Status code 200

The response includes a JSON object like the following:

```shell
{
    "error_code": 0, 
    "files": [
        {
            "file_name": "FC5WLAHAam_db_default_db/o3qQcKBZuI_table_hr_data_mix_remote/seg_0/blk_4/0.col", 
            "object_name": "01927a51-068a-7ba8-8d80-4d89bd6391de", 
            "offset": "78660", 
            "size": "131096"
        }, 
        {
            "file_name": "FC5WLAHAam_db_default_db/o3qQcKBZuI_table_hr_data_mix_remote/seg_0/blk_0/col_1_out_0", 
            "object_name": "01927a4f-c82c-7ba8-b135-80dd3cfb2d87", 
            "offset": "340852", 
            "size": "11435869"
        }, 
        {
            "file_name": "FC5WLAHAam_db_default_db/o3qQcKBZuI_table_hr_data_mix_remote/seg_0/blk_3/1.col", 
            "object_name": "01927a50-dd6d-7138-9665-9e21502390a6", 
            "offset": "209756", 
            "size": "131096"
        }, 
        {
            "file_name": "FC5WLAHAam_db_default_db/o3qQcKBZuI_table_hr_data_mix_remote/seg_0/blk_0/0.col", 
            "object_name": "01927a4f-c82c-7ba8-b135-80dd3cfb2d87", 
            "offset": "78660", 
            "size": "131096"
        }
    ]
}
```

- `"error_code"`: `integer`  
  `0`: The operation succeeds.

---

### Show memory

**GET** `/instance/memory`

Shows the memory usage information of the instance.

#### Request

- Method: GET
- URL: `/instance/memory`
- Headers: `accept: application/json`

##### Request example

```shell
curl --request GET \
    --url http://localhost:23821/instance/memory \
    --header 'accept: application/json'
```

#### Response

##### Status code 200

The response includes a JSON object like the following:

```shell
{
    "error_code":0,
    "memory_allocation":"Not activate",
    "memory_objects":"Not activate"
}
```

- `"error_code"`: `integer`  
  `0`: The operation succeeds.

---

### Show memory object

**GET** `/instance/memory/objects`

Shows the memory object usage information of the instance.

#### Request

- Method: GET
- URL: `/instance/memory/objects`
- Headers: `accept: application/json`

##### Request example

```shell
curl --request GET \
    --url http://localhost:23821/instance/memory/objects \
    --header 'accept: application/json'
```

#### Response

##### Status code 200

The response includes a JSON object like the following:

```shell
{
    "error_code":0,
}
```

- `"error_code"`: `integer`  
  `0`: The operation succeeds.

---

### Show memory allocation

**GET** `/instance/memory/allocation`

Shows the memory allocation information of the instance.

#### Request

- Method: GET
- URL: `/instance/memory/allocation`
- Headers: `accept: application/json`

##### Request example

```shell
curl --request GET \
    --url http://localhost:23821/instance/memory/allocation \
    --header 'accept: application/json'
```

#### Response

##### Status code 200

The response includes a JSON object like the following:

```shell
{
    "error_code":0,
}
```

- `"error_code"`: `integer`  
  `0`: The operation succeeds.

---

### Global Checkpoint

**POST** `/instance/flush`

Performs a checkpoint on the instance.

#### Request

- Method: POST
- URL: `/instance/flush`
- Headers:
  - `accept: application/json`
  - `content-type: application/json`

##### Request example

```shell
curl --request POST \
    --url http://localhost:23822/instance/flush\
    --header 'accept: application/json' \
```

#### Response

##### Status code 200

The response includes a JSON object like the following:

```shell
{
    "error_code":0,
}
```

- `"error_code"`: `integer`  
  `0`: The operation succeeds.

---

### Compact Table

**POST** `/instance/table/compact`

Performs a compaction operation on a specified table.

#### Request

- Method: POST
- URL: `/instance/table/compact`
- Headers:
  - `accept: application/json`
  - `content-type: application/json`

##### Request example

```shell
curl --request POST \
    --url http://localhost:23820/instance/table/compact \
    --header 'accept: application/json' \
    --header 'content-type: application/json' \
    --data ' 
    {
        "db_name" : "default_db",
        "table_name" : "my_table",
    } '
```

##### Request parameters

- `db_name`: Required (*Body parameter*)
- `table_name`: Required (*Body parameter*)
`db_name` and `table_name` specifies the table to perform compaction.

#### Response


<Tabs
  defaultValue="s200"
  values={[
    {label: 'Status code 200', value: 's200'},
    {label: 'Status code 500', value: 's500'},
  ]}>
  <TabItem value="s200">

The response includes a JSON object like the following:

```shell
{
    "error_code": 0
}
```

- `"error_code"`: `integer`  
  `0`: The operation succeeds.

</TabItem>
  <TabItem value="s500">

A `500` HTTP status code indicates an error condition. The response includes a JSON object like the following:

```shell
{
    "error_code":3022,
    "error_message":"Table id_timep doesn't exist@src/planner/query_binder.cpp:423"
}
```

- `"error_code"`: `integer`  
  A non-zero value indicates a specific error condition.
- `"error_message"`: `string`  
  When `error_code` is non-zero, `"error_message"` provides additional details about the error.

</TabItem>
</Tabs>

---

## DISTRIBUTED

---

### Admin set node role

**POST** `/admin/node/current`

Sets role for a node.

#### Request

- Method: POST
- URL: `/admin/node/current`
- Headers:
  - `accept: application/json`
  - `content-type: application/json`

##### Request example

```shell
curl --request POST \
    --url http://localhost:23822/admin/node/current \
    --header 'accept: application/json' \
    --header 'content-type: application/json' \
    --data ' {
        "role" : "follower",
        "name" : "following",
        "address" : "0.0.0.0:23851"
    } '
```

##### Request parameters

- `role`: Required (*Body parameter*)
  The role of the node. It must be set to `admin`, `standalone`, `leader` or `follower`.
    - Parameter settings for setting a node to `admin` and `standalone` are both empty, no extra parameters are required.
    - Parameter settings for setting a node to `leader`:
        - "name" Required : Name of the node in the cluster.
    - Parameter settings for setting a node to `follower` or `learner`:
        - "name" Required : Name of the node in the cluster.
        - "address" Required : Peer server address of the leader in the cluster.

#### Response

<Tabs
  defaultValue="s200"
  values={[
    {label: 'Status code 200', value: 's200'},
    {label: 'Status code 500', value: 's500'},
  ]}>
  <TabItem value="s200">

The response includes a JSON object like the following:

```shell
{
    "error_code": 0
}
```

- `"error_code"`: `integer`  
  `0`: The operation succeeds.

</TabItem>
  <TabItem value="s500">

A `500` HTTP status code indicates an error condition. The response includes a JSON object like the following:

```shell
{
    "error_code":7020
    "error_message" : "Duplicate node: following"
}
```

- `"error_code"`: `integer`  
  A non-zero value indicates a specific error condition.
- `"error_message"`: `string`  
  When `error_code` is non-zero, `"error_message"` provides additional details about the error.

</TabItem>
</Tabs>

---


### Admin remove node

**POST** `/admin/node/{node_name}`

Removes a node from the cluster. This command can only be executed by the leader node.

#### Request

- Method: DELETE
- URL: `/admin/node/node_name`
- Headers:
  - `accept: application/json`
  - `content-type: application/json`

##### Request example

```shell
curl --request DELETE \
    --url http://localhost:23821/admin/node/follower1 \
    --header 'accept: application/json' \
    --header 'content-type: application/json'
```

#### Response

<Tabs
  defaultValue="s200"
  values={[
    {label: 'Status code 200', value: 's200'},
    {label: 'Status code 500', value: 's500'},
  ]}>
  <TabItem value="s200">

The response includes a JSON object like the following:

```shell
{
    "error_code": 0
}
```

- `"error_code"`: `integer`
  `0`: The operation succeeds.

</TabItem>
  <TabItem value="s500">

A `500` HTTP status code indicates an error condition. The response includes a JSON object like the following:

```shell
{
    "error_code":7020
    "error_message" : "Duplicate node: following"
}
```

- `"error_code"`: `integer`
  A non-zero value indicates a specific error condition.
- `"error_message"`: `string`
  When `error_code` is non-zero, `"error_message"` provides additional details about the error.

</TabItem>
</Tabs>

---

### Admin show node variables

**GET** `/admin/variables`

Shows all node variables in admin mode.

#### Request

- Method: GET
- URL: `/admin/variables`
- Headers: `accept: application/json`

##### Request example

```shell
curl --request GET \
    --url http://localhost:23821/admin/variables \
    --header 'accept: application/json'
```

##### Request parameters

None.

#### Response

##### Status code 200

The response includes a JSON object like the following:

```shell
{
    "error_code":0,
    "role":"admin"
}
```

- `"error_code"`: `integer`  
  `0`: The operation succeeds.

---

### Admin show node configs

**GET** `/admin/configs`

Shows all node configs in admin mode.

#### Request

- Method: GET
- URL: `/admin/configs`
- Headers: `accept: application/json`

##### Request example

```shell
curl --request GET \
    --url http://localhost:23821/admin/configs \
    --header 'accept: application/json'
```

##### Request parameters

None.

#### Response

##### Status Code 200

The response includes a JSON object like the following:

```shell
{
    "buffer_manager_size":"4294967296",
    "cleanup_interval":"60",
    "client_port":"23818",
    "compact_interval":"120",
    "connection_pool_size":"128",
    "cpu_limit":"16",
    "data_dir":"/var/infinity/leader/data",
    "error_code":0,
    "checkpoint_interval":"86400",
    "http_port":"23821",
    "log_dir":"/var/infinity/leader/log",
    "log_file_max_size":"10737418240",
    "log_file_rotate_count":"10",
    "log_filename":"infinity.log",
    "log_level":"Debug",
    "log_to_stdout":"true",
    "mem_index_capacity":"65536",
    "optimize_interval":"10",
    "peer_server_connection_pool_size":"64",
    "postgres_port":"5433",
    "record_running_query":"false",
    "resource_dir":"/var/infinity/leader/resource",
    "server_address":"0.0.0.0",
    "temp_dir":"/var/infinity/leader/tmp",
    "time_zone":"UTC-8",
    "version":"0.6.0",
    "wal_compact_threshold":"1073741824",
    "wal_dir":"/var/infinity/leader/wal",
    "wal_flush":"FlushAtOnce"
}
```

---

### Admin show node variable

**GET** `/admin/variables/{variable_name}`

Retrieves the value of a global variable in admin mode.

#### Request

- Method: GET
- URL: `/admin/variables/{variable_name}`
- Headers: `accept: application/json`

##### Request example

```shell
curl --request GET \
     --url http://localhost:23820/admin/variables/{variable_name} \
     --header 'accept: application/json'
```

##### Request parameters

- `variable_name`: (*Path parameter*)  
  The name of the variable.

#### Response

<Tabs
  defaultValue="s200"
  values={[
    {label: 'Status code 200', value: 's200'},
    {label: 'Status code 500', value: 's500'},
  ]}>
  <TabItem value="s200">

The response includes a JSON object like the following:

```shell
{
    "error_code":0,
    "server_role":"admin"
}
```

- `"error_code"`: `integer`  
  `0`: The operation succeeds.

</TabItem>
  <TabItem value="s500">

The response includes a JSON object like the following:

```shell
{
    "error_code" : 3027,
    "error_message":"variable does not exist : role."
}
```

- `"error_code"`: `integer`  
  A non-zero value indicates a specific error condition.
- `"error_message"`: `string`  
  When `error_code` is non-zero, `"error_message"` provides additional details about the error.

</TabItem>
</Tabs>

---

### Admin show current node

**GET** `/admin/node/current`

Gets information about the currently connected node.

#### Request

- Method: GET
- URL: `/admin/node/current`
- Headers: `accept: application/json`

##### Request example

```shell
curl --request GET \
    --url http://localhost:23821/admin/node/current \
    --header 'accept: application/json'
```

##### Request parameters

None.

#### Response

##### Status code 200

The response includes a JSON object like the following:

```shell
{
    "error_code":0,
    "node_role":"learner"
}
```

- `"error_code"`: `integer`  
  `0`: The operation succeeds.
- `"node_role": string`
  The role of querying node.

---

### Admin show node

**GET** `/admin/node/{node_name}`

Retrieves information about a node in the cluster.

#### Request

- Method: GET
- URL: `/admin/node/{node_name}`
- Headers: `accept: application/json`

##### Request example

```shell
curl --request GET \
    --url http://localhost:23821/admin/node/learning \
    --header 'accept: application/json'
```

##### Request parameters

None.

#### Response
<Tabs
  defaultValue="s200"
  values={[
    {label: 'Status code 200', value: 's200'},
    {label: 'Status code 500', value: 's500'},
  ]}>
  <TabItem value="s200">

The response includes a JSON object like the following:

```shell
{
    "error_code":0,
    "node_role":"learner"
}
```

- `"error_code"`: `integer`  
  `0`: The operation succeeds.
- `"node_role": string`
  The role of querying node.

</TabItem>
  <TabItem value="s500">

A `500` HTTP status code indicates an error condition. The response includes a JSON object like the following:

```shell
{
    "error_code":7019,
    "error_msg":"Node doesn't exist: notlearning"
}
```

- `"error_code"`: `integer`  
  A non-zero value indicates a specific error condition.
- `"error_message"`: `string`  
  When `error_code` is non-zero, `"error_message"` provides additional details about the error.

</TabItem>
</Tabs>

---

### Admin list nodes

**GET** `/admin/nodes`

Lists all nodes in the cluster

#### Request

- Method: GET
- URL: `/admin/nodes`
- Headers: `accept: application/json`

##### Request example

```shell
curl --request GET \
    --url http://localhost:23822/admin/nodes \
    --header 'accept: application/json'
```

##### Request parameters

None.

#### Response

##### Status code 200

The response includes a JSON object like the following:

```shell
{
  "error_code": 0,
  "nodes": [
    {
      "address": "0.0.0.0:23852",
      "heartbeat": "91",
      "last_update": "Mon Dec  2 14:48:34 2024\n",
      "name": "follower",
      "role": "follower",
      "status": "alive"
    },
    {
      "address": "0.0.0.0:23851",
      "heartbeat": "0",
      "last_update": "Mon Dec  2 14:48:34 2024\n",
      "name": "boss",
      "role": "leader",
      "status": "alive"
    }
  ]
}
```

- `"error_code"`: `integer`
  `0`: The operation succeeds.
- `"nodes" : array` :  
  Each element is an object with following information:
    - `"address"` : The IP address of the node.
    - `"heartbeat"` : The number of heartbeat messages received from the leader. For non-leader roles only.
    - `"name"` : The node's name.
    - `"role"` : The node's role.
    - `"status"` : The node's status, this is one of `"invalid"`, `"alive"`, `"lostconnection"`, `"removed"`, `"timeout"`.
    - `"last_update"` : The last time that this node is updated.

---


