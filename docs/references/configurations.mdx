---
sidebar_position: 0
slug: /configurations
---

# Configurations
import Tabs from '@theme/Tabs';
import Tab<PERSON><PERSON> from '@theme/TabItem';

How to set and load configuration file when starting Infinity.

---

This document provides instructions for loading configuration file for Infinity and descriptions of each configuration entry.


## Load configuration file


<Tabs
  defaultValue="docker"
  values={[
    {label: 'Docker', value: 'docker'},
    {label: 'DEB/RPM', value: 'binary'},
  ]}>
  <TabItem value="docker">

To load a configuration file when you start up Infinity using Docker:

1. Mount the directory for the configuration file to inside the Docker container.
2. When running Docker, specify the configuration file path using the `-f` option. For example: 

   ```bash
   docker run -d --name infinity -v /var/infinity/:/var/infinity --ulimit nofile=500000:500000 --network=host infiniflow/infinity:nightly -f /var/infinity/infinity_conf.toml
   ```

</TabItem>
  <TabItem value="binary">

If you start up Infinity from DEB or RPM, the configuration file will be in the `/usr/etc` directory. When the Infinity service starts, it will load this configuration file.

</TabItem>
</Tabs>

## A configuration file example


```toml
[general]
# Infinity server version, the minor version of this parameter should match server minor version.
version                  = "0.6.0"
# Timezone
time_zone                = "utc-8"
# The number of worker threads. Defaults to the number of CPU cores.
# Range: [1, 16384]
cpu_limit                = 8
# The mode in which the server starts. Available options:
# - `"standalone"`: Start Infinity as a standalone server.
# - `"admin"`:
#   - Start Infinity either as a standalone server in `ADMIN` mode (when `storage_type` is set to `"local"`)
#   - Start Infinity as a cluster node in `ADMIN` mode (when `storage_type` is set to `"minio"`)
server_mode              = "standalone"

# Network configuration
[network]
# IP address of the Infinity server
server_address           = "0.0.0.0"

# Port of the Postgres server
postgres_port            = 5432
# Port of the HTTP server
http_port                = 23820
# Port of the Python SDK server
client_port              = 23817
# The maximum number of connections. Defaults to 256.
# Range: [1, 65536]
connection_pool_size     = 128
# The IP address on which the current node listens. Used for registration and inter-node communication
peer_ip                  = "0.0.0.0"
# The port number on which the current node listens. Used for registration and inter-node communication
peer_port                = 23850

# The delay time for reconnecting to the Infinity peer server after a failed connection
peer_retry_delay         = 0
# The maximum number of reconnection attempts to the Infinity peer server after a failed connection
peer_retry_count           = 0
# Timeout for connecting to the Infinity peer server
peer_connect_timeout     = 2000
# Timeout for the Infinity peer server to receive data
peer_recv_timeout        = 0
# Timeout for the Infinity peer server to send data
peer_send_timeout        = 0

# Log configuration
[log]
# Filename of the log files.
log_filename             = "infinity.log"
# Directory for the log files.
log_dir                  = "/var/infinity/log"
# Controls whether to print the log to the standard output. Defaults to true
# Range: {true|false} 
log_to_stdout            = true
# The maximum size of each log file. Defaults to "1GB".
# Range: at least 1GB
log_file_max_size        = "10GB"
# The number of rotating log files. Defaults to 8
# Range: [1, 655636]
log_file_rotate_count    = 10
# The log output level. Defaults to "info".
# Range: {"trace"/"debug"/"info"/"warning"/"error"/"critical"}
log_level               = "info"

# Storage configuration
[storage]
# The working directory of the persistence manager:
# - Holds the data that Infinity saves when using the local disk for storage.
# - Caches the accessed object files when using MinIO/S3 for object storage.
persistence_dir          = "/var/infinity/persistence"

# Garbage collection period
# Defined as "number + unit of time":
# - s for seconds (e.g., "60s" for 60 seconds),
# - m for minutes (e.g., "60m" for 60 minutes),
# - h for hours (e.g., "1h" for one hour).
# To disable a specific periodic task, set the corresponding variable to "0s"

# Interval between index optimization tasks
# Range: ["10s", "720h"]
optimize_interval        = "10s"
# Interval between cleanup tasks
# Range: ["10s", "720h"]
cleanup_interval         = "60s"
# Interval between compaction tasks
# Range: ["10s", "720h"]
compact_interval         = "120s"
# The threshold for flushing an in-memory index to disk.
# When the number of rows in the index exceeds this threshold,
# the system performs a flush operation on that index.
# Range: [8192, 8388608]
mem_index_capacity       = 65536
# The type of storage to use. Available options:
# - `"local"`: (default)
# - `"minio"`: If you set `server_mode` to `"admin"` and `storage_type` to `"minio"`, the node will start as a cluster node in `ADMIN` mode.
# Range: {"local"|"minio"}  
storage_type             = "local"

# The number of dense vector index building worker threads. Defaults to the half number of CPU cores.
# Range: [1, number of CPU cores]
dense_index_building_worker = 2

# The number of sparse vector index building worker threads. Defaults to the half number of CPU cores.
# Range: [1, number of CPU cores]
sparse_index_building_worker = 2

# The number of fulltext index building worker threads. Defaults to the half number of CPU cores.
# Range: [1, number of CPU cores]
fulltext_index_building_worker = 2

# Object storage configuration
[storage.object_storage]
# URL of the object storage server
url                      = "127.0.0.1:9000"
# The name of bucket where the data is stored
bucket_name              = "infinity"
# Access key of object storage server
access_key               = "minioadmin"
# Secret key of object storage server
secret_key               = "minioadmin"
# Whether to enable HTTP or HTTPS protocol
enable_https             = false

# Buffer manager configuration
[buffer]
# The amount of memory occupied by the buffer manager. Defaults to "8GB".
buffer_manager_size      = "4GB"
# The number of LRU caches in the buffer manager.
# Range: [1, 100]
lru_num                  = 7
# When the required memory size exceeds the free memory in the buffer manager,
# the buffer manager dumps some of the its in-use memory to a temporary storage.
# `temp_dir` specifies the path to this temporary storage. 
temp_dir                 = "/var/infinity/tmp"
# The global memory index threshold for triggering a flush to disk
# When the memory used by all existing in-memory indices in the system exceeds this threshold,
# the system will perform a flush operation on all in-memory indices.
memindex_memory_quota   = "1GB"

# If cache the query result.
# If same query is sent to Infinity, Infinity will check and return the cached result.
result_cache            = "on"

# Cache query capacity
cache_result_capacity        = 100

# WAL configuration
[wal]
# The directory containing the WAL files
wal_dir                       = "/var/infinity/wal"
# Interval between checkpoints
# Range: ["0s", "720h"]
checkpoint_interval      = "86400s"

# Size threshold for triggering a compaction on a WAL file
# When the size of a WAL file exceeds this threshold, the system will perform compaction.
# Range: ["1KB", "1024GB"]
wal_compact_threshold         = "1GB"

# Controls how the WAL manager flushes its logs to disk
# - "only_write": (Default) Writes logs but leaves it to the OS to determine when to flush them.
# - "flush_at_once": Writes and flushes logs upon each commit.
# - "flush_per_second": Writes logs after each commit and flushes them to disk once per second.
wal_flush                     = "only_write"

[resource]
# Directory for Infinity's resource files, including the dictionary files used by the analyzer
resource_dir                  = "/var/infinity/resource"
```