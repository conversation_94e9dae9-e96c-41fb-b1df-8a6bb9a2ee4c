---
sidebar_position: 3
slug: /deploy_infinity_server
---

# Deploy Infinity using binary
import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';

Deploy the Infinity database using binary.

---

This document provides guidance on deploying the Infinity database using binary, with the client and server as separate processes. 

## Prerequisites

- CPU: x86_64 with AVX2 support.
- OS: 
  - Linux with glibc 2.17+.
  - Windows 10+ with WSL/WSL2.

## Install Infinity server

This section provides instructions on deploying Infinity using binary package on Linux x86_64. You can download the binary package (deb, rpm, or tgz) for your Linux system from https://github.com/infiniflow/infinity/releases. The prebuilt packages are compatible with Linux distributions based on glibc 2.17 or later, for example, RHEL 7, Debian 8, Ubuntu 14.04.


<Tabs
  defaultValue="fedora"
  values={[
    {label: 'Fedora/RHEL/CentOS/OpenSUSE', value: 'fedora'},
    {label: 'Ubuntu/Debian', value: 'ubuntu'},
  ]}>
   <TabItem value="fedora">

Fedora/RHEL/CentOS/OpenSUSE
```bash
sudo rpm -i infinity-0.6.0.dev3-x86_64.rpm
```

```bash
sudo systemctl start infinity
```

  </TabItem>
  <TabItem value="ubuntu">

```bash
sudo dpkg -i infinity-0.6.0.dev3-x86_64.deb
```

```bash
sudo systemctl start infinity
```

  </TabItem>
</Tabs>

## Install Infinity client

```
pip install infinity-sdk==0.6.0.dev3
```

## Run a vector search

```python
import infinity

infinity_object = infinity.connect(infinity.NetworkAddress("<SERVER_IP_ADDRESS>", 23817)) 
db_object = infinity_object.get_database("default_db")
table_object = db_object.create_table("my_table", {"num": {"type": "integer"}, "body": {"type": "varchar"}, "vec": {"type": "vector, 4, float"}})
table_object.insert([{"num": 1, "body": "unnecessary and harmful", "vec": [1.0, 1.2, 0.8, 0.9]}])
table_object.insert([{"num": 2, "body": "Office for Harmful Blooms", "vec": [4.0, 4.2, 4.3, 4.5]}])
res = table_object.output(["*"])
                  .match_dense("vec", [3.0, 2.8, 2.7, 3.1], "float", "ip", 2)
                  .to_pl()
print(res)
```

:::tip NOTE
For detailed information about the capabilities and usage of Infinity's Python API, see the [Python API Reference](../references/pysdk_api_reference.md).
:::

