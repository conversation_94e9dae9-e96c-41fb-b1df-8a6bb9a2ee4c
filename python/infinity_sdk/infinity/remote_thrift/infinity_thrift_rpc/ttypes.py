#
# Autogenerated by Thrift Compiler (0.22.0)
#
# DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
#
#  options string: py
#

from thrift.Thrift import TType, TMessageType, TFrozenDict, TException, TApplicationException
from thrift.protocol.TProtocol import TProtocolException
from thrift.TRecursive import fix_spec
from uuid import UUID

import sys

from thrift.transport import TTransport
all_structs = []


class LogicType(object):
    Boolean = 0
    TinyInt = 1
    SmallInt = 2
    Integer = 3
    BigInt = 4
    HugeInt = 5
    Decimal = 6
    Float = 7
    Double = 8
    Float16 = 9
    BFloat16 = 10
    Varchar = 11
    Embedding = 12
    Tensor = 13
    TensorArray = 14
    Sparse = 15
    MultiVector = 16
    Date = 17
    Time = 18
    DateTime = 19
    Timestamp = 20
    Interval = 21
    Array = 22
    Invalid = 23

    _VALUES_TO_NAMES = {
        0: "Boolean",
        1: "TinyInt",
        2: "SmallInt",
        3: "Integer",
        4: "BigInt",
        5: "HugeInt",
        6: "Decimal",
        7: "Float",
        8: "Double",
        9: "Float16",
        10: "BFloat16",
        11: "Varchar",
        12: "Embedding",
        13: "Tensor",
        14: "TensorArray",
        15: "Sparse",
        16: "MultiVector",
        17: "Date",
        18: "Time",
        19: "DateTime",
        20: "Timestamp",
        21: "Interval",
        22: "Array",
        23: "Invalid",
    }

    _NAMES_TO_VALUES = {
        "Boolean": 0,
        "TinyInt": 1,
        "SmallInt": 2,
        "Integer": 3,
        "BigInt": 4,
        "HugeInt": 5,
        "Decimal": 6,
        "Float": 7,
        "Double": 8,
        "Float16": 9,
        "BFloat16": 10,
        "Varchar": 11,
        "Embedding": 12,
        "Tensor": 13,
        "TensorArray": 14,
        "Sparse": 15,
        "MultiVector": 16,
        "Date": 17,
        "Time": 18,
        "DateTime": 19,
        "Timestamp": 20,
        "Interval": 21,
        "Array": 22,
        "Invalid": 23,
    }


class CreateConflict(object):
    Ignore = 0
    Error = 1
    Replace = 2

    _VALUES_TO_NAMES = {
        0: "Ignore",
        1: "Error",
        2: "Replace",
    }

    _NAMES_TO_VALUES = {
        "Ignore": 0,
        "Error": 1,
        "Replace": 2,
    }


class DropConflict(object):
    Ignore = 0
    Error = 1

    _VALUES_TO_NAMES = {
        0: "Ignore",
        1: "Error",
    }

    _NAMES_TO_VALUES = {
        "Ignore": 0,
        "Error": 1,
    }


class ElementType(object):
    ElementBit = 0
    ElementUInt8 = 1
    ElementInt8 = 2
    ElementInt16 = 3
    ElementInt32 = 4
    ElementInt64 = 5
    ElementFloat32 = 6
    ElementFloat64 = 7
    ElementFloat16 = 8
    ElementBFloat16 = 9

    _VALUES_TO_NAMES = {
        0: "ElementBit",
        1: "ElementUInt8",
        2: "ElementInt8",
        3: "ElementInt16",
        4: "ElementInt32",
        5: "ElementInt64",
        6: "ElementFloat32",
        7: "ElementFloat64",
        8: "ElementFloat16",
        9: "ElementBFloat16",
    }

    _NAMES_TO_VALUES = {
        "ElementBit": 0,
        "ElementUInt8": 1,
        "ElementInt8": 2,
        "ElementInt16": 3,
        "ElementInt32": 4,
        "ElementInt64": 5,
        "ElementFloat32": 6,
        "ElementFloat64": 7,
        "ElementFloat16": 8,
        "ElementBFloat16": 9,
    }


class Constraint(object):
    PrimaryKey = 0
    NotNull = 1
    Null = 2
    Unique = 3

    _VALUES_TO_NAMES = {
        0: "PrimaryKey",
        1: "NotNull",
        2: "Null",
        3: "Unique",
    }

    _NAMES_TO_VALUES = {
        "PrimaryKey": 0,
        "NotNull": 1,
        "Null": 2,
        "Unique": 3,
    }


class LiteralType(object):
    Boolean = 0
    Double = 1
    String = 2
    Int64 = 3
    Null = 4
    IntegerArray = 5
    DoubleArray = 6
    IntegerTensor = 7
    DoubleTensor = 8
    IntegerTensorArray = 9
    DoubleTensorArray = 10
    SparseIntegerArray = 11
    SparseDoubleArray = 12
    Date = 13
    Time = 14
    Inteval = 15
    DateTime = 16
    Timestamp = 17
    CurlyBracketsArray = 18

    _VALUES_TO_NAMES = {
        0: "Boolean",
        1: "Double",
        2: "String",
        3: "Int64",
        4: "Null",
        5: "IntegerArray",
        6: "DoubleArray",
        7: "IntegerTensor",
        8: "DoubleTensor",
        9: "IntegerTensorArray",
        10: "DoubleTensorArray",
        11: "SparseIntegerArray",
        12: "SparseDoubleArray",
        13: "Date",
        14: "Time",
        15: "Inteval",
        16: "DateTime",
        17: "Timestamp",
        18: "CurlyBracketsArray",
    }

    _NAMES_TO_VALUES = {
        "Boolean": 0,
        "Double": 1,
        "String": 2,
        "Int64": 3,
        "Null": 4,
        "IntegerArray": 5,
        "DoubleArray": 6,
        "IntegerTensor": 7,
        "DoubleTensor": 8,
        "IntegerTensorArray": 9,
        "DoubleTensorArray": 10,
        "SparseIntegerArray": 11,
        "SparseDoubleArray": 12,
        "Date": 13,
        "Time": 14,
        "Inteval": 15,
        "DateTime": 16,
        "Timestamp": 17,
        "CurlyBracketsArray": 18,
    }


class KnnDistanceType(object):
    L2 = 0
    Cosine = 1
    InnerProduct = 2
    Hamming = 3

    _VALUES_TO_NAMES = {
        0: "L2",
        1: "Cosine",
        2: "InnerProduct",
        3: "Hamming",
    }

    _NAMES_TO_VALUES = {
        "L2": 0,
        "Cosine": 1,
        "InnerProduct": 2,
        "Hamming": 3,
    }


class CopyFileType(object):
    CSV = 0
    JSON = 1
    JSONL = 2
    FVECS = 3
    CSR = 4
    BVECS = 5

    _VALUES_TO_NAMES = {
        0: "CSV",
        1: "JSON",
        2: "JSONL",
        3: "FVECS",
        4: "CSR",
        5: "BVECS",
    }

    _NAMES_TO_VALUES = {
        "CSV": 0,
        "JSON": 1,
        "JSONL": 2,
        "FVECS": 3,
        "CSR": 4,
        "BVECS": 5,
    }


class ColumnType(object):
    ColumnBool = 0
    ColumnInt8 = 1
    ColumnInt16 = 2
    ColumnInt32 = 3
    ColumnInt64 = 4
    ColumnFloat32 = 5
    ColumnFloat64 = 6
    ColumnFloat16 = 7
    ColumnBFloat16 = 8
    ColumnVarchar = 9
    ColumnEmbedding = 10
    ColumnTensor = 11
    ColumnTensorArray = 12
    ColumnSparse = 13
    ColumnMultiVector = 14
    ColumnRowID = 15
    ColumnDate = 16
    ColumnTime = 17
    ColumnDateTime = 18
    ColumnTimestamp = 19
    ColumnInterval = 20
    ColumnArray = 21
    ColumnInvalid = 22

    _VALUES_TO_NAMES = {
        0: "ColumnBool",
        1: "ColumnInt8",
        2: "ColumnInt16",
        3: "ColumnInt32",
        4: "ColumnInt64",
        5: "ColumnFloat32",
        6: "ColumnFloat64",
        7: "ColumnFloat16",
        8: "ColumnBFloat16",
        9: "ColumnVarchar",
        10: "ColumnEmbedding",
        11: "ColumnTensor",
        12: "ColumnTensorArray",
        13: "ColumnSparse",
        14: "ColumnMultiVector",
        15: "ColumnRowID",
        16: "ColumnDate",
        17: "ColumnTime",
        18: "ColumnDateTime",
        19: "ColumnTimestamp",
        20: "ColumnInterval",
        21: "ColumnArray",
        22: "ColumnInvalid",
    }

    _NAMES_TO_VALUES = {
        "ColumnBool": 0,
        "ColumnInt8": 1,
        "ColumnInt16": 2,
        "ColumnInt32": 3,
        "ColumnInt64": 4,
        "ColumnFloat32": 5,
        "ColumnFloat64": 6,
        "ColumnFloat16": 7,
        "ColumnBFloat16": 8,
        "ColumnVarchar": 9,
        "ColumnEmbedding": 10,
        "ColumnTensor": 11,
        "ColumnTensorArray": 12,
        "ColumnSparse": 13,
        "ColumnMultiVector": 14,
        "ColumnRowID": 15,
        "ColumnDate": 16,
        "ColumnTime": 17,
        "ColumnDateTime": 18,
        "ColumnTimestamp": 19,
        "ColumnInterval": 20,
        "ColumnArray": 21,
        "ColumnInvalid": 22,
    }


class IndexType(object):
    IVF = 0
    Hnsw = 1
    FullText = 2
    BMP = 3
    Secondary = 4
    EMVB = 5
    DiskAnn = 6

    _VALUES_TO_NAMES = {
        0: "IVF",
        1: "Hnsw",
        2: "FullText",
        3: "BMP",
        4: "Secondary",
        5: "EMVB",
        6: "DiskAnn",
    }

    _NAMES_TO_VALUES = {
        "IVF": 0,
        "Hnsw": 1,
        "FullText": 2,
        "BMP": 3,
        "Secondary": 4,
        "EMVB": 5,
        "DiskAnn": 6,
    }


class ExplainType(object):
    Analyze = 0
    Ast = 1
    UnOpt = 2
    Opt = 3
    Physical = 4
    Pipeline = 5
    Fragment = 6

    _VALUES_TO_NAMES = {
        0: "Analyze",
        1: "Ast",
        2: "UnOpt",
        3: "Opt",
        4: "Physical",
        5: "Pipeline",
        6: "Fragment",
    }

    _NAMES_TO_VALUES = {
        "Analyze": 0,
        "Ast": 1,
        "UnOpt": 2,
        "Opt": 3,
        "Physical": 4,
        "Pipeline": 5,
        "Fragment": 6,
    }


class Property(object):
    """
    Attributes:
     - key
     - value

    """
    thrift_spec = None


    def __init__(self, key = None, value = None,):
        self.key = key
        self.value = value

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.STRING:
                    self.key = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRING:
                    self.value = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('Property')
        if self.key is not None:
            oprot.writeFieldBegin('key', TType.STRING, 1)
            oprot.writeString(self.key.encode('utf-8') if sys.version_info[0] == 2 else self.key)
            oprot.writeFieldEnd()
        if self.value is not None:
            oprot.writeFieldBegin('value', TType.STRING, 2)
            oprot.writeString(self.value.encode('utf-8') if sys.version_info[0] == 2 else self.value)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class CreateOption(object):
    """
    Attributes:
     - conflict_type
     - properties

    """
    thrift_spec = None


    def __init__(self, conflict_type = None, properties = [
    ],):
        self.conflict_type = conflict_type
        if properties is self.thrift_spec[2][4]:
            properties = [
            ]
        self.properties = properties

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.I32:
                    self.conflict_type = iprot.readI32()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.LIST:
                    self.properties = []
                    (_etype3, _size0) = iprot.readListBegin()
                    for _i4 in range(_size0):
                        _elem5 = Property()
                        _elem5.read(iprot)
                        self.properties.append(_elem5)
                    iprot.readListEnd()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('CreateOption')
        if self.conflict_type is not None:
            oprot.writeFieldBegin('conflict_type', TType.I32, 1)
            oprot.writeI32(self.conflict_type)
            oprot.writeFieldEnd()
        if self.properties is not None:
            oprot.writeFieldBegin('properties', TType.LIST, 2)
            oprot.writeListBegin(TType.STRUCT, len(self.properties))
            for iter6 in self.properties:
                iter6.write(oprot)
            oprot.writeListEnd()
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class DropOption(object):
    """
    Attributes:
     - conflict_type

    """
    thrift_spec = None


    def __init__(self, conflict_type = None,):
        self.conflict_type = conflict_type

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.I32:
                    self.conflict_type = iprot.readI32()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('DropOption')
        if self.conflict_type is not None:
            oprot.writeFieldBegin('conflict_type', TType.I32, 1)
            oprot.writeI32(self.conflict_type)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class NumberType(object):
    thrift_spec = None


    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('NumberType')
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class VarcharType(object):
    thrift_spec = None


    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('VarcharType')
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class EmbeddingType(object):
    """
    Attributes:
     - dimension
     - element_type

    """
    thrift_spec = None


    def __init__(self, dimension = None, element_type = None,):
        self.dimension = dimension
        self.element_type = element_type

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.I32:
                    self.dimension = iprot.readI32()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.I32:
                    self.element_type = iprot.readI32()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('EmbeddingType')
        if self.dimension is not None:
            oprot.writeFieldBegin('dimension', TType.I32, 1)
            oprot.writeI32(self.dimension)
            oprot.writeFieldEnd()
        if self.element_type is not None:
            oprot.writeFieldBegin('element_type', TType.I32, 2)
            oprot.writeI32(self.element_type)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class SparseType(object):
    """
    Attributes:
     - dimension
     - element_type
     - index_type

    """
    thrift_spec = None


    def __init__(self, dimension = None, element_type = None, index_type = None,):
        self.dimension = dimension
        self.element_type = element_type
        self.index_type = index_type

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.I64:
                    self.dimension = iprot.readI64()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.I32:
                    self.element_type = iprot.readI32()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.I32:
                    self.index_type = iprot.readI32()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('SparseType')
        if self.dimension is not None:
            oprot.writeFieldBegin('dimension', TType.I64, 1)
            oprot.writeI64(self.dimension)
            oprot.writeFieldEnd()
        if self.element_type is not None:
            oprot.writeFieldBegin('element_type', TType.I32, 2)
            oprot.writeI32(self.element_type)
            oprot.writeFieldEnd()
        if self.index_type is not None:
            oprot.writeFieldBegin('index_type', TType.I32, 3)
            oprot.writeI32(self.index_type)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class ArrayType(object):
    """
    Attributes:
     - element_data_type

    """
    thrift_spec = None


    def __init__(self, element_data_type = None,):
        self.element_data_type = element_data_type

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.STRUCT:
                    self.element_data_type = DataType()
                    self.element_data_type.read(iprot)
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('ArrayType')
        if self.element_data_type is not None:
            oprot.writeFieldBegin('element_data_type', TType.STRUCT, 1)
            self.element_data_type.write(oprot)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class PhysicalType(object):
    """
    Attributes:
     - number_type
     - varchar_type
     - embedding_type
     - sparse_type
     - array_type

    """
    thrift_spec = None


    def __init__(self, number_type = None, varchar_type = None, embedding_type = None, sparse_type = None, array_type = None,):
        self.number_type = number_type
        self.varchar_type = varchar_type
        self.embedding_type = embedding_type
        self.sparse_type = sparse_type
        self.array_type = array_type

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.STRUCT:
                    self.number_type = NumberType()
                    self.number_type.read(iprot)
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRUCT:
                    self.varchar_type = VarcharType()
                    self.varchar_type.read(iprot)
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.STRUCT:
                    self.embedding_type = EmbeddingType()
                    self.embedding_type.read(iprot)
                else:
                    iprot.skip(ftype)
            elif fid == 4:
                if ftype == TType.STRUCT:
                    self.sparse_type = SparseType()
                    self.sparse_type.read(iprot)
                else:
                    iprot.skip(ftype)
            elif fid == 5:
                if ftype == TType.STRUCT:
                    self.array_type = ArrayType()
                    self.array_type.read(iprot)
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('PhysicalType')
        if self.number_type is not None:
            oprot.writeFieldBegin('number_type', TType.STRUCT, 1)
            self.number_type.write(oprot)
            oprot.writeFieldEnd()
        if self.varchar_type is not None:
            oprot.writeFieldBegin('varchar_type', TType.STRUCT, 2)
            self.varchar_type.write(oprot)
            oprot.writeFieldEnd()
        if self.embedding_type is not None:
            oprot.writeFieldBegin('embedding_type', TType.STRUCT, 3)
            self.embedding_type.write(oprot)
            oprot.writeFieldEnd()
        if self.sparse_type is not None:
            oprot.writeFieldBegin('sparse_type', TType.STRUCT, 4)
            self.sparse_type.write(oprot)
            oprot.writeFieldEnd()
        if self.array_type is not None:
            oprot.writeFieldBegin('array_type', TType.STRUCT, 5)
            self.array_type.write(oprot)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class DataType(object):
    """
    Attributes:
     - logic_type
     - physical_type

    """
    thrift_spec = None


    def __init__(self, logic_type = None, physical_type = None,):
        self.logic_type = logic_type
        self.physical_type = physical_type

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.I32:
                    self.logic_type = iprot.readI32()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRUCT:
                    self.physical_type = PhysicalType()
                    self.physical_type.read(iprot)
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('DataType')
        if self.logic_type is not None:
            oprot.writeFieldBegin('logic_type', TType.I32, 1)
            oprot.writeI32(self.logic_type)
            oprot.writeFieldEnd()
        if self.physical_type is not None:
            oprot.writeFieldBegin('physical_type', TType.STRUCT, 2)
            self.physical_type.write(oprot)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class ParsedExprType(object):
    """
    Attributes:
     - constant_expr
     - column_expr
     - function_expr
     - between_expr
     - knn_expr
     - match_sparse_expr
     - match_tensor_expr
     - match_expr
     - fusion_expr
     - search_expr
     - in_expr

    """
    thrift_spec = None


    def __init__(self, constant_expr = None, column_expr = None, function_expr = None, between_expr = None, knn_expr = None, match_sparse_expr = None, match_tensor_expr = None, match_expr = None, fusion_expr = None, search_expr = None, in_expr = None,):
        self.constant_expr = constant_expr
        self.column_expr = column_expr
        self.function_expr = function_expr
        self.between_expr = between_expr
        self.knn_expr = knn_expr
        self.match_sparse_expr = match_sparse_expr
        self.match_tensor_expr = match_tensor_expr
        self.match_expr = match_expr
        self.fusion_expr = fusion_expr
        self.search_expr = search_expr
        self.in_expr = in_expr

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.STRUCT:
                    self.constant_expr = ConstantExpr()
                    self.constant_expr.read(iprot)
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRUCT:
                    self.column_expr = ColumnExpr()
                    self.column_expr.read(iprot)
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.STRUCT:
                    self.function_expr = FunctionExpr()
                    self.function_expr.read(iprot)
                else:
                    iprot.skip(ftype)
            elif fid == 4:
                if ftype == TType.STRUCT:
                    self.between_expr = BetweenExpr()
                    self.between_expr.read(iprot)
                else:
                    iprot.skip(ftype)
            elif fid == 5:
                if ftype == TType.STRUCT:
                    self.knn_expr = KnnExpr()
                    self.knn_expr.read(iprot)
                else:
                    iprot.skip(ftype)
            elif fid == 6:
                if ftype == TType.STRUCT:
                    self.match_sparse_expr = MatchSparseExpr()
                    self.match_sparse_expr.read(iprot)
                else:
                    iprot.skip(ftype)
            elif fid == 7:
                if ftype == TType.STRUCT:
                    self.match_tensor_expr = MatchTensorExpr()
                    self.match_tensor_expr.read(iprot)
                else:
                    iprot.skip(ftype)
            elif fid == 8:
                if ftype == TType.STRUCT:
                    self.match_expr = MatchExpr()
                    self.match_expr.read(iprot)
                else:
                    iprot.skip(ftype)
            elif fid == 9:
                if ftype == TType.STRUCT:
                    self.fusion_expr = FusionExpr()
                    self.fusion_expr.read(iprot)
                else:
                    iprot.skip(ftype)
            elif fid == 10:
                if ftype == TType.STRUCT:
                    self.search_expr = SearchExpr()
                    self.search_expr.read(iprot)
                else:
                    iprot.skip(ftype)
            elif fid == 11:
                if ftype == TType.STRUCT:
                    self.in_expr = InExpr()
                    self.in_expr.read(iprot)
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('ParsedExprType')
        if self.constant_expr is not None:
            oprot.writeFieldBegin('constant_expr', TType.STRUCT, 1)
            self.constant_expr.write(oprot)
            oprot.writeFieldEnd()
        if self.column_expr is not None:
            oprot.writeFieldBegin('column_expr', TType.STRUCT, 2)
            self.column_expr.write(oprot)
            oprot.writeFieldEnd()
        if self.function_expr is not None:
            oprot.writeFieldBegin('function_expr', TType.STRUCT, 3)
            self.function_expr.write(oprot)
            oprot.writeFieldEnd()
        if self.between_expr is not None:
            oprot.writeFieldBegin('between_expr', TType.STRUCT, 4)
            self.between_expr.write(oprot)
            oprot.writeFieldEnd()
        if self.knn_expr is not None:
            oprot.writeFieldBegin('knn_expr', TType.STRUCT, 5)
            self.knn_expr.write(oprot)
            oprot.writeFieldEnd()
        if self.match_sparse_expr is not None:
            oprot.writeFieldBegin('match_sparse_expr', TType.STRUCT, 6)
            self.match_sparse_expr.write(oprot)
            oprot.writeFieldEnd()
        if self.match_tensor_expr is not None:
            oprot.writeFieldBegin('match_tensor_expr', TType.STRUCT, 7)
            self.match_tensor_expr.write(oprot)
            oprot.writeFieldEnd()
        if self.match_expr is not None:
            oprot.writeFieldBegin('match_expr', TType.STRUCT, 8)
            self.match_expr.write(oprot)
            oprot.writeFieldEnd()
        if self.fusion_expr is not None:
            oprot.writeFieldBegin('fusion_expr', TType.STRUCT, 9)
            self.fusion_expr.write(oprot)
            oprot.writeFieldEnd()
        if self.search_expr is not None:
            oprot.writeFieldBegin('search_expr', TType.STRUCT, 10)
            self.search_expr.write(oprot)
            oprot.writeFieldEnd()
        if self.in_expr is not None:
            oprot.writeFieldBegin('in_expr', TType.STRUCT, 11)
            self.in_expr.write(oprot)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class ParsedExpr(object):
    """
    Attributes:
     - type
     - alias_name

    """
    thrift_spec = None


    def __init__(self, type = None, alias_name = None,):
        self.type = type
        self.alias_name = alias_name

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.STRUCT:
                    self.type = ParsedExprType()
                    self.type.read(iprot)
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRING:
                    self.alias_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('ParsedExpr')
        if self.type is not None:
            oprot.writeFieldBegin('type', TType.STRUCT, 1)
            self.type.write(oprot)
            oprot.writeFieldEnd()
        if self.alias_name is not None:
            oprot.writeFieldBegin('alias_name', TType.STRING, 2)
            oprot.writeString(self.alias_name.encode('utf-8') if sys.version_info[0] == 2 else self.alias_name)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class ColumnExpr(object):
    """
    Attributes:
     - column_name
     - star

    """
    thrift_spec = None


    def __init__(self, column_name = [
    ], star = None,):
        if column_name is self.thrift_spec[1][4]:
            column_name = [
            ]
        self.column_name = column_name
        self.star = star

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.LIST:
                    self.column_name = []
                    (_etype10, _size7) = iprot.readListBegin()
                    for _i11 in range(_size7):
                        _elem12 = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                        self.column_name.append(_elem12)
                    iprot.readListEnd()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.BOOL:
                    self.star = iprot.readBool()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('ColumnExpr')
        if self.column_name is not None:
            oprot.writeFieldBegin('column_name', TType.LIST, 1)
            oprot.writeListBegin(TType.STRING, len(self.column_name))
            for iter13 in self.column_name:
                oprot.writeString(iter13.encode('utf-8') if sys.version_info[0] == 2 else iter13)
            oprot.writeListEnd()
            oprot.writeFieldEnd()
        if self.star is not None:
            oprot.writeFieldBegin('star', TType.BOOL, 2)
            oprot.writeBool(self.star)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class EmbeddingData(object):
    """
    Attributes:
     - bool_array_value
     - u8_array_value
     - i8_array_value
     - i16_array_value
     - i32_array_value
     - i64_array_value
     - f32_array_value
     - f64_array_value
     - f16_array_value
     - bf16_array_value

    """
    thrift_spec = None


    def __init__(self, bool_array_value = None, u8_array_value = None, i8_array_value = None, i16_array_value = None, i32_array_value = None, i64_array_value = None, f32_array_value = None, f64_array_value = None, f16_array_value = None, bf16_array_value = None,):
        self.bool_array_value = bool_array_value
        self.u8_array_value = u8_array_value
        self.i8_array_value = i8_array_value
        self.i16_array_value = i16_array_value
        self.i32_array_value = i32_array_value
        self.i64_array_value = i64_array_value
        self.f32_array_value = f32_array_value
        self.f64_array_value = f64_array_value
        self.f16_array_value = f16_array_value
        self.bf16_array_value = bf16_array_value

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.LIST:
                    self.bool_array_value = []
                    (_etype17, _size14) = iprot.readListBegin()
                    for _i18 in range(_size14):
                        _elem19 = iprot.readBool()
                        self.bool_array_value.append(_elem19)
                    iprot.readListEnd()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.LIST:
                    self.u8_array_value = []
                    (_etype23, _size20) = iprot.readListBegin()
                    for _i24 in range(_size20):
                        _elem25 = iprot.readI16()
                        self.u8_array_value.append(_elem25)
                    iprot.readListEnd()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.LIST:
                    self.i8_array_value = []
                    (_etype29, _size26) = iprot.readListBegin()
                    for _i30 in range(_size26):
                        _elem31 = iprot.readI16()
                        self.i8_array_value.append(_elem31)
                    iprot.readListEnd()
                else:
                    iprot.skip(ftype)
            elif fid == 4:
                if ftype == TType.LIST:
                    self.i16_array_value = []
                    (_etype35, _size32) = iprot.readListBegin()
                    for _i36 in range(_size32):
                        _elem37 = iprot.readI16()
                        self.i16_array_value.append(_elem37)
                    iprot.readListEnd()
                else:
                    iprot.skip(ftype)
            elif fid == 5:
                if ftype == TType.LIST:
                    self.i32_array_value = []
                    (_etype41, _size38) = iprot.readListBegin()
                    for _i42 in range(_size38):
                        _elem43 = iprot.readI32()
                        self.i32_array_value.append(_elem43)
                    iprot.readListEnd()
                else:
                    iprot.skip(ftype)
            elif fid == 6:
                if ftype == TType.LIST:
                    self.i64_array_value = []
                    (_etype47, _size44) = iprot.readListBegin()
                    for _i48 in range(_size44):
                        _elem49 = iprot.readI64()
                        self.i64_array_value.append(_elem49)
                    iprot.readListEnd()
                else:
                    iprot.skip(ftype)
            elif fid == 7:
                if ftype == TType.LIST:
                    self.f32_array_value = []
                    (_etype53, _size50) = iprot.readListBegin()
                    for _i54 in range(_size50):
                        _elem55 = iprot.readDouble()
                        self.f32_array_value.append(_elem55)
                    iprot.readListEnd()
                else:
                    iprot.skip(ftype)
            elif fid == 8:
                if ftype == TType.LIST:
                    self.f64_array_value = []
                    (_etype59, _size56) = iprot.readListBegin()
                    for _i60 in range(_size56):
                        _elem61 = iprot.readDouble()
                        self.f64_array_value.append(_elem61)
                    iprot.readListEnd()
                else:
                    iprot.skip(ftype)
            elif fid == 9:
                if ftype == TType.LIST:
                    self.f16_array_value = []
                    (_etype65, _size62) = iprot.readListBegin()
                    for _i66 in range(_size62):
                        _elem67 = iprot.readDouble()
                        self.f16_array_value.append(_elem67)
                    iprot.readListEnd()
                else:
                    iprot.skip(ftype)
            elif fid == 10:
                if ftype == TType.LIST:
                    self.bf16_array_value = []
                    (_etype71, _size68) = iprot.readListBegin()
                    for _i72 in range(_size68):
                        _elem73 = iprot.readDouble()
                        self.bf16_array_value.append(_elem73)
                    iprot.readListEnd()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('EmbeddingData')
        if self.bool_array_value is not None:
            oprot.writeFieldBegin('bool_array_value', TType.LIST, 1)
            oprot.writeListBegin(TType.BOOL, len(self.bool_array_value))
            for iter74 in self.bool_array_value:
                oprot.writeBool(iter74)
            oprot.writeListEnd()
            oprot.writeFieldEnd()
        if self.u8_array_value is not None:
            oprot.writeFieldBegin('u8_array_value', TType.LIST, 2)
            oprot.writeListBegin(TType.I16, len(self.u8_array_value))
            for iter75 in self.u8_array_value:
                oprot.writeI16(iter75)
            oprot.writeListEnd()
            oprot.writeFieldEnd()
        if self.i8_array_value is not None:
            oprot.writeFieldBegin('i8_array_value', TType.LIST, 3)
            oprot.writeListBegin(TType.I16, len(self.i8_array_value))
            for iter76 in self.i8_array_value:
                oprot.writeI16(iter76)
            oprot.writeListEnd()
            oprot.writeFieldEnd()
        if self.i16_array_value is not None:
            oprot.writeFieldBegin('i16_array_value', TType.LIST, 4)
            oprot.writeListBegin(TType.I16, len(self.i16_array_value))
            for iter77 in self.i16_array_value:
                oprot.writeI16(iter77)
            oprot.writeListEnd()
            oprot.writeFieldEnd()
        if self.i32_array_value is not None:
            oprot.writeFieldBegin('i32_array_value', TType.LIST, 5)
            oprot.writeListBegin(TType.I32, len(self.i32_array_value))
            for iter78 in self.i32_array_value:
                oprot.writeI32(iter78)
            oprot.writeListEnd()
            oprot.writeFieldEnd()
        if self.i64_array_value is not None:
            oprot.writeFieldBegin('i64_array_value', TType.LIST, 6)
            oprot.writeListBegin(TType.I64, len(self.i64_array_value))
            for iter79 in self.i64_array_value:
                oprot.writeI64(iter79)
            oprot.writeListEnd()
            oprot.writeFieldEnd()
        if self.f32_array_value is not None:
            oprot.writeFieldBegin('f32_array_value', TType.LIST, 7)
            oprot.writeListBegin(TType.DOUBLE, len(self.f32_array_value))
            for iter80 in self.f32_array_value:
                oprot.writeDouble(iter80)
            oprot.writeListEnd()
            oprot.writeFieldEnd()
        if self.f64_array_value is not None:
            oprot.writeFieldBegin('f64_array_value', TType.LIST, 8)
            oprot.writeListBegin(TType.DOUBLE, len(self.f64_array_value))
            for iter81 in self.f64_array_value:
                oprot.writeDouble(iter81)
            oprot.writeListEnd()
            oprot.writeFieldEnd()
        if self.f16_array_value is not None:
            oprot.writeFieldBegin('f16_array_value', TType.LIST, 9)
            oprot.writeListBegin(TType.DOUBLE, len(self.f16_array_value))
            for iter82 in self.f16_array_value:
                oprot.writeDouble(iter82)
            oprot.writeListEnd()
            oprot.writeFieldEnd()
        if self.bf16_array_value is not None:
            oprot.writeFieldBegin('bf16_array_value', TType.LIST, 10)
            oprot.writeListBegin(TType.DOUBLE, len(self.bf16_array_value))
            for iter83 in self.bf16_array_value:
                oprot.writeDouble(iter83)
            oprot.writeListEnd()
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class InitParameter(object):
    """
    Attributes:
     - param_name
     - param_value

    """
    thrift_spec = None


    def __init__(self, param_name = None, param_value = None,):
        self.param_name = param_name
        self.param_value = param_value

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.STRING:
                    self.param_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRING:
                    self.param_value = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('InitParameter')
        if self.param_name is not None:
            oprot.writeFieldBegin('param_name', TType.STRING, 1)
            oprot.writeString(self.param_name.encode('utf-8') if sys.version_info[0] == 2 else self.param_name)
            oprot.writeFieldEnd()
        if self.param_value is not None:
            oprot.writeFieldBegin('param_value', TType.STRING, 2)
            oprot.writeString(self.param_value.encode('utf-8') if sys.version_info[0] == 2 else self.param_value)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class ConstantExpr(object):
    """
    Attributes:
     - literal_type
     - bool_value
     - i64_value
     - f64_value
     - str_value
     - i64_array_value
     - f64_array_value
     - i64_tensor_value
     - f64_tensor_value
     - i64_tensor_array_value
     - f64_tensor_array_value
     - i64_array_idx
     - curly_brackets_array

    """
    thrift_spec = None


    def __init__(self, literal_type = None, bool_value = None, i64_value = None, f64_value = None, str_value = None, i64_array_value = None, f64_array_value = None, i64_tensor_value = None, f64_tensor_value = None, i64_tensor_array_value = None, f64_tensor_array_value = None, i64_array_idx = None, curly_brackets_array = None,):
        self.literal_type = literal_type
        self.bool_value = bool_value
        self.i64_value = i64_value
        self.f64_value = f64_value
        self.str_value = str_value
        self.i64_array_value = i64_array_value
        self.f64_array_value = f64_array_value
        self.i64_tensor_value = i64_tensor_value
        self.f64_tensor_value = f64_tensor_value
        self.i64_tensor_array_value = i64_tensor_array_value
        self.f64_tensor_array_value = f64_tensor_array_value
        self.i64_array_idx = i64_array_idx
        self.curly_brackets_array = curly_brackets_array

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.I32:
                    self.literal_type = iprot.readI32()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.BOOL:
                    self.bool_value = iprot.readBool()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.I64:
                    self.i64_value = iprot.readI64()
                else:
                    iprot.skip(ftype)
            elif fid == 4:
                if ftype == TType.DOUBLE:
                    self.f64_value = iprot.readDouble()
                else:
                    iprot.skip(ftype)
            elif fid == 5:
                if ftype == TType.STRING:
                    self.str_value = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 6:
                if ftype == TType.LIST:
                    self.i64_array_value = []
                    (_etype87, _size84) = iprot.readListBegin()
                    for _i88 in range(_size84):
                        _elem89 = iprot.readI64()
                        self.i64_array_value.append(_elem89)
                    iprot.readListEnd()
                else:
                    iprot.skip(ftype)
            elif fid == 7:
                if ftype == TType.LIST:
                    self.f64_array_value = []
                    (_etype93, _size90) = iprot.readListBegin()
                    for _i94 in range(_size90):
                        _elem95 = iprot.readDouble()
                        self.f64_array_value.append(_elem95)
                    iprot.readListEnd()
                else:
                    iprot.skip(ftype)
            elif fid == 8:
                if ftype == TType.LIST:
                    self.i64_tensor_value = []
                    (_etype99, _size96) = iprot.readListBegin()
                    for _i100 in range(_size96):
                        _elem101 = []
                        (_etype105, _size102) = iprot.readListBegin()
                        for _i106 in range(_size102):
                            _elem107 = iprot.readI64()
                            _elem101.append(_elem107)
                        iprot.readListEnd()
                        self.i64_tensor_value.append(_elem101)
                    iprot.readListEnd()
                else:
                    iprot.skip(ftype)
            elif fid == 9:
                if ftype == TType.LIST:
                    self.f64_tensor_value = []
                    (_etype111, _size108) = iprot.readListBegin()
                    for _i112 in range(_size108):
                        _elem113 = []
                        (_etype117, _size114) = iprot.readListBegin()
                        for _i118 in range(_size114):
                            _elem119 = iprot.readDouble()
                            _elem113.append(_elem119)
                        iprot.readListEnd()
                        self.f64_tensor_value.append(_elem113)
                    iprot.readListEnd()
                else:
                    iprot.skip(ftype)
            elif fid == 10:
                if ftype == TType.LIST:
                    self.i64_tensor_array_value = []
                    (_etype123, _size120) = iprot.readListBegin()
                    for _i124 in range(_size120):
                        _elem125 = []
                        (_etype129, _size126) = iprot.readListBegin()
                        for _i130 in range(_size126):
                            _elem131 = []
                            (_etype135, _size132) = iprot.readListBegin()
                            for _i136 in range(_size132):
                                _elem137 = iprot.readI64()
                                _elem131.append(_elem137)
                            iprot.readListEnd()
                            _elem125.append(_elem131)
                        iprot.readListEnd()
                        self.i64_tensor_array_value.append(_elem125)
                    iprot.readListEnd()
                else:
                    iprot.skip(ftype)
            elif fid == 11:
                if ftype == TType.LIST:
                    self.f64_tensor_array_value = []
                    (_etype141, _size138) = iprot.readListBegin()
                    for _i142 in range(_size138):
                        _elem143 = []
                        (_etype147, _size144) = iprot.readListBegin()
                        for _i148 in range(_size144):
                            _elem149 = []
                            (_etype153, _size150) = iprot.readListBegin()
                            for _i154 in range(_size150):
                                _elem155 = iprot.readDouble()
                                _elem149.append(_elem155)
                            iprot.readListEnd()
                            _elem143.append(_elem149)
                        iprot.readListEnd()
                        self.f64_tensor_array_value.append(_elem143)
                    iprot.readListEnd()
                else:
                    iprot.skip(ftype)
            elif fid == 12:
                if ftype == TType.LIST:
                    self.i64_array_idx = []
                    (_etype159, _size156) = iprot.readListBegin()
                    for _i160 in range(_size156):
                        _elem161 = iprot.readI64()
                        self.i64_array_idx.append(_elem161)
                    iprot.readListEnd()
                else:
                    iprot.skip(ftype)
            elif fid == 13:
                if ftype == TType.LIST:
                    self.curly_brackets_array = []
                    (_etype165, _size162) = iprot.readListBegin()
                    for _i166 in range(_size162):
                        _elem167 = ConstantExpr()
                        _elem167.read(iprot)
                        self.curly_brackets_array.append(_elem167)
                    iprot.readListEnd()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('ConstantExpr')
        if self.literal_type is not None:
            oprot.writeFieldBegin('literal_type', TType.I32, 1)
            oprot.writeI32(self.literal_type)
            oprot.writeFieldEnd()
        if self.bool_value is not None:
            oprot.writeFieldBegin('bool_value', TType.BOOL, 2)
            oprot.writeBool(self.bool_value)
            oprot.writeFieldEnd()
        if self.i64_value is not None:
            oprot.writeFieldBegin('i64_value', TType.I64, 3)
            oprot.writeI64(self.i64_value)
            oprot.writeFieldEnd()
        if self.f64_value is not None:
            oprot.writeFieldBegin('f64_value', TType.DOUBLE, 4)
            oprot.writeDouble(self.f64_value)
            oprot.writeFieldEnd()
        if self.str_value is not None:
            oprot.writeFieldBegin('str_value', TType.STRING, 5)
            oprot.writeString(self.str_value.encode('utf-8') if sys.version_info[0] == 2 else self.str_value)
            oprot.writeFieldEnd()
        if self.i64_array_value is not None:
            oprot.writeFieldBegin('i64_array_value', TType.LIST, 6)
            oprot.writeListBegin(TType.I64, len(self.i64_array_value))
            for iter168 in self.i64_array_value:
                oprot.writeI64(iter168)
            oprot.writeListEnd()
            oprot.writeFieldEnd()
        if self.f64_array_value is not None:
            oprot.writeFieldBegin('f64_array_value', TType.LIST, 7)
            oprot.writeListBegin(TType.DOUBLE, len(self.f64_array_value))
            for iter169 in self.f64_array_value:
                oprot.writeDouble(iter169)
            oprot.writeListEnd()
            oprot.writeFieldEnd()
        if self.i64_tensor_value is not None:
            oprot.writeFieldBegin('i64_tensor_value', TType.LIST, 8)
            oprot.writeListBegin(TType.LIST, len(self.i64_tensor_value))
            for iter170 in self.i64_tensor_value:
                oprot.writeListBegin(TType.I64, len(iter170))
                for iter171 in iter170:
                    oprot.writeI64(iter171)
                oprot.writeListEnd()
            oprot.writeListEnd()
            oprot.writeFieldEnd()
        if self.f64_tensor_value is not None:
            oprot.writeFieldBegin('f64_tensor_value', TType.LIST, 9)
            oprot.writeListBegin(TType.LIST, len(self.f64_tensor_value))
            for iter172 in self.f64_tensor_value:
                oprot.writeListBegin(TType.DOUBLE, len(iter172))
                for iter173 in iter172:
                    oprot.writeDouble(iter173)
                oprot.writeListEnd()
            oprot.writeListEnd()
            oprot.writeFieldEnd()
        if self.i64_tensor_array_value is not None:
            oprot.writeFieldBegin('i64_tensor_array_value', TType.LIST, 10)
            oprot.writeListBegin(TType.LIST, len(self.i64_tensor_array_value))
            for iter174 in self.i64_tensor_array_value:
                oprot.writeListBegin(TType.LIST, len(iter174))
                for iter175 in iter174:
                    oprot.writeListBegin(TType.I64, len(iter175))
                    for iter176 in iter175:
                        oprot.writeI64(iter176)
                    oprot.writeListEnd()
                oprot.writeListEnd()
            oprot.writeListEnd()
            oprot.writeFieldEnd()
        if self.f64_tensor_array_value is not None:
            oprot.writeFieldBegin('f64_tensor_array_value', TType.LIST, 11)
            oprot.writeListBegin(TType.LIST, len(self.f64_tensor_array_value))
            for iter177 in self.f64_tensor_array_value:
                oprot.writeListBegin(TType.LIST, len(iter177))
                for iter178 in iter177:
                    oprot.writeListBegin(TType.DOUBLE, len(iter178))
                    for iter179 in iter178:
                        oprot.writeDouble(iter179)
                    oprot.writeListEnd()
                oprot.writeListEnd()
            oprot.writeListEnd()
            oprot.writeFieldEnd()
        if self.i64_array_idx is not None:
            oprot.writeFieldBegin('i64_array_idx', TType.LIST, 12)
            oprot.writeListBegin(TType.I64, len(self.i64_array_idx))
            for iter180 in self.i64_array_idx:
                oprot.writeI64(iter180)
            oprot.writeListEnd()
            oprot.writeFieldEnd()
        if self.curly_brackets_array is not None:
            oprot.writeFieldBegin('curly_brackets_array', TType.LIST, 13)
            oprot.writeListBegin(TType.STRUCT, len(self.curly_brackets_array))
            for iter181 in self.curly_brackets_array:
                iter181.write(oprot)
            oprot.writeListEnd()
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class KnnExpr(object):
    """
    Attributes:
     - column_expr
     - embedding_data
     - embedding_data_type
     - distance_type
     - topn
     - opt_params
     - filter_expr

    """
    thrift_spec = None


    def __init__(self, column_expr = None, embedding_data = None, embedding_data_type = None, distance_type = None, topn = None, opt_params = [
    ], filter_expr = None,):
        self.column_expr = column_expr
        self.embedding_data = embedding_data
        self.embedding_data_type = embedding_data_type
        self.distance_type = distance_type
        self.topn = topn
        if opt_params is self.thrift_spec[6][4]:
            opt_params = [
            ]
        self.opt_params = opt_params
        self.filter_expr = filter_expr

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.STRUCT:
                    self.column_expr = ColumnExpr()
                    self.column_expr.read(iprot)
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRUCT:
                    self.embedding_data = EmbeddingData()
                    self.embedding_data.read(iprot)
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.I32:
                    self.embedding_data_type = iprot.readI32()
                else:
                    iprot.skip(ftype)
            elif fid == 4:
                if ftype == TType.I32:
                    self.distance_type = iprot.readI32()
                else:
                    iprot.skip(ftype)
            elif fid == 5:
                if ftype == TType.I64:
                    self.topn = iprot.readI64()
                else:
                    iprot.skip(ftype)
            elif fid == 6:
                if ftype == TType.LIST:
                    self.opt_params = []
                    (_etype185, _size182) = iprot.readListBegin()
                    for _i186 in range(_size182):
                        _elem187 = InitParameter()
                        _elem187.read(iprot)
                        self.opt_params.append(_elem187)
                    iprot.readListEnd()
                else:
                    iprot.skip(ftype)
            elif fid == 7:
                if ftype == TType.STRUCT:
                    self.filter_expr = ParsedExpr()
                    self.filter_expr.read(iprot)
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('KnnExpr')
        if self.column_expr is not None:
            oprot.writeFieldBegin('column_expr', TType.STRUCT, 1)
            self.column_expr.write(oprot)
            oprot.writeFieldEnd()
        if self.embedding_data is not None:
            oprot.writeFieldBegin('embedding_data', TType.STRUCT, 2)
            self.embedding_data.write(oprot)
            oprot.writeFieldEnd()
        if self.embedding_data_type is not None:
            oprot.writeFieldBegin('embedding_data_type', TType.I32, 3)
            oprot.writeI32(self.embedding_data_type)
            oprot.writeFieldEnd()
        if self.distance_type is not None:
            oprot.writeFieldBegin('distance_type', TType.I32, 4)
            oprot.writeI32(self.distance_type)
            oprot.writeFieldEnd()
        if self.topn is not None:
            oprot.writeFieldBegin('topn', TType.I64, 5)
            oprot.writeI64(self.topn)
            oprot.writeFieldEnd()
        if self.opt_params is not None:
            oprot.writeFieldBegin('opt_params', TType.LIST, 6)
            oprot.writeListBegin(TType.STRUCT, len(self.opt_params))
            for iter188 in self.opt_params:
                iter188.write(oprot)
            oprot.writeListEnd()
            oprot.writeFieldEnd()
        if self.filter_expr is not None:
            oprot.writeFieldBegin('filter_expr', TType.STRUCT, 7)
            self.filter_expr.write(oprot)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class MatchSparseExpr(object):
    """
    Attributes:
     - column_expr
     - query_sparse_expr
     - metric_type
     - topn
     - opt_params
     - filter_expr

    """
    thrift_spec = None


    def __init__(self, column_expr = None, query_sparse_expr = None, metric_type = None, topn = None, opt_params = [
    ], filter_expr = None,):
        self.column_expr = column_expr
        self.query_sparse_expr = query_sparse_expr
        self.metric_type = metric_type
        self.topn = topn
        if opt_params is self.thrift_spec[5][4]:
            opt_params = [
            ]
        self.opt_params = opt_params
        self.filter_expr = filter_expr

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.STRUCT:
                    self.column_expr = ColumnExpr()
                    self.column_expr.read(iprot)
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRUCT:
                    self.query_sparse_expr = ConstantExpr()
                    self.query_sparse_expr.read(iprot)
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.STRING:
                    self.metric_type = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 4:
                if ftype == TType.I64:
                    self.topn = iprot.readI64()
                else:
                    iprot.skip(ftype)
            elif fid == 5:
                if ftype == TType.LIST:
                    self.opt_params = []
                    (_etype192, _size189) = iprot.readListBegin()
                    for _i193 in range(_size189):
                        _elem194 = InitParameter()
                        _elem194.read(iprot)
                        self.opt_params.append(_elem194)
                    iprot.readListEnd()
                else:
                    iprot.skip(ftype)
            elif fid == 6:
                if ftype == TType.STRUCT:
                    self.filter_expr = ParsedExpr()
                    self.filter_expr.read(iprot)
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('MatchSparseExpr')
        if self.column_expr is not None:
            oprot.writeFieldBegin('column_expr', TType.STRUCT, 1)
            self.column_expr.write(oprot)
            oprot.writeFieldEnd()
        if self.query_sparse_expr is not None:
            oprot.writeFieldBegin('query_sparse_expr', TType.STRUCT, 2)
            self.query_sparse_expr.write(oprot)
            oprot.writeFieldEnd()
        if self.metric_type is not None:
            oprot.writeFieldBegin('metric_type', TType.STRING, 3)
            oprot.writeString(self.metric_type.encode('utf-8') if sys.version_info[0] == 2 else self.metric_type)
            oprot.writeFieldEnd()
        if self.topn is not None:
            oprot.writeFieldBegin('topn', TType.I64, 4)
            oprot.writeI64(self.topn)
            oprot.writeFieldEnd()
        if self.opt_params is not None:
            oprot.writeFieldBegin('opt_params', TType.LIST, 5)
            oprot.writeListBegin(TType.STRUCT, len(self.opt_params))
            for iter195 in self.opt_params:
                iter195.write(oprot)
            oprot.writeListEnd()
            oprot.writeFieldEnd()
        if self.filter_expr is not None:
            oprot.writeFieldBegin('filter_expr', TType.STRUCT, 6)
            self.filter_expr.write(oprot)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class MatchTensorExpr(object):
    """
    Attributes:
     - search_method
     - column_expr
     - embedding_data_type
     - embedding_data
     - extra_options
     - filter_expr

    """
    thrift_spec = None


    def __init__(self, search_method = None, column_expr = None, embedding_data_type = None, embedding_data = None, extra_options = None, filter_expr = None,):
        self.search_method = search_method
        self.column_expr = column_expr
        self.embedding_data_type = embedding_data_type
        self.embedding_data = embedding_data
        self.extra_options = extra_options
        self.filter_expr = filter_expr

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.STRING:
                    self.search_method = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRUCT:
                    self.column_expr = ColumnExpr()
                    self.column_expr.read(iprot)
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.I32:
                    self.embedding_data_type = iprot.readI32()
                else:
                    iprot.skip(ftype)
            elif fid == 4:
                if ftype == TType.STRUCT:
                    self.embedding_data = EmbeddingData()
                    self.embedding_data.read(iprot)
                else:
                    iprot.skip(ftype)
            elif fid == 5:
                if ftype == TType.STRING:
                    self.extra_options = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 6:
                if ftype == TType.STRUCT:
                    self.filter_expr = ParsedExpr()
                    self.filter_expr.read(iprot)
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('MatchTensorExpr')
        if self.search_method is not None:
            oprot.writeFieldBegin('search_method', TType.STRING, 1)
            oprot.writeString(self.search_method.encode('utf-8') if sys.version_info[0] == 2 else self.search_method)
            oprot.writeFieldEnd()
        if self.column_expr is not None:
            oprot.writeFieldBegin('column_expr', TType.STRUCT, 2)
            self.column_expr.write(oprot)
            oprot.writeFieldEnd()
        if self.embedding_data_type is not None:
            oprot.writeFieldBegin('embedding_data_type', TType.I32, 3)
            oprot.writeI32(self.embedding_data_type)
            oprot.writeFieldEnd()
        if self.embedding_data is not None:
            oprot.writeFieldBegin('embedding_data', TType.STRUCT, 4)
            self.embedding_data.write(oprot)
            oprot.writeFieldEnd()
        if self.extra_options is not None:
            oprot.writeFieldBegin('extra_options', TType.STRING, 5)
            oprot.writeString(self.extra_options.encode('utf-8') if sys.version_info[0] == 2 else self.extra_options)
            oprot.writeFieldEnd()
        if self.filter_expr is not None:
            oprot.writeFieldBegin('filter_expr', TType.STRUCT, 6)
            self.filter_expr.write(oprot)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class MatchExpr(object):
    """
    Attributes:
     - fields
     - matching_text
     - options_text
     - filter_expr

    """
    thrift_spec = None


    def __init__(self, fields = None, matching_text = None, options_text = None, filter_expr = None,):
        self.fields = fields
        self.matching_text = matching_text
        self.options_text = options_text
        self.filter_expr = filter_expr

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.STRING:
                    self.fields = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRING:
                    self.matching_text = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.STRING:
                    self.options_text = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 4:
                if ftype == TType.STRUCT:
                    self.filter_expr = ParsedExpr()
                    self.filter_expr.read(iprot)
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('MatchExpr')
        if self.fields is not None:
            oprot.writeFieldBegin('fields', TType.STRING, 1)
            oprot.writeString(self.fields.encode('utf-8') if sys.version_info[0] == 2 else self.fields)
            oprot.writeFieldEnd()
        if self.matching_text is not None:
            oprot.writeFieldBegin('matching_text', TType.STRING, 2)
            oprot.writeString(self.matching_text.encode('utf-8') if sys.version_info[0] == 2 else self.matching_text)
            oprot.writeFieldEnd()
        if self.options_text is not None:
            oprot.writeFieldBegin('options_text', TType.STRING, 3)
            oprot.writeString(self.options_text.encode('utf-8') if sys.version_info[0] == 2 else self.options_text)
            oprot.writeFieldEnd()
        if self.filter_expr is not None:
            oprot.writeFieldBegin('filter_expr', TType.STRUCT, 4)
            self.filter_expr.write(oprot)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class GenericMatchExpr(object):
    """
    Attributes:
     - match_vector_expr
     - match_sparse_expr
     - match_tensor_expr
     - match_text_expr

    """
    thrift_spec = None


    def __init__(self, match_vector_expr = None, match_sparse_expr = None, match_tensor_expr = None, match_text_expr = None,):
        self.match_vector_expr = match_vector_expr
        self.match_sparse_expr = match_sparse_expr
        self.match_tensor_expr = match_tensor_expr
        self.match_text_expr = match_text_expr

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.STRUCT:
                    self.match_vector_expr = KnnExpr()
                    self.match_vector_expr.read(iprot)
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRUCT:
                    self.match_sparse_expr = MatchSparseExpr()
                    self.match_sparse_expr.read(iprot)
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.STRUCT:
                    self.match_tensor_expr = MatchTensorExpr()
                    self.match_tensor_expr.read(iprot)
                else:
                    iprot.skip(ftype)
            elif fid == 4:
                if ftype == TType.STRUCT:
                    self.match_text_expr = MatchExpr()
                    self.match_text_expr.read(iprot)
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('GenericMatchExpr')
        if self.match_vector_expr is not None:
            oprot.writeFieldBegin('match_vector_expr', TType.STRUCT, 1)
            self.match_vector_expr.write(oprot)
            oprot.writeFieldEnd()
        if self.match_sparse_expr is not None:
            oprot.writeFieldBegin('match_sparse_expr', TType.STRUCT, 2)
            self.match_sparse_expr.write(oprot)
            oprot.writeFieldEnd()
        if self.match_tensor_expr is not None:
            oprot.writeFieldBegin('match_tensor_expr', TType.STRUCT, 3)
            self.match_tensor_expr.write(oprot)
            oprot.writeFieldEnd()
        if self.match_text_expr is not None:
            oprot.writeFieldBegin('match_text_expr', TType.STRUCT, 4)
            self.match_text_expr.write(oprot)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class FusionExpr(object):
    """
    Attributes:
     - method
     - options_text
     - optional_match_tensor_expr

    """
    thrift_spec = None


    def __init__(self, method = None, options_text = None, optional_match_tensor_expr = None,):
        self.method = method
        self.options_text = options_text
        self.optional_match_tensor_expr = optional_match_tensor_expr

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.STRING:
                    self.method = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRING:
                    self.options_text = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.STRUCT:
                    self.optional_match_tensor_expr = MatchTensorExpr()
                    self.optional_match_tensor_expr.read(iprot)
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('FusionExpr')
        if self.method is not None:
            oprot.writeFieldBegin('method', TType.STRING, 1)
            oprot.writeString(self.method.encode('utf-8') if sys.version_info[0] == 2 else self.method)
            oprot.writeFieldEnd()
        if self.options_text is not None:
            oprot.writeFieldBegin('options_text', TType.STRING, 2)
            oprot.writeString(self.options_text.encode('utf-8') if sys.version_info[0] == 2 else self.options_text)
            oprot.writeFieldEnd()
        if self.optional_match_tensor_expr is not None:
            oprot.writeFieldBegin('optional_match_tensor_expr', TType.STRUCT, 3)
            self.optional_match_tensor_expr.write(oprot)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class SearchExpr(object):
    """
    Attributes:
     - match_exprs
     - fusion_exprs

    """
    thrift_spec = None


    def __init__(self, match_exprs = None, fusion_exprs = None,):
        self.match_exprs = match_exprs
        self.fusion_exprs = fusion_exprs

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.LIST:
                    self.match_exprs = []
                    (_etype199, _size196) = iprot.readListBegin()
                    for _i200 in range(_size196):
                        _elem201 = GenericMatchExpr()
                        _elem201.read(iprot)
                        self.match_exprs.append(_elem201)
                    iprot.readListEnd()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.LIST:
                    self.fusion_exprs = []
                    (_etype205, _size202) = iprot.readListBegin()
                    for _i206 in range(_size202):
                        _elem207 = FusionExpr()
                        _elem207.read(iprot)
                        self.fusion_exprs.append(_elem207)
                    iprot.readListEnd()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('SearchExpr')
        if self.match_exprs is not None:
            oprot.writeFieldBegin('match_exprs', TType.LIST, 1)
            oprot.writeListBegin(TType.STRUCT, len(self.match_exprs))
            for iter208 in self.match_exprs:
                iter208.write(oprot)
            oprot.writeListEnd()
            oprot.writeFieldEnd()
        if self.fusion_exprs is not None:
            oprot.writeFieldBegin('fusion_exprs', TType.LIST, 2)
            oprot.writeListBegin(TType.STRUCT, len(self.fusion_exprs))
            for iter209 in self.fusion_exprs:
                iter209.write(oprot)
            oprot.writeListEnd()
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class FunctionExpr(object):
    """
    Attributes:
     - function_name
     - arguments

    """
    thrift_spec = None


    def __init__(self, function_name = None, arguments = None,):
        self.function_name = function_name
        self.arguments = arguments

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.STRING:
                    self.function_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.LIST:
                    self.arguments = []
                    (_etype213, _size210) = iprot.readListBegin()
                    for _i214 in range(_size210):
                        _elem215 = ParsedExpr()
                        _elem215.read(iprot)
                        self.arguments.append(_elem215)
                    iprot.readListEnd()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('FunctionExpr')
        if self.function_name is not None:
            oprot.writeFieldBegin('function_name', TType.STRING, 1)
            oprot.writeString(self.function_name.encode('utf-8') if sys.version_info[0] == 2 else self.function_name)
            oprot.writeFieldEnd()
        if self.arguments is not None:
            oprot.writeFieldBegin('arguments', TType.LIST, 2)
            oprot.writeListBegin(TType.STRUCT, len(self.arguments))
            for iter216 in self.arguments:
                iter216.write(oprot)
            oprot.writeListEnd()
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class BetweenExpr(object):
    """
    Attributes:
     - value
     - upper_bound
     - lower_bound

    """
    thrift_spec = None


    def __init__(self, value = None, upper_bound = None, lower_bound = None,):
        self.value = value
        self.upper_bound = upper_bound
        self.lower_bound = lower_bound

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.STRUCT:
                    self.value = ParsedExpr()
                    self.value.read(iprot)
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRUCT:
                    self.upper_bound = ParsedExpr()
                    self.upper_bound.read(iprot)
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.STRUCT:
                    self.lower_bound = ParsedExpr()
                    self.lower_bound.read(iprot)
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('BetweenExpr')
        if self.value is not None:
            oprot.writeFieldBegin('value', TType.STRUCT, 1)
            self.value.write(oprot)
            oprot.writeFieldEnd()
        if self.upper_bound is not None:
            oprot.writeFieldBegin('upper_bound', TType.STRUCT, 2)
            self.upper_bound.write(oprot)
            oprot.writeFieldEnd()
        if self.lower_bound is not None:
            oprot.writeFieldBegin('lower_bound', TType.STRUCT, 3)
            self.lower_bound.write(oprot)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class UpdateExpr(object):
    """
    Attributes:
     - column_name
     - value

    """
    thrift_spec = None


    def __init__(self, column_name = None, value = None,):
        self.column_name = column_name
        self.value = value

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.STRING:
                    self.column_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRUCT:
                    self.value = ParsedExpr()
                    self.value.read(iprot)
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('UpdateExpr')
        if self.column_name is not None:
            oprot.writeFieldBegin('column_name', TType.STRING, 1)
            oprot.writeString(self.column_name.encode('utf-8') if sys.version_info[0] == 2 else self.column_name)
            oprot.writeFieldEnd()
        if self.value is not None:
            oprot.writeFieldBegin('value', TType.STRUCT, 2)
            self.value.write(oprot)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class OrderByExpr(object):
    """
    Attributes:
     - expr
     - asc

    """
    thrift_spec = None


    def __init__(self, expr = None, asc = None,):
        self.expr = expr
        self.asc = asc

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.STRUCT:
                    self.expr = ParsedExpr()
                    self.expr.read(iprot)
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.BOOL:
                    self.asc = iprot.readBool()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('OrderByExpr')
        if self.expr is not None:
            oprot.writeFieldBegin('expr', TType.STRUCT, 1)
            self.expr.write(oprot)
            oprot.writeFieldEnd()
        if self.asc is not None:
            oprot.writeFieldBegin('asc', TType.BOOL, 2)
            oprot.writeBool(self.asc)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class InExpr(object):
    """
    Attributes:
     - left_operand
     - arguments
     - in_type

    """
    thrift_spec = None


    def __init__(self, left_operand = None, arguments = None, in_type = None,):
        self.left_operand = left_operand
        self.arguments = arguments
        self.in_type = in_type

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.STRUCT:
                    self.left_operand = ParsedExpr()
                    self.left_operand.read(iprot)
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.LIST:
                    self.arguments = []
                    (_etype220, _size217) = iprot.readListBegin()
                    for _i221 in range(_size217):
                        _elem222 = ParsedExpr()
                        _elem222.read(iprot)
                        self.arguments.append(_elem222)
                    iprot.readListEnd()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.BOOL:
                    self.in_type = iprot.readBool()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('InExpr')
        if self.left_operand is not None:
            oprot.writeFieldBegin('left_operand', TType.STRUCT, 1)
            self.left_operand.write(oprot)
            oprot.writeFieldEnd()
        if self.arguments is not None:
            oprot.writeFieldBegin('arguments', TType.LIST, 2)
            oprot.writeListBegin(TType.STRUCT, len(self.arguments))
            for iter223 in self.arguments:
                iter223.write(oprot)
            oprot.writeListEnd()
            oprot.writeFieldEnd()
        if self.in_type is not None:
            oprot.writeFieldBegin('in_type', TType.BOOL, 3)
            oprot.writeBool(self.in_type)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class ColumnDef(object):
    """
    Attributes:
     - id
     - name
     - data_type
     - constraints
     - constant_expr
     - comment

    """
    thrift_spec = None


    def __init__(self, id = None, name = None, data_type = None, constraints = [
    ], constant_expr = None, comment = None,):
        self.id = id
        self.name = name
        self.data_type = data_type
        if constraints is self.thrift_spec[4][4]:
            constraints = [
            ]
        self.constraints = constraints
        self.constant_expr = constant_expr
        self.comment = comment

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.I32:
                    self.id = iprot.readI32()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRING:
                    self.name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.STRUCT:
                    self.data_type = DataType()
                    self.data_type.read(iprot)
                else:
                    iprot.skip(ftype)
            elif fid == 4:
                if ftype == TType.LIST:
                    self.constraints = []
                    (_etype227, _size224) = iprot.readListBegin()
                    for _i228 in range(_size224):
                        _elem229 = iprot.readI32()
                        self.constraints.append(_elem229)
                    iprot.readListEnd()
                else:
                    iprot.skip(ftype)
            elif fid == 5:
                if ftype == TType.STRUCT:
                    self.constant_expr = ConstantExpr()
                    self.constant_expr.read(iprot)
                else:
                    iprot.skip(ftype)
            elif fid == 6:
                if ftype == TType.STRING:
                    self.comment = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('ColumnDef')
        if self.id is not None:
            oprot.writeFieldBegin('id', TType.I32, 1)
            oprot.writeI32(self.id)
            oprot.writeFieldEnd()
        if self.name is not None:
            oprot.writeFieldBegin('name', TType.STRING, 2)
            oprot.writeString(self.name.encode('utf-8') if sys.version_info[0] == 2 else self.name)
            oprot.writeFieldEnd()
        if self.data_type is not None:
            oprot.writeFieldBegin('data_type', TType.STRUCT, 3)
            self.data_type.write(oprot)
            oprot.writeFieldEnd()
        if self.constraints is not None:
            oprot.writeFieldBegin('constraints', TType.LIST, 4)
            oprot.writeListBegin(TType.I32, len(self.constraints))
            for iter230 in self.constraints:
                oprot.writeI32(iter230)
            oprot.writeListEnd()
            oprot.writeFieldEnd()
        if self.constant_expr is not None:
            oprot.writeFieldBegin('constant_expr', TType.STRUCT, 5)
            self.constant_expr.write(oprot)
            oprot.writeFieldEnd()
        if self.comment is not None:
            oprot.writeFieldBegin('comment', TType.STRING, 6)
            oprot.writeString(self.comment.encode('utf-8') if sys.version_info[0] == 2 else self.comment)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class Field(object):
    """
    Attributes:
     - column_names
     - parse_exprs

    """
    thrift_spec = None


    def __init__(self, column_names = [
    ], parse_exprs = [
    ],):
        if column_names is self.thrift_spec[1][4]:
            column_names = [
            ]
        self.column_names = column_names
        if parse_exprs is self.thrift_spec[2][4]:
            parse_exprs = [
            ]
        self.parse_exprs = parse_exprs

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.LIST:
                    self.column_names = []
                    (_etype234, _size231) = iprot.readListBegin()
                    for _i235 in range(_size231):
                        _elem236 = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                        self.column_names.append(_elem236)
                    iprot.readListEnd()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.LIST:
                    self.parse_exprs = []
                    (_etype240, _size237) = iprot.readListBegin()
                    for _i241 in range(_size237):
                        _elem242 = ParsedExpr()
                        _elem242.read(iprot)
                        self.parse_exprs.append(_elem242)
                    iprot.readListEnd()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('Field')
        if self.column_names is not None:
            oprot.writeFieldBegin('column_names', TType.LIST, 1)
            oprot.writeListBegin(TType.STRING, len(self.column_names))
            for iter243 in self.column_names:
                oprot.writeString(iter243.encode('utf-8') if sys.version_info[0] == 2 else iter243)
            oprot.writeListEnd()
            oprot.writeFieldEnd()
        if self.parse_exprs is not None:
            oprot.writeFieldBegin('parse_exprs', TType.LIST, 2)
            oprot.writeListBegin(TType.STRUCT, len(self.parse_exprs))
            for iter244 in self.parse_exprs:
                iter244.write(oprot)
            oprot.writeListEnd()
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class ColumnField(object):
    """
    Attributes:
     - column_type
     - column_vectors
     - column_name

    """
    thrift_spec = None


    def __init__(self, column_type = None, column_vectors = [
    ], column_name = None,):
        self.column_type = column_type
        if column_vectors is self.thrift_spec[2][4]:
            column_vectors = [
            ]
        self.column_vectors = column_vectors
        self.column_name = column_name

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.I32:
                    self.column_type = iprot.readI32()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.LIST:
                    self.column_vectors = []
                    (_etype248, _size245) = iprot.readListBegin()
                    for _i249 in range(_size245):
                        _elem250 = iprot.readBinary()
                        self.column_vectors.append(_elem250)
                    iprot.readListEnd()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.STRING:
                    self.column_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('ColumnField')
        if self.column_type is not None:
            oprot.writeFieldBegin('column_type', TType.I32, 1)
            oprot.writeI32(self.column_type)
            oprot.writeFieldEnd()
        if self.column_vectors is not None:
            oprot.writeFieldBegin('column_vectors', TType.LIST, 2)
            oprot.writeListBegin(TType.STRING, len(self.column_vectors))
            for iter251 in self.column_vectors:
                oprot.writeBinary(iter251)
            oprot.writeListEnd()
            oprot.writeFieldEnd()
        if self.column_name is not None:
            oprot.writeFieldBegin('column_name', TType.STRING, 3)
            oprot.writeString(self.column_name.encode('utf-8') if sys.version_info[0] == 2 else self.column_name)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class ImportOption(object):
    """
    Attributes:
     - delimiter
     - has_header
     - copy_file_type

    """
    thrift_spec = None


    def __init__(self, delimiter = None, has_header = None, copy_file_type = None,):
        self.delimiter = delimiter
        self.has_header = has_header
        self.copy_file_type = copy_file_type

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.STRING:
                    self.delimiter = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.BOOL:
                    self.has_header = iprot.readBool()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.I32:
                    self.copy_file_type = iprot.readI32()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('ImportOption')
        if self.delimiter is not None:
            oprot.writeFieldBegin('delimiter', TType.STRING, 1)
            oprot.writeString(self.delimiter.encode('utf-8') if sys.version_info[0] == 2 else self.delimiter)
            oprot.writeFieldEnd()
        if self.has_header is not None:
            oprot.writeFieldBegin('has_header', TType.BOOL, 2)
            oprot.writeBool(self.has_header)
            oprot.writeFieldEnd()
        if self.copy_file_type is not None:
            oprot.writeFieldBegin('copy_file_type', TType.I32, 3)
            oprot.writeI32(self.copy_file_type)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class ExportOption(object):
    """
    Attributes:
     - delimiter
     - has_header
     - copy_file_type
     - offset
     - limit
     - row_limit

    """
    thrift_spec = None


    def __init__(self, delimiter = None, has_header = None, copy_file_type = None, offset = None, limit = None, row_limit = None,):
        self.delimiter = delimiter
        self.has_header = has_header
        self.copy_file_type = copy_file_type
        self.offset = offset
        self.limit = limit
        self.row_limit = row_limit

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.STRING:
                    self.delimiter = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.BOOL:
                    self.has_header = iprot.readBool()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.I32:
                    self.copy_file_type = iprot.readI32()
                else:
                    iprot.skip(ftype)
            elif fid == 4:
                if ftype == TType.I64:
                    self.offset = iprot.readI64()
                else:
                    iprot.skip(ftype)
            elif fid == 5:
                if ftype == TType.I64:
                    self.limit = iprot.readI64()
                else:
                    iprot.skip(ftype)
            elif fid == 6:
                if ftype == TType.I64:
                    self.row_limit = iprot.readI64()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('ExportOption')
        if self.delimiter is not None:
            oprot.writeFieldBegin('delimiter', TType.STRING, 1)
            oprot.writeString(self.delimiter.encode('utf-8') if sys.version_info[0] == 2 else self.delimiter)
            oprot.writeFieldEnd()
        if self.has_header is not None:
            oprot.writeFieldBegin('has_header', TType.BOOL, 2)
            oprot.writeBool(self.has_header)
            oprot.writeFieldEnd()
        if self.copy_file_type is not None:
            oprot.writeFieldBegin('copy_file_type', TType.I32, 3)
            oprot.writeI32(self.copy_file_type)
            oprot.writeFieldEnd()
        if self.offset is not None:
            oprot.writeFieldBegin('offset', TType.I64, 4)
            oprot.writeI64(self.offset)
            oprot.writeFieldEnd()
        if self.limit is not None:
            oprot.writeFieldBegin('limit', TType.I64, 5)
            oprot.writeI64(self.limit)
            oprot.writeFieldEnd()
        if self.row_limit is not None:
            oprot.writeFieldBegin('row_limit', TType.I64, 6)
            oprot.writeI64(self.row_limit)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class OptimizeOptions(object):
    """
    Attributes:
     - index_name
     - opt_params

    """
    thrift_spec = None


    def __init__(self, index_name = None, opt_params = [
    ],):
        self.index_name = index_name
        if opt_params is self.thrift_spec[2][4]:
            opt_params = [
            ]
        self.opt_params = opt_params

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.STRING:
                    self.index_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.LIST:
                    self.opt_params = []
                    (_etype255, _size252) = iprot.readListBegin()
                    for _i256 in range(_size252):
                        _elem257 = InitParameter()
                        _elem257.read(iprot)
                        self.opt_params.append(_elem257)
                    iprot.readListEnd()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('OptimizeOptions')
        if self.index_name is not None:
            oprot.writeFieldBegin('index_name', TType.STRING, 1)
            oprot.writeString(self.index_name.encode('utf-8') if sys.version_info[0] == 2 else self.index_name)
            oprot.writeFieldEnd()
        if self.opt_params is not None:
            oprot.writeFieldBegin('opt_params', TType.LIST, 2)
            oprot.writeListBegin(TType.STRUCT, len(self.opt_params))
            for iter258 in self.opt_params:
                iter258.write(oprot)
            oprot.writeListEnd()
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class ConnectRequest(object):
    """
    Attributes:
     - client_version

    """
    thrift_spec = None


    def __init__(self, client_version = None,):
        self.client_version = client_version

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.I64:
                    self.client_version = iprot.readI64()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('ConnectRequest')
        if self.client_version is not None:
            oprot.writeFieldBegin('client_version', TType.I64, 1)
            oprot.writeI64(self.client_version)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class CommonRequest(object):
    """
    Attributes:
     - session_id

    """
    thrift_spec = None


    def __init__(self, session_id = None,):
        self.session_id = session_id

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.I64:
                    self.session_id = iprot.readI64()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('CommonRequest')
        if self.session_id is not None:
            oprot.writeFieldBegin('session_id', TType.I64, 1)
            oprot.writeI64(self.session_id)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class CommonResponse(object):
    """
    Attributes:
     - error_code
     - error_msg
     - session_id

    """
    thrift_spec = None


    def __init__(self, error_code = None, error_msg = None, session_id = None,):
        self.error_code = error_code
        self.error_msg = error_msg
        self.session_id = session_id

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.I64:
                    self.error_code = iprot.readI64()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRING:
                    self.error_msg = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.I64:
                    self.session_id = iprot.readI64()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('CommonResponse')
        if self.error_code is not None:
            oprot.writeFieldBegin('error_code', TType.I64, 1)
            oprot.writeI64(self.error_code)
            oprot.writeFieldEnd()
        if self.error_msg is not None:
            oprot.writeFieldBegin('error_msg', TType.STRING, 2)
            oprot.writeString(self.error_msg.encode('utf-8') if sys.version_info[0] == 2 else self.error_msg)
            oprot.writeFieldEnd()
        if self.session_id is not None:
            oprot.writeFieldBegin('session_id', TType.I64, 3)
            oprot.writeI64(self.session_id)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class ListDatabaseRequest(object):
    """
    Attributes:
     - session_id

    """
    thrift_spec = None


    def __init__(self, session_id = None,):
        self.session_id = session_id

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.I64:
                    self.session_id = iprot.readI64()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('ListDatabaseRequest')
        if self.session_id is not None:
            oprot.writeFieldBegin('session_id', TType.I64, 1)
            oprot.writeI64(self.session_id)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class ListDatabaseResponse(object):
    """
    Attributes:
     - error_code
     - error_msg
     - db_names
     - db_dirs
     - db_comments

    """
    thrift_spec = None


    def __init__(self, error_code = None, error_msg = None, db_names = [
    ], db_dirs = [
    ], db_comments = [
    ],):
        self.error_code = error_code
        self.error_msg = error_msg
        if db_names is self.thrift_spec[3][4]:
            db_names = [
            ]
        self.db_names = db_names
        if db_dirs is self.thrift_spec[4][4]:
            db_dirs = [
            ]
        self.db_dirs = db_dirs
        if db_comments is self.thrift_spec[5][4]:
            db_comments = [
            ]
        self.db_comments = db_comments

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.I64:
                    self.error_code = iprot.readI64()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRING:
                    self.error_msg = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.LIST:
                    self.db_names = []
                    (_etype262, _size259) = iprot.readListBegin()
                    for _i263 in range(_size259):
                        _elem264 = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                        self.db_names.append(_elem264)
                    iprot.readListEnd()
                else:
                    iprot.skip(ftype)
            elif fid == 4:
                if ftype == TType.LIST:
                    self.db_dirs = []
                    (_etype268, _size265) = iprot.readListBegin()
                    for _i269 in range(_size265):
                        _elem270 = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                        self.db_dirs.append(_elem270)
                    iprot.readListEnd()
                else:
                    iprot.skip(ftype)
            elif fid == 5:
                if ftype == TType.LIST:
                    self.db_comments = []
                    (_etype274, _size271) = iprot.readListBegin()
                    for _i275 in range(_size271):
                        _elem276 = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                        self.db_comments.append(_elem276)
                    iprot.readListEnd()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('ListDatabaseResponse')
        if self.error_code is not None:
            oprot.writeFieldBegin('error_code', TType.I64, 1)
            oprot.writeI64(self.error_code)
            oprot.writeFieldEnd()
        if self.error_msg is not None:
            oprot.writeFieldBegin('error_msg', TType.STRING, 2)
            oprot.writeString(self.error_msg.encode('utf-8') if sys.version_info[0] == 2 else self.error_msg)
            oprot.writeFieldEnd()
        if self.db_names is not None:
            oprot.writeFieldBegin('db_names', TType.LIST, 3)
            oprot.writeListBegin(TType.STRING, len(self.db_names))
            for iter277 in self.db_names:
                oprot.writeString(iter277.encode('utf-8') if sys.version_info[0] == 2 else iter277)
            oprot.writeListEnd()
            oprot.writeFieldEnd()
        if self.db_dirs is not None:
            oprot.writeFieldBegin('db_dirs', TType.LIST, 4)
            oprot.writeListBegin(TType.STRING, len(self.db_dirs))
            for iter278 in self.db_dirs:
                oprot.writeString(iter278.encode('utf-8') if sys.version_info[0] == 2 else iter278)
            oprot.writeListEnd()
            oprot.writeFieldEnd()
        if self.db_comments is not None:
            oprot.writeFieldBegin('db_comments', TType.LIST, 5)
            oprot.writeListBegin(TType.STRING, len(self.db_comments))
            for iter279 in self.db_comments:
                oprot.writeString(iter279.encode('utf-8') if sys.version_info[0] == 2 else iter279)
            oprot.writeListEnd()
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class ListTableRequest(object):
    """
    Attributes:
     - db_name
     - session_id

    """
    thrift_spec = None


    def __init__(self, db_name = None, session_id = None,):
        self.db_name = db_name
        self.session_id = session_id

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.STRING:
                    self.db_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.I64:
                    self.session_id = iprot.readI64()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('ListTableRequest')
        if self.db_name is not None:
            oprot.writeFieldBegin('db_name', TType.STRING, 1)
            oprot.writeString(self.db_name.encode('utf-8') if sys.version_info[0] == 2 else self.db_name)
            oprot.writeFieldEnd()
        if self.session_id is not None:
            oprot.writeFieldBegin('session_id', TType.I64, 2)
            oprot.writeI64(self.session_id)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class ListTableResponse(object):
    """
    Attributes:
     - error_code
     - error_msg
     - table_names

    """
    thrift_spec = None


    def __init__(self, error_code = None, error_msg = None, table_names = [
    ],):
        self.error_code = error_code
        self.error_msg = error_msg
        if table_names is self.thrift_spec[3][4]:
            table_names = [
            ]
        self.table_names = table_names

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.I64:
                    self.error_code = iprot.readI64()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRING:
                    self.error_msg = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.LIST:
                    self.table_names = []
                    (_etype283, _size280) = iprot.readListBegin()
                    for _i284 in range(_size280):
                        _elem285 = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                        self.table_names.append(_elem285)
                    iprot.readListEnd()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('ListTableResponse')
        if self.error_code is not None:
            oprot.writeFieldBegin('error_code', TType.I64, 1)
            oprot.writeI64(self.error_code)
            oprot.writeFieldEnd()
        if self.error_msg is not None:
            oprot.writeFieldBegin('error_msg', TType.STRING, 2)
            oprot.writeString(self.error_msg.encode('utf-8') if sys.version_info[0] == 2 else self.error_msg)
            oprot.writeFieldEnd()
        if self.table_names is not None:
            oprot.writeFieldBegin('table_names', TType.LIST, 3)
            oprot.writeListBegin(TType.STRING, len(self.table_names))
            for iter286 in self.table_names:
                oprot.writeString(iter286.encode('utf-8') if sys.version_info[0] == 2 else iter286)
            oprot.writeListEnd()
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class ListIndexRequest(object):
    """
    Attributes:
     - db_name
     - table_name
     - session_id

    """
    thrift_spec = None


    def __init__(self, db_name = None, table_name = None, session_id = None,):
        self.db_name = db_name
        self.table_name = table_name
        self.session_id = session_id

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.STRING:
                    self.db_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRING:
                    self.table_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.I64:
                    self.session_id = iprot.readI64()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('ListIndexRequest')
        if self.db_name is not None:
            oprot.writeFieldBegin('db_name', TType.STRING, 1)
            oprot.writeString(self.db_name.encode('utf-8') if sys.version_info[0] == 2 else self.db_name)
            oprot.writeFieldEnd()
        if self.table_name is not None:
            oprot.writeFieldBegin('table_name', TType.STRING, 2)
            oprot.writeString(self.table_name.encode('utf-8') if sys.version_info[0] == 2 else self.table_name)
            oprot.writeFieldEnd()
        if self.session_id is not None:
            oprot.writeFieldBegin('session_id', TType.I64, 3)
            oprot.writeI64(self.session_id)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class ListIndexResponse(object):
    """
    Attributes:
     - error_code
     - error_msg
     - index_names

    """
    thrift_spec = None


    def __init__(self, error_code = None, error_msg = None, index_names = [
    ],):
        self.error_code = error_code
        self.error_msg = error_msg
        if index_names is self.thrift_spec[3][4]:
            index_names = [
            ]
        self.index_names = index_names

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.I64:
                    self.error_code = iprot.readI64()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRING:
                    self.error_msg = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.LIST:
                    self.index_names = []
                    (_etype290, _size287) = iprot.readListBegin()
                    for _i291 in range(_size287):
                        _elem292 = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                        self.index_names.append(_elem292)
                    iprot.readListEnd()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('ListIndexResponse')
        if self.error_code is not None:
            oprot.writeFieldBegin('error_code', TType.I64, 1)
            oprot.writeI64(self.error_code)
            oprot.writeFieldEnd()
        if self.error_msg is not None:
            oprot.writeFieldBegin('error_msg', TType.STRING, 2)
            oprot.writeString(self.error_msg.encode('utf-8') if sys.version_info[0] == 2 else self.error_msg)
            oprot.writeFieldEnd()
        if self.index_names is not None:
            oprot.writeFieldBegin('index_names', TType.LIST, 3)
            oprot.writeListBegin(TType.STRING, len(self.index_names))
            for iter293 in self.index_names:
                oprot.writeString(iter293.encode('utf-8') if sys.version_info[0] == 2 else iter293)
            oprot.writeListEnd()
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class ShowDatabaseRequest(object):
    """
    Attributes:
     - db_name
     - session_id

    """
    thrift_spec = None


    def __init__(self, db_name = None, session_id = None,):
        self.db_name = db_name
        self.session_id = session_id

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.STRING:
                    self.db_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.I64:
                    self.session_id = iprot.readI64()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('ShowDatabaseRequest')
        if self.db_name is not None:
            oprot.writeFieldBegin('db_name', TType.STRING, 1)
            oprot.writeString(self.db_name.encode('utf-8') if sys.version_info[0] == 2 else self.db_name)
            oprot.writeFieldEnd()
        if self.session_id is not None:
            oprot.writeFieldBegin('session_id', TType.I64, 2)
            oprot.writeI64(self.session_id)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class ShowDatabaseResponse(object):
    """
    Attributes:
     - error_code
     - error_msg
     - database_name
     - store_dir
     - table_count
     - comment

    """
    thrift_spec = None


    def __init__(self, error_code = None, error_msg = None, database_name = None, store_dir = None, table_count = None, comment = None,):
        self.error_code = error_code
        self.error_msg = error_msg
        self.database_name = database_name
        self.store_dir = store_dir
        self.table_count = table_count
        self.comment = comment

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.I64:
                    self.error_code = iprot.readI64()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRING:
                    self.error_msg = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.STRING:
                    self.database_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 4:
                if ftype == TType.STRING:
                    self.store_dir = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 5:
                if ftype == TType.I64:
                    self.table_count = iprot.readI64()
                else:
                    iprot.skip(ftype)
            elif fid == 6:
                if ftype == TType.STRING:
                    self.comment = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('ShowDatabaseResponse')
        if self.error_code is not None:
            oprot.writeFieldBegin('error_code', TType.I64, 1)
            oprot.writeI64(self.error_code)
            oprot.writeFieldEnd()
        if self.error_msg is not None:
            oprot.writeFieldBegin('error_msg', TType.STRING, 2)
            oprot.writeString(self.error_msg.encode('utf-8') if sys.version_info[0] == 2 else self.error_msg)
            oprot.writeFieldEnd()
        if self.database_name is not None:
            oprot.writeFieldBegin('database_name', TType.STRING, 3)
            oprot.writeString(self.database_name.encode('utf-8') if sys.version_info[0] == 2 else self.database_name)
            oprot.writeFieldEnd()
        if self.store_dir is not None:
            oprot.writeFieldBegin('store_dir', TType.STRING, 4)
            oprot.writeString(self.store_dir.encode('utf-8') if sys.version_info[0] == 2 else self.store_dir)
            oprot.writeFieldEnd()
        if self.table_count is not None:
            oprot.writeFieldBegin('table_count', TType.I64, 5)
            oprot.writeI64(self.table_count)
            oprot.writeFieldEnd()
        if self.comment is not None:
            oprot.writeFieldBegin('comment', TType.STRING, 6)
            oprot.writeString(self.comment.encode('utf-8') if sys.version_info[0] == 2 else self.comment)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class ShowTableRequest(object):
    """
    Attributes:
     - db_name
     - table_name
     - session_id

    """
    thrift_spec = None


    def __init__(self, db_name = None, table_name = None, session_id = None,):
        self.db_name = db_name
        self.table_name = table_name
        self.session_id = session_id

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.STRING:
                    self.db_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRING:
                    self.table_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.I64:
                    self.session_id = iprot.readI64()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('ShowTableRequest')
        if self.db_name is not None:
            oprot.writeFieldBegin('db_name', TType.STRING, 1)
            oprot.writeString(self.db_name.encode('utf-8') if sys.version_info[0] == 2 else self.db_name)
            oprot.writeFieldEnd()
        if self.table_name is not None:
            oprot.writeFieldBegin('table_name', TType.STRING, 2)
            oprot.writeString(self.table_name.encode('utf-8') if sys.version_info[0] == 2 else self.table_name)
            oprot.writeFieldEnd()
        if self.session_id is not None:
            oprot.writeFieldBegin('session_id', TType.I64, 3)
            oprot.writeI64(self.session_id)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class ShowTableResponse(object):
    """
    Attributes:
     - error_code
     - error_msg
     - database_name
     - table_name
     - store_dir
     - column_count
     - segment_count
     - row_count

    """
    thrift_spec = None


    def __init__(self, error_code = None, error_msg = None, database_name = None, table_name = None, store_dir = None, column_count = None, segment_count = None, row_count = None,):
        self.error_code = error_code
        self.error_msg = error_msg
        self.database_name = database_name
        self.table_name = table_name
        self.store_dir = store_dir
        self.column_count = column_count
        self.segment_count = segment_count
        self.row_count = row_count

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.I64:
                    self.error_code = iprot.readI64()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRING:
                    self.error_msg = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.STRING:
                    self.database_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 4:
                if ftype == TType.STRING:
                    self.table_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 5:
                if ftype == TType.STRING:
                    self.store_dir = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 6:
                if ftype == TType.I64:
                    self.column_count = iprot.readI64()
                else:
                    iprot.skip(ftype)
            elif fid == 7:
                if ftype == TType.I64:
                    self.segment_count = iprot.readI64()
                else:
                    iprot.skip(ftype)
            elif fid == 8:
                if ftype == TType.I64:
                    self.row_count = iprot.readI64()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('ShowTableResponse')
        if self.error_code is not None:
            oprot.writeFieldBegin('error_code', TType.I64, 1)
            oprot.writeI64(self.error_code)
            oprot.writeFieldEnd()
        if self.error_msg is not None:
            oprot.writeFieldBegin('error_msg', TType.STRING, 2)
            oprot.writeString(self.error_msg.encode('utf-8') if sys.version_info[0] == 2 else self.error_msg)
            oprot.writeFieldEnd()
        if self.database_name is not None:
            oprot.writeFieldBegin('database_name', TType.STRING, 3)
            oprot.writeString(self.database_name.encode('utf-8') if sys.version_info[0] == 2 else self.database_name)
            oprot.writeFieldEnd()
        if self.table_name is not None:
            oprot.writeFieldBegin('table_name', TType.STRING, 4)
            oprot.writeString(self.table_name.encode('utf-8') if sys.version_info[0] == 2 else self.table_name)
            oprot.writeFieldEnd()
        if self.store_dir is not None:
            oprot.writeFieldBegin('store_dir', TType.STRING, 5)
            oprot.writeString(self.store_dir.encode('utf-8') if sys.version_info[0] == 2 else self.store_dir)
            oprot.writeFieldEnd()
        if self.column_count is not None:
            oprot.writeFieldBegin('column_count', TType.I64, 6)
            oprot.writeI64(self.column_count)
            oprot.writeFieldEnd()
        if self.segment_count is not None:
            oprot.writeFieldBegin('segment_count', TType.I64, 7)
            oprot.writeI64(self.segment_count)
            oprot.writeFieldEnd()
        if self.row_count is not None:
            oprot.writeFieldBegin('row_count', TType.I64, 8)
            oprot.writeI64(self.row_count)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class ShowColumnsRequest(object):
    """
    Attributes:
     - db_name
     - table_name
     - session_id

    """
    thrift_spec = None


    def __init__(self, db_name = None, table_name = None, session_id = None,):
        self.db_name = db_name
        self.table_name = table_name
        self.session_id = session_id

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.STRING:
                    self.db_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRING:
                    self.table_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.I64:
                    self.session_id = iprot.readI64()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('ShowColumnsRequest')
        if self.db_name is not None:
            oprot.writeFieldBegin('db_name', TType.STRING, 1)
            oprot.writeString(self.db_name.encode('utf-8') if sys.version_info[0] == 2 else self.db_name)
            oprot.writeFieldEnd()
        if self.table_name is not None:
            oprot.writeFieldBegin('table_name', TType.STRING, 2)
            oprot.writeString(self.table_name.encode('utf-8') if sys.version_info[0] == 2 else self.table_name)
            oprot.writeFieldEnd()
        if self.session_id is not None:
            oprot.writeFieldBegin('session_id', TType.I64, 3)
            oprot.writeI64(self.session_id)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class GetTableRequest(object):
    """
    Attributes:
     - db_name
     - table_name
     - session_id

    """
    thrift_spec = None


    def __init__(self, db_name = None, table_name = None, session_id = None,):
        self.db_name = db_name
        self.table_name = table_name
        self.session_id = session_id

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.STRING:
                    self.db_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRING:
                    self.table_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.I64:
                    self.session_id = iprot.readI64()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('GetTableRequest')
        if self.db_name is not None:
            oprot.writeFieldBegin('db_name', TType.STRING, 1)
            oprot.writeString(self.db_name.encode('utf-8') if sys.version_info[0] == 2 else self.db_name)
            oprot.writeFieldEnd()
        if self.table_name is not None:
            oprot.writeFieldBegin('table_name', TType.STRING, 2)
            oprot.writeString(self.table_name.encode('utf-8') if sys.version_info[0] == 2 else self.table_name)
            oprot.writeFieldEnd()
        if self.session_id is not None:
            oprot.writeFieldBegin('session_id', TType.I64, 3)
            oprot.writeI64(self.session_id)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class IndexInfo(object):
    """
    Attributes:
     - column_name
     - index_type
     - index_param_list

    """
    thrift_spec = None


    def __init__(self, column_name = None, index_type = None, index_param_list = [
    ],):
        self.column_name = column_name
        self.index_type = index_type
        if index_param_list is self.thrift_spec[3][4]:
            index_param_list = [
            ]
        self.index_param_list = index_param_list

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.STRING:
                    self.column_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.I32:
                    self.index_type = iprot.readI32()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.LIST:
                    self.index_param_list = []
                    (_etype297, _size294) = iprot.readListBegin()
                    for _i298 in range(_size294):
                        _elem299 = InitParameter()
                        _elem299.read(iprot)
                        self.index_param_list.append(_elem299)
                    iprot.readListEnd()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('IndexInfo')
        if self.column_name is not None:
            oprot.writeFieldBegin('column_name', TType.STRING, 1)
            oprot.writeString(self.column_name.encode('utf-8') if sys.version_info[0] == 2 else self.column_name)
            oprot.writeFieldEnd()
        if self.index_type is not None:
            oprot.writeFieldBegin('index_type', TType.I32, 2)
            oprot.writeI32(self.index_type)
            oprot.writeFieldEnd()
        if self.index_param_list is not None:
            oprot.writeFieldBegin('index_param_list', TType.LIST, 3)
            oprot.writeListBegin(TType.STRUCT, len(self.index_param_list))
            for iter300 in self.index_param_list:
                iter300.write(oprot)
            oprot.writeListEnd()
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class CreateIndexRequest(object):
    """
    Attributes:
     - db_name
     - table_name
     - index_name
     - index_comment
     - index_info
     - session_id
     - create_option

    """
    thrift_spec = None


    def __init__(self, db_name = None, table_name = None, index_name = None, index_comment = None, index_info = None, session_id = None, create_option = None,):
        self.db_name = db_name
        self.table_name = table_name
        self.index_name = index_name
        self.index_comment = index_comment
        self.index_info = index_info
        self.session_id = session_id
        self.create_option = create_option

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.STRING:
                    self.db_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRING:
                    self.table_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.STRING:
                    self.index_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 4:
                if ftype == TType.STRING:
                    self.index_comment = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 5:
                if ftype == TType.STRUCT:
                    self.index_info = IndexInfo()
                    self.index_info.read(iprot)
                else:
                    iprot.skip(ftype)
            elif fid == 6:
                if ftype == TType.I64:
                    self.session_id = iprot.readI64()
                else:
                    iprot.skip(ftype)
            elif fid == 7:
                if ftype == TType.STRUCT:
                    self.create_option = CreateOption()
                    self.create_option.read(iprot)
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('CreateIndexRequest')
        if self.db_name is not None:
            oprot.writeFieldBegin('db_name', TType.STRING, 1)
            oprot.writeString(self.db_name.encode('utf-8') if sys.version_info[0] == 2 else self.db_name)
            oprot.writeFieldEnd()
        if self.table_name is not None:
            oprot.writeFieldBegin('table_name', TType.STRING, 2)
            oprot.writeString(self.table_name.encode('utf-8') if sys.version_info[0] == 2 else self.table_name)
            oprot.writeFieldEnd()
        if self.index_name is not None:
            oprot.writeFieldBegin('index_name', TType.STRING, 3)
            oprot.writeString(self.index_name.encode('utf-8') if sys.version_info[0] == 2 else self.index_name)
            oprot.writeFieldEnd()
        if self.index_comment is not None:
            oprot.writeFieldBegin('index_comment', TType.STRING, 4)
            oprot.writeString(self.index_comment.encode('utf-8') if sys.version_info[0] == 2 else self.index_comment)
            oprot.writeFieldEnd()
        if self.index_info is not None:
            oprot.writeFieldBegin('index_info', TType.STRUCT, 5)
            self.index_info.write(oprot)
            oprot.writeFieldEnd()
        if self.session_id is not None:
            oprot.writeFieldBegin('session_id', TType.I64, 6)
            oprot.writeI64(self.session_id)
            oprot.writeFieldEnd()
        if self.create_option is not None:
            oprot.writeFieldBegin('create_option', TType.STRUCT, 7)
            self.create_option.write(oprot)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class DropIndexRequest(object):
    """
    Attributes:
     - db_name
     - table_name
     - index_name
     - session_id
     - drop_option

    """
    thrift_spec = None


    def __init__(self, db_name = None, table_name = None, index_name = None, session_id = None, drop_option = None,):
        self.db_name = db_name
        self.table_name = table_name
        self.index_name = index_name
        self.session_id = session_id
        self.drop_option = drop_option

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.STRING:
                    self.db_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRING:
                    self.table_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.STRING:
                    self.index_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 4:
                if ftype == TType.I64:
                    self.session_id = iprot.readI64()
                else:
                    iprot.skip(ftype)
            elif fid == 5:
                if ftype == TType.STRUCT:
                    self.drop_option = DropOption()
                    self.drop_option.read(iprot)
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('DropIndexRequest')
        if self.db_name is not None:
            oprot.writeFieldBegin('db_name', TType.STRING, 1)
            oprot.writeString(self.db_name.encode('utf-8') if sys.version_info[0] == 2 else self.db_name)
            oprot.writeFieldEnd()
        if self.table_name is not None:
            oprot.writeFieldBegin('table_name', TType.STRING, 2)
            oprot.writeString(self.table_name.encode('utf-8') if sys.version_info[0] == 2 else self.table_name)
            oprot.writeFieldEnd()
        if self.index_name is not None:
            oprot.writeFieldBegin('index_name', TType.STRING, 3)
            oprot.writeString(self.index_name.encode('utf-8') if sys.version_info[0] == 2 else self.index_name)
            oprot.writeFieldEnd()
        if self.session_id is not None:
            oprot.writeFieldBegin('session_id', TType.I64, 4)
            oprot.writeI64(self.session_id)
            oprot.writeFieldEnd()
        if self.drop_option is not None:
            oprot.writeFieldBegin('drop_option', TType.STRUCT, 5)
            self.drop_option.write(oprot)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class ShowIndexRequest(object):
    """
    Attributes:
     - db_name
     - table_name
     - index_name
     - session_id

    """
    thrift_spec = None


    def __init__(self, db_name = None, table_name = None, index_name = None, session_id = None,):
        self.db_name = db_name
        self.table_name = table_name
        self.index_name = index_name
        self.session_id = session_id

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.STRING:
                    self.db_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRING:
                    self.table_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.STRING:
                    self.index_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 4:
                if ftype == TType.I64:
                    self.session_id = iprot.readI64()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('ShowIndexRequest')
        if self.db_name is not None:
            oprot.writeFieldBegin('db_name', TType.STRING, 1)
            oprot.writeString(self.db_name.encode('utf-8') if sys.version_info[0] == 2 else self.db_name)
            oprot.writeFieldEnd()
        if self.table_name is not None:
            oprot.writeFieldBegin('table_name', TType.STRING, 2)
            oprot.writeString(self.table_name.encode('utf-8') if sys.version_info[0] == 2 else self.table_name)
            oprot.writeFieldEnd()
        if self.index_name is not None:
            oprot.writeFieldBegin('index_name', TType.STRING, 3)
            oprot.writeString(self.index_name.encode('utf-8') if sys.version_info[0] == 2 else self.index_name)
            oprot.writeFieldEnd()
        if self.session_id is not None:
            oprot.writeFieldBegin('session_id', TType.I64, 4)
            oprot.writeI64(self.session_id)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class ShowIndexResponse(object):
    """
    Attributes:
     - error_code
     - error_msg
     - db_name
     - table_name
     - index_name
     - index_comment
     - index_type
     - index_column_names
     - index_column_ids
     - other_parameters
     - store_dir
     - segment_index_count

    """
    thrift_spec = None


    def __init__(self, error_code = None, error_msg = None, db_name = None, table_name = None, index_name = None, index_comment = None, index_type = None, index_column_names = None, index_column_ids = None, other_parameters = None, store_dir = None, segment_index_count = None,):
        self.error_code = error_code
        self.error_msg = error_msg
        self.db_name = db_name
        self.table_name = table_name
        self.index_name = index_name
        self.index_comment = index_comment
        self.index_type = index_type
        self.index_column_names = index_column_names
        self.index_column_ids = index_column_ids
        self.other_parameters = other_parameters
        self.store_dir = store_dir
        self.segment_index_count = segment_index_count

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.I64:
                    self.error_code = iprot.readI64()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRING:
                    self.error_msg = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.STRING:
                    self.db_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 4:
                if ftype == TType.STRING:
                    self.table_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 5:
                if ftype == TType.STRING:
                    self.index_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 6:
                if ftype == TType.STRING:
                    self.index_comment = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 7:
                if ftype == TType.STRING:
                    self.index_type = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 8:
                if ftype == TType.STRING:
                    self.index_column_names = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 9:
                if ftype == TType.STRING:
                    self.index_column_ids = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 10:
                if ftype == TType.STRING:
                    self.other_parameters = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 11:
                if ftype == TType.STRING:
                    self.store_dir = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 12:
                if ftype == TType.STRING:
                    self.segment_index_count = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('ShowIndexResponse')
        if self.error_code is not None:
            oprot.writeFieldBegin('error_code', TType.I64, 1)
            oprot.writeI64(self.error_code)
            oprot.writeFieldEnd()
        if self.error_msg is not None:
            oprot.writeFieldBegin('error_msg', TType.STRING, 2)
            oprot.writeString(self.error_msg.encode('utf-8') if sys.version_info[0] == 2 else self.error_msg)
            oprot.writeFieldEnd()
        if self.db_name is not None:
            oprot.writeFieldBegin('db_name', TType.STRING, 3)
            oprot.writeString(self.db_name.encode('utf-8') if sys.version_info[0] == 2 else self.db_name)
            oprot.writeFieldEnd()
        if self.table_name is not None:
            oprot.writeFieldBegin('table_name', TType.STRING, 4)
            oprot.writeString(self.table_name.encode('utf-8') if sys.version_info[0] == 2 else self.table_name)
            oprot.writeFieldEnd()
        if self.index_name is not None:
            oprot.writeFieldBegin('index_name', TType.STRING, 5)
            oprot.writeString(self.index_name.encode('utf-8') if sys.version_info[0] == 2 else self.index_name)
            oprot.writeFieldEnd()
        if self.index_comment is not None:
            oprot.writeFieldBegin('index_comment', TType.STRING, 6)
            oprot.writeString(self.index_comment.encode('utf-8') if sys.version_info[0] == 2 else self.index_comment)
            oprot.writeFieldEnd()
        if self.index_type is not None:
            oprot.writeFieldBegin('index_type', TType.STRING, 7)
            oprot.writeString(self.index_type.encode('utf-8') if sys.version_info[0] == 2 else self.index_type)
            oprot.writeFieldEnd()
        if self.index_column_names is not None:
            oprot.writeFieldBegin('index_column_names', TType.STRING, 8)
            oprot.writeString(self.index_column_names.encode('utf-8') if sys.version_info[0] == 2 else self.index_column_names)
            oprot.writeFieldEnd()
        if self.index_column_ids is not None:
            oprot.writeFieldBegin('index_column_ids', TType.STRING, 9)
            oprot.writeString(self.index_column_ids.encode('utf-8') if sys.version_info[0] == 2 else self.index_column_ids)
            oprot.writeFieldEnd()
        if self.other_parameters is not None:
            oprot.writeFieldBegin('other_parameters', TType.STRING, 10)
            oprot.writeString(self.other_parameters.encode('utf-8') if sys.version_info[0] == 2 else self.other_parameters)
            oprot.writeFieldEnd()
        if self.store_dir is not None:
            oprot.writeFieldBegin('store_dir', TType.STRING, 11)
            oprot.writeString(self.store_dir.encode('utf-8') if sys.version_info[0] == 2 else self.store_dir)
            oprot.writeFieldEnd()
        if self.segment_index_count is not None:
            oprot.writeFieldBegin('segment_index_count', TType.STRING, 12)
            oprot.writeString(self.segment_index_count.encode('utf-8') if sys.version_info[0] == 2 else self.segment_index_count)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class OptimizeRequest(object):
    """
    Attributes:
     - db_name
     - table_name
     - optimize_options
     - session_id

    """
    thrift_spec = None


    def __init__(self, db_name = None, table_name = None, optimize_options = None, session_id = None,):
        self.db_name = db_name
        self.table_name = table_name
        self.optimize_options = optimize_options
        self.session_id = session_id

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.STRING:
                    self.db_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRING:
                    self.table_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.STRUCT:
                    self.optimize_options = OptimizeOptions()
                    self.optimize_options.read(iprot)
                else:
                    iprot.skip(ftype)
            elif fid == 4:
                if ftype == TType.I64:
                    self.session_id = iprot.readI64()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('OptimizeRequest')
        if self.db_name is not None:
            oprot.writeFieldBegin('db_name', TType.STRING, 1)
            oprot.writeString(self.db_name.encode('utf-8') if sys.version_info[0] == 2 else self.db_name)
            oprot.writeFieldEnd()
        if self.table_name is not None:
            oprot.writeFieldBegin('table_name', TType.STRING, 2)
            oprot.writeString(self.table_name.encode('utf-8') if sys.version_info[0] == 2 else self.table_name)
            oprot.writeFieldEnd()
        if self.optimize_options is not None:
            oprot.writeFieldBegin('optimize_options', TType.STRUCT, 3)
            self.optimize_options.write(oprot)
            oprot.writeFieldEnd()
        if self.session_id is not None:
            oprot.writeFieldBegin('session_id', TType.I64, 4)
            oprot.writeI64(self.session_id)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class GetDatabaseRequest(object):
    """
    Attributes:
     - db_name
     - session_id

    """
    thrift_spec = None


    def __init__(self, db_name = None, session_id = None,):
        self.db_name = db_name
        self.session_id = session_id

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.STRING:
                    self.db_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.I64:
                    self.session_id = iprot.readI64()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('GetDatabaseRequest')
        if self.db_name is not None:
            oprot.writeFieldBegin('db_name', TType.STRING, 1)
            oprot.writeString(self.db_name.encode('utf-8') if sys.version_info[0] == 2 else self.db_name)
            oprot.writeFieldEnd()
        if self.session_id is not None:
            oprot.writeFieldBegin('session_id', TType.I64, 2)
            oprot.writeI64(self.session_id)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class CreateDatabaseRequest(object):
    """
    Attributes:
     - db_name
     - session_id
     - create_option
     - db_comment

    """
    thrift_spec = None


    def __init__(self, db_name = None, session_id = None, create_option = None, db_comment = None,):
        self.db_name = db_name
        self.session_id = session_id
        self.create_option = create_option
        self.db_comment = db_comment

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.STRING:
                    self.db_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.I64:
                    self.session_id = iprot.readI64()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.STRUCT:
                    self.create_option = CreateOption()
                    self.create_option.read(iprot)
                else:
                    iprot.skip(ftype)
            elif fid == 4:
                if ftype == TType.STRING:
                    self.db_comment = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('CreateDatabaseRequest')
        if self.db_name is not None:
            oprot.writeFieldBegin('db_name', TType.STRING, 1)
            oprot.writeString(self.db_name.encode('utf-8') if sys.version_info[0] == 2 else self.db_name)
            oprot.writeFieldEnd()
        if self.session_id is not None:
            oprot.writeFieldBegin('session_id', TType.I64, 2)
            oprot.writeI64(self.session_id)
            oprot.writeFieldEnd()
        if self.create_option is not None:
            oprot.writeFieldBegin('create_option', TType.STRUCT, 3)
            self.create_option.write(oprot)
            oprot.writeFieldEnd()
        if self.db_comment is not None:
            oprot.writeFieldBegin('db_comment', TType.STRING, 4)
            oprot.writeString(self.db_comment.encode('utf-8') if sys.version_info[0] == 2 else self.db_comment)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class DropDatabaseRequest(object):
    """
    Attributes:
     - db_name
     - session_id
     - drop_option

    """
    thrift_spec = None


    def __init__(self, db_name = None, session_id = None, drop_option = None,):
        self.db_name = db_name
        self.session_id = session_id
        self.drop_option = drop_option

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.STRING:
                    self.db_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.I64:
                    self.session_id = iprot.readI64()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.STRUCT:
                    self.drop_option = DropOption()
                    self.drop_option.read(iprot)
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('DropDatabaseRequest')
        if self.db_name is not None:
            oprot.writeFieldBegin('db_name', TType.STRING, 1)
            oprot.writeString(self.db_name.encode('utf-8') if sys.version_info[0] == 2 else self.db_name)
            oprot.writeFieldEnd()
        if self.session_id is not None:
            oprot.writeFieldBegin('session_id', TType.I64, 2)
            oprot.writeI64(self.session_id)
            oprot.writeFieldEnd()
        if self.drop_option is not None:
            oprot.writeFieldBegin('drop_option', TType.STRUCT, 3)
            self.drop_option.write(oprot)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class CreateTableRequest(object):
    """
    Attributes:
     - db_name
     - table_name
     - column_defs
     - session_id
     - create_option

    """
    thrift_spec = None


    def __init__(self, db_name = None, table_name = None, column_defs = [
    ], session_id = None, create_option = None,):
        self.db_name = db_name
        self.table_name = table_name
        if column_defs is self.thrift_spec[3][4]:
            column_defs = [
            ]
        self.column_defs = column_defs
        self.session_id = session_id
        self.create_option = create_option

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.STRING:
                    self.db_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRING:
                    self.table_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.LIST:
                    self.column_defs = []
                    (_etype304, _size301) = iprot.readListBegin()
                    for _i305 in range(_size301):
                        _elem306 = ColumnDef()
                        _elem306.read(iprot)
                        self.column_defs.append(_elem306)
                    iprot.readListEnd()
                else:
                    iprot.skip(ftype)
            elif fid == 6:
                if ftype == TType.I64:
                    self.session_id = iprot.readI64()
                else:
                    iprot.skip(ftype)
            elif fid == 7:
                if ftype == TType.STRUCT:
                    self.create_option = CreateOption()
                    self.create_option.read(iprot)
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('CreateTableRequest')
        if self.db_name is not None:
            oprot.writeFieldBegin('db_name', TType.STRING, 1)
            oprot.writeString(self.db_name.encode('utf-8') if sys.version_info[0] == 2 else self.db_name)
            oprot.writeFieldEnd()
        if self.table_name is not None:
            oprot.writeFieldBegin('table_name', TType.STRING, 2)
            oprot.writeString(self.table_name.encode('utf-8') if sys.version_info[0] == 2 else self.table_name)
            oprot.writeFieldEnd()
        if self.column_defs is not None:
            oprot.writeFieldBegin('column_defs', TType.LIST, 3)
            oprot.writeListBegin(TType.STRUCT, len(self.column_defs))
            for iter307 in self.column_defs:
                iter307.write(oprot)
            oprot.writeListEnd()
            oprot.writeFieldEnd()
        if self.session_id is not None:
            oprot.writeFieldBegin('session_id', TType.I64, 6)
            oprot.writeI64(self.session_id)
            oprot.writeFieldEnd()
        if self.create_option is not None:
            oprot.writeFieldBegin('create_option', TType.STRUCT, 7)
            self.create_option.write(oprot)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class DropTableRequest(object):
    """
    Attributes:
     - db_name
     - table_name
     - session_id
     - drop_option

    """
    thrift_spec = None


    def __init__(self, db_name = None, table_name = None, session_id = None, drop_option = None,):
        self.db_name = db_name
        self.table_name = table_name
        self.session_id = session_id
        self.drop_option = drop_option

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.STRING:
                    self.db_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRING:
                    self.table_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.I64:
                    self.session_id = iprot.readI64()
                else:
                    iprot.skip(ftype)
            elif fid == 4:
                if ftype == TType.STRUCT:
                    self.drop_option = DropOption()
                    self.drop_option.read(iprot)
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('DropTableRequest')
        if self.db_name is not None:
            oprot.writeFieldBegin('db_name', TType.STRING, 1)
            oprot.writeString(self.db_name.encode('utf-8') if sys.version_info[0] == 2 else self.db_name)
            oprot.writeFieldEnd()
        if self.table_name is not None:
            oprot.writeFieldBegin('table_name', TType.STRING, 2)
            oprot.writeString(self.table_name.encode('utf-8') if sys.version_info[0] == 2 else self.table_name)
            oprot.writeFieldEnd()
        if self.session_id is not None:
            oprot.writeFieldBegin('session_id', TType.I64, 3)
            oprot.writeI64(self.session_id)
            oprot.writeFieldEnd()
        if self.drop_option is not None:
            oprot.writeFieldBegin('drop_option', TType.STRUCT, 4)
            self.drop_option.write(oprot)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class InsertRequest(object):
    """
    Attributes:
     - db_name
     - table_name
     - fields
     - session_id

    """
    thrift_spec = None


    def __init__(self, db_name = None, table_name = None, fields = [
    ], session_id = None,):
        self.db_name = db_name
        self.table_name = table_name
        if fields is self.thrift_spec[3][4]:
            fields = [
            ]
        self.fields = fields
        self.session_id = session_id

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.STRING:
                    self.db_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRING:
                    self.table_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.LIST:
                    self.fields = []
                    (_etype311, _size308) = iprot.readListBegin()
                    for _i312 in range(_size308):
                        _elem313 = Field()
                        _elem313.read(iprot)
                        self.fields.append(_elem313)
                    iprot.readListEnd()
                else:
                    iprot.skip(ftype)
            elif fid == 4:
                if ftype == TType.I64:
                    self.session_id = iprot.readI64()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('InsertRequest')
        if self.db_name is not None:
            oprot.writeFieldBegin('db_name', TType.STRING, 1)
            oprot.writeString(self.db_name.encode('utf-8') if sys.version_info[0] == 2 else self.db_name)
            oprot.writeFieldEnd()
        if self.table_name is not None:
            oprot.writeFieldBegin('table_name', TType.STRING, 2)
            oprot.writeString(self.table_name.encode('utf-8') if sys.version_info[0] == 2 else self.table_name)
            oprot.writeFieldEnd()
        if self.fields is not None:
            oprot.writeFieldBegin('fields', TType.LIST, 3)
            oprot.writeListBegin(TType.STRUCT, len(self.fields))
            for iter314 in self.fields:
                iter314.write(oprot)
            oprot.writeListEnd()
            oprot.writeFieldEnd()
        if self.session_id is not None:
            oprot.writeFieldBegin('session_id', TType.I64, 4)
            oprot.writeI64(self.session_id)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class ImportRequest(object):
    """
    Attributes:
     - db_name
     - table_name
     - file_name
     - import_option
     - session_id

    """
    thrift_spec = None


    def __init__(self, db_name = None, table_name = None, file_name = None, import_option = None, session_id = None,):
        self.db_name = db_name
        self.table_name = table_name
        self.file_name = file_name
        self.import_option = import_option
        self.session_id = session_id

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.STRING:
                    self.db_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRING:
                    self.table_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.STRING:
                    self.file_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 4:
                if ftype == TType.STRUCT:
                    self.import_option = ImportOption()
                    self.import_option.read(iprot)
                else:
                    iprot.skip(ftype)
            elif fid == 5:
                if ftype == TType.I64:
                    self.session_id = iprot.readI64()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('ImportRequest')
        if self.db_name is not None:
            oprot.writeFieldBegin('db_name', TType.STRING, 1)
            oprot.writeString(self.db_name.encode('utf-8') if sys.version_info[0] == 2 else self.db_name)
            oprot.writeFieldEnd()
        if self.table_name is not None:
            oprot.writeFieldBegin('table_name', TType.STRING, 2)
            oprot.writeString(self.table_name.encode('utf-8') if sys.version_info[0] == 2 else self.table_name)
            oprot.writeFieldEnd()
        if self.file_name is not None:
            oprot.writeFieldBegin('file_name', TType.STRING, 3)
            oprot.writeString(self.file_name.encode('utf-8') if sys.version_info[0] == 2 else self.file_name)
            oprot.writeFieldEnd()
        if self.import_option is not None:
            oprot.writeFieldBegin('import_option', TType.STRUCT, 4)
            self.import_option.write(oprot)
            oprot.writeFieldEnd()
        if self.session_id is not None:
            oprot.writeFieldBegin('session_id', TType.I64, 5)
            oprot.writeI64(self.session_id)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class ExportRequest(object):
    """
    Attributes:
     - db_name
     - table_name
     - columns
     - file_name
     - export_option
     - session_id

    """
    thrift_spec = None


    def __init__(self, db_name = None, table_name = None, columns = None, file_name = None, export_option = None, session_id = None,):
        self.db_name = db_name
        self.table_name = table_name
        self.columns = columns
        self.file_name = file_name
        self.export_option = export_option
        self.session_id = session_id

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.STRING:
                    self.db_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRING:
                    self.table_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.LIST:
                    self.columns = []
                    (_etype318, _size315) = iprot.readListBegin()
                    for _i319 in range(_size315):
                        _elem320 = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                        self.columns.append(_elem320)
                    iprot.readListEnd()
                else:
                    iprot.skip(ftype)
            elif fid == 4:
                if ftype == TType.STRING:
                    self.file_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 5:
                if ftype == TType.STRUCT:
                    self.export_option = ExportOption()
                    self.export_option.read(iprot)
                else:
                    iprot.skip(ftype)
            elif fid == 6:
                if ftype == TType.I64:
                    self.session_id = iprot.readI64()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('ExportRequest')
        if self.db_name is not None:
            oprot.writeFieldBegin('db_name', TType.STRING, 1)
            oprot.writeString(self.db_name.encode('utf-8') if sys.version_info[0] == 2 else self.db_name)
            oprot.writeFieldEnd()
        if self.table_name is not None:
            oprot.writeFieldBegin('table_name', TType.STRING, 2)
            oprot.writeString(self.table_name.encode('utf-8') if sys.version_info[0] == 2 else self.table_name)
            oprot.writeFieldEnd()
        if self.columns is not None:
            oprot.writeFieldBegin('columns', TType.LIST, 3)
            oprot.writeListBegin(TType.STRING, len(self.columns))
            for iter321 in self.columns:
                oprot.writeString(iter321.encode('utf-8') if sys.version_info[0] == 2 else iter321)
            oprot.writeListEnd()
            oprot.writeFieldEnd()
        if self.file_name is not None:
            oprot.writeFieldBegin('file_name', TType.STRING, 4)
            oprot.writeString(self.file_name.encode('utf-8') if sys.version_info[0] == 2 else self.file_name)
            oprot.writeFieldEnd()
        if self.export_option is not None:
            oprot.writeFieldBegin('export_option', TType.STRUCT, 5)
            self.export_option.write(oprot)
            oprot.writeFieldEnd()
        if self.session_id is not None:
            oprot.writeFieldBegin('session_id', TType.I64, 6)
            oprot.writeI64(self.session_id)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class ExplainRequest(object):
    """
    Attributes:
     - session_id
     - db_name
     - table_name
     - select_list
     - highlight_list
     - search_expr
     - where_expr
     - group_by_list
     - having_expr
     - limit_expr
     - offset_expr
     - order_by_list
     - explain_type

    """
    thrift_spec = None


    def __init__(self, session_id = None, db_name = None, table_name = None, select_list = [
    ], highlight_list = [
    ], search_expr = None, where_expr = None, group_by_list = [
    ], having_expr = None, limit_expr = None, offset_expr = None, order_by_list = [
    ], explain_type = None,):
        self.session_id = session_id
        self.db_name = db_name
        self.table_name = table_name
        if select_list is self.thrift_spec[4][4]:
            select_list = [
            ]
        self.select_list = select_list
        if highlight_list is self.thrift_spec[5][4]:
            highlight_list = [
            ]
        self.highlight_list = highlight_list
        self.search_expr = search_expr
        self.where_expr = where_expr
        if group_by_list is self.thrift_spec[8][4]:
            group_by_list = [
            ]
        self.group_by_list = group_by_list
        self.having_expr = having_expr
        self.limit_expr = limit_expr
        self.offset_expr = offset_expr
        if order_by_list is self.thrift_spec[12][4]:
            order_by_list = [
            ]
        self.order_by_list = order_by_list
        self.explain_type = explain_type

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.I64:
                    self.session_id = iprot.readI64()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRING:
                    self.db_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.STRING:
                    self.table_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 4:
                if ftype == TType.LIST:
                    self.select_list = []
                    (_etype325, _size322) = iprot.readListBegin()
                    for _i326 in range(_size322):
                        _elem327 = ParsedExpr()
                        _elem327.read(iprot)
                        self.select_list.append(_elem327)
                    iprot.readListEnd()
                else:
                    iprot.skip(ftype)
            elif fid == 5:
                if ftype == TType.LIST:
                    self.highlight_list = []
                    (_etype331, _size328) = iprot.readListBegin()
                    for _i332 in range(_size328):
                        _elem333 = ParsedExpr()
                        _elem333.read(iprot)
                        self.highlight_list.append(_elem333)
                    iprot.readListEnd()
                else:
                    iprot.skip(ftype)
            elif fid == 6:
                if ftype == TType.STRUCT:
                    self.search_expr = SearchExpr()
                    self.search_expr.read(iprot)
                else:
                    iprot.skip(ftype)
            elif fid == 7:
                if ftype == TType.STRUCT:
                    self.where_expr = ParsedExpr()
                    self.where_expr.read(iprot)
                else:
                    iprot.skip(ftype)
            elif fid == 8:
                if ftype == TType.LIST:
                    self.group_by_list = []
                    (_etype337, _size334) = iprot.readListBegin()
                    for _i338 in range(_size334):
                        _elem339 = ParsedExpr()
                        _elem339.read(iprot)
                        self.group_by_list.append(_elem339)
                    iprot.readListEnd()
                else:
                    iprot.skip(ftype)
            elif fid == 9:
                if ftype == TType.STRUCT:
                    self.having_expr = ParsedExpr()
                    self.having_expr.read(iprot)
                else:
                    iprot.skip(ftype)
            elif fid == 10:
                if ftype == TType.STRUCT:
                    self.limit_expr = ParsedExpr()
                    self.limit_expr.read(iprot)
                else:
                    iprot.skip(ftype)
            elif fid == 11:
                if ftype == TType.STRUCT:
                    self.offset_expr = ParsedExpr()
                    self.offset_expr.read(iprot)
                else:
                    iprot.skip(ftype)
            elif fid == 12:
                if ftype == TType.LIST:
                    self.order_by_list = []
                    (_etype343, _size340) = iprot.readListBegin()
                    for _i344 in range(_size340):
                        _elem345 = OrderByExpr()
                        _elem345.read(iprot)
                        self.order_by_list.append(_elem345)
                    iprot.readListEnd()
                else:
                    iprot.skip(ftype)
            elif fid == 13:
                if ftype == TType.I32:
                    self.explain_type = iprot.readI32()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('ExplainRequest')
        if self.session_id is not None:
            oprot.writeFieldBegin('session_id', TType.I64, 1)
            oprot.writeI64(self.session_id)
            oprot.writeFieldEnd()
        if self.db_name is not None:
            oprot.writeFieldBegin('db_name', TType.STRING, 2)
            oprot.writeString(self.db_name.encode('utf-8') if sys.version_info[0] == 2 else self.db_name)
            oprot.writeFieldEnd()
        if self.table_name is not None:
            oprot.writeFieldBegin('table_name', TType.STRING, 3)
            oprot.writeString(self.table_name.encode('utf-8') if sys.version_info[0] == 2 else self.table_name)
            oprot.writeFieldEnd()
        if self.select_list is not None:
            oprot.writeFieldBegin('select_list', TType.LIST, 4)
            oprot.writeListBegin(TType.STRUCT, len(self.select_list))
            for iter346 in self.select_list:
                iter346.write(oprot)
            oprot.writeListEnd()
            oprot.writeFieldEnd()
        if self.highlight_list is not None:
            oprot.writeFieldBegin('highlight_list', TType.LIST, 5)
            oprot.writeListBegin(TType.STRUCT, len(self.highlight_list))
            for iter347 in self.highlight_list:
                iter347.write(oprot)
            oprot.writeListEnd()
            oprot.writeFieldEnd()
        if self.search_expr is not None:
            oprot.writeFieldBegin('search_expr', TType.STRUCT, 6)
            self.search_expr.write(oprot)
            oprot.writeFieldEnd()
        if self.where_expr is not None:
            oprot.writeFieldBegin('where_expr', TType.STRUCT, 7)
            self.where_expr.write(oprot)
            oprot.writeFieldEnd()
        if self.group_by_list is not None:
            oprot.writeFieldBegin('group_by_list', TType.LIST, 8)
            oprot.writeListBegin(TType.STRUCT, len(self.group_by_list))
            for iter348 in self.group_by_list:
                iter348.write(oprot)
            oprot.writeListEnd()
            oprot.writeFieldEnd()
        if self.having_expr is not None:
            oprot.writeFieldBegin('having_expr', TType.STRUCT, 9)
            self.having_expr.write(oprot)
            oprot.writeFieldEnd()
        if self.limit_expr is not None:
            oprot.writeFieldBegin('limit_expr', TType.STRUCT, 10)
            self.limit_expr.write(oprot)
            oprot.writeFieldEnd()
        if self.offset_expr is not None:
            oprot.writeFieldBegin('offset_expr', TType.STRUCT, 11)
            self.offset_expr.write(oprot)
            oprot.writeFieldEnd()
        if self.order_by_list is not None:
            oprot.writeFieldBegin('order_by_list', TType.LIST, 12)
            oprot.writeListBegin(TType.STRUCT, len(self.order_by_list))
            for iter349 in self.order_by_list:
                iter349.write(oprot)
            oprot.writeListEnd()
            oprot.writeFieldEnd()
        if self.explain_type is not None:
            oprot.writeFieldBegin('explain_type', TType.I32, 13)
            oprot.writeI32(self.explain_type)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class ExplainResponse(object):
    """
    Attributes:
     - error_code
     - error_msg
     - column_defs
     - column_fields

    """
    thrift_spec = None


    def __init__(self, error_code = None, error_msg = None, column_defs = [
    ], column_fields = [
    ],):
        self.error_code = error_code
        self.error_msg = error_msg
        if column_defs is self.thrift_spec[3][4]:
            column_defs = [
            ]
        self.column_defs = column_defs
        if column_fields is self.thrift_spec[4][4]:
            column_fields = [
            ]
        self.column_fields = column_fields

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.I64:
                    self.error_code = iprot.readI64()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRING:
                    self.error_msg = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.LIST:
                    self.column_defs = []
                    (_etype353, _size350) = iprot.readListBegin()
                    for _i354 in range(_size350):
                        _elem355 = ColumnDef()
                        _elem355.read(iprot)
                        self.column_defs.append(_elem355)
                    iprot.readListEnd()
                else:
                    iprot.skip(ftype)
            elif fid == 4:
                if ftype == TType.LIST:
                    self.column_fields = []
                    (_etype359, _size356) = iprot.readListBegin()
                    for _i360 in range(_size356):
                        _elem361 = ColumnField()
                        _elem361.read(iprot)
                        self.column_fields.append(_elem361)
                    iprot.readListEnd()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('ExplainResponse')
        if self.error_code is not None:
            oprot.writeFieldBegin('error_code', TType.I64, 1)
            oprot.writeI64(self.error_code)
            oprot.writeFieldEnd()
        if self.error_msg is not None:
            oprot.writeFieldBegin('error_msg', TType.STRING, 2)
            oprot.writeString(self.error_msg.encode('utf-8') if sys.version_info[0] == 2 else self.error_msg)
            oprot.writeFieldEnd()
        if self.column_defs is not None:
            oprot.writeFieldBegin('column_defs', TType.LIST, 3)
            oprot.writeListBegin(TType.STRUCT, len(self.column_defs))
            for iter362 in self.column_defs:
                iter362.write(oprot)
            oprot.writeListEnd()
            oprot.writeFieldEnd()
        if self.column_fields is not None:
            oprot.writeFieldBegin('column_fields', TType.LIST, 4)
            oprot.writeListBegin(TType.STRUCT, len(self.column_fields))
            for iter363 in self.column_fields:
                iter363.write(oprot)
            oprot.writeListEnd()
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class SelectRequest(object):
    """
    Attributes:
     - session_id
     - db_name
     - table_name
     - select_list
     - highlight_list
     - search_expr
     - where_expr
     - group_by_list
     - having_expr
     - limit_expr
     - offset_expr
     - order_by_list
     - total_hits_count

    """
    thrift_spec = None


    def __init__(self, session_id = None, db_name = None, table_name = None, select_list = [
    ], highlight_list = [
    ], search_expr = None, where_expr = None, group_by_list = [
    ], having_expr = None, limit_expr = None, offset_expr = None, order_by_list = [
    ], total_hits_count = None,):
        self.session_id = session_id
        self.db_name = db_name
        self.table_name = table_name
        if select_list is self.thrift_spec[4][4]:
            select_list = [
            ]
        self.select_list = select_list
        if highlight_list is self.thrift_spec[5][4]:
            highlight_list = [
            ]
        self.highlight_list = highlight_list
        self.search_expr = search_expr
        self.where_expr = where_expr
        if group_by_list is self.thrift_spec[8][4]:
            group_by_list = [
            ]
        self.group_by_list = group_by_list
        self.having_expr = having_expr
        self.limit_expr = limit_expr
        self.offset_expr = offset_expr
        if order_by_list is self.thrift_spec[12][4]:
            order_by_list = [
            ]
        self.order_by_list = order_by_list
        self.total_hits_count = total_hits_count

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.I64:
                    self.session_id = iprot.readI64()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRING:
                    self.db_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.STRING:
                    self.table_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 4:
                if ftype == TType.LIST:
                    self.select_list = []
                    (_etype367, _size364) = iprot.readListBegin()
                    for _i368 in range(_size364):
                        _elem369 = ParsedExpr()
                        _elem369.read(iprot)
                        self.select_list.append(_elem369)
                    iprot.readListEnd()
                else:
                    iprot.skip(ftype)
            elif fid == 5:
                if ftype == TType.LIST:
                    self.highlight_list = []
                    (_etype373, _size370) = iprot.readListBegin()
                    for _i374 in range(_size370):
                        _elem375 = ParsedExpr()
                        _elem375.read(iprot)
                        self.highlight_list.append(_elem375)
                    iprot.readListEnd()
                else:
                    iprot.skip(ftype)
            elif fid == 6:
                if ftype == TType.STRUCT:
                    self.search_expr = SearchExpr()
                    self.search_expr.read(iprot)
                else:
                    iprot.skip(ftype)
            elif fid == 7:
                if ftype == TType.STRUCT:
                    self.where_expr = ParsedExpr()
                    self.where_expr.read(iprot)
                else:
                    iprot.skip(ftype)
            elif fid == 8:
                if ftype == TType.LIST:
                    self.group_by_list = []
                    (_etype379, _size376) = iprot.readListBegin()
                    for _i380 in range(_size376):
                        _elem381 = ParsedExpr()
                        _elem381.read(iprot)
                        self.group_by_list.append(_elem381)
                    iprot.readListEnd()
                else:
                    iprot.skip(ftype)
            elif fid == 9:
                if ftype == TType.STRUCT:
                    self.having_expr = ParsedExpr()
                    self.having_expr.read(iprot)
                else:
                    iprot.skip(ftype)
            elif fid == 10:
                if ftype == TType.STRUCT:
                    self.limit_expr = ParsedExpr()
                    self.limit_expr.read(iprot)
                else:
                    iprot.skip(ftype)
            elif fid == 11:
                if ftype == TType.STRUCT:
                    self.offset_expr = ParsedExpr()
                    self.offset_expr.read(iprot)
                else:
                    iprot.skip(ftype)
            elif fid == 12:
                if ftype == TType.LIST:
                    self.order_by_list = []
                    (_etype385, _size382) = iprot.readListBegin()
                    for _i386 in range(_size382):
                        _elem387 = OrderByExpr()
                        _elem387.read(iprot)
                        self.order_by_list.append(_elem387)
                    iprot.readListEnd()
                else:
                    iprot.skip(ftype)
            elif fid == 13:
                if ftype == TType.BOOL:
                    self.total_hits_count = iprot.readBool()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('SelectRequest')
        if self.session_id is not None:
            oprot.writeFieldBegin('session_id', TType.I64, 1)
            oprot.writeI64(self.session_id)
            oprot.writeFieldEnd()
        if self.db_name is not None:
            oprot.writeFieldBegin('db_name', TType.STRING, 2)
            oprot.writeString(self.db_name.encode('utf-8') if sys.version_info[0] == 2 else self.db_name)
            oprot.writeFieldEnd()
        if self.table_name is not None:
            oprot.writeFieldBegin('table_name', TType.STRING, 3)
            oprot.writeString(self.table_name.encode('utf-8') if sys.version_info[0] == 2 else self.table_name)
            oprot.writeFieldEnd()
        if self.select_list is not None:
            oprot.writeFieldBegin('select_list', TType.LIST, 4)
            oprot.writeListBegin(TType.STRUCT, len(self.select_list))
            for iter388 in self.select_list:
                iter388.write(oprot)
            oprot.writeListEnd()
            oprot.writeFieldEnd()
        if self.highlight_list is not None:
            oprot.writeFieldBegin('highlight_list', TType.LIST, 5)
            oprot.writeListBegin(TType.STRUCT, len(self.highlight_list))
            for iter389 in self.highlight_list:
                iter389.write(oprot)
            oprot.writeListEnd()
            oprot.writeFieldEnd()
        if self.search_expr is not None:
            oprot.writeFieldBegin('search_expr', TType.STRUCT, 6)
            self.search_expr.write(oprot)
            oprot.writeFieldEnd()
        if self.where_expr is not None:
            oprot.writeFieldBegin('where_expr', TType.STRUCT, 7)
            self.where_expr.write(oprot)
            oprot.writeFieldEnd()
        if self.group_by_list is not None:
            oprot.writeFieldBegin('group_by_list', TType.LIST, 8)
            oprot.writeListBegin(TType.STRUCT, len(self.group_by_list))
            for iter390 in self.group_by_list:
                iter390.write(oprot)
            oprot.writeListEnd()
            oprot.writeFieldEnd()
        if self.having_expr is not None:
            oprot.writeFieldBegin('having_expr', TType.STRUCT, 9)
            self.having_expr.write(oprot)
            oprot.writeFieldEnd()
        if self.limit_expr is not None:
            oprot.writeFieldBegin('limit_expr', TType.STRUCT, 10)
            self.limit_expr.write(oprot)
            oprot.writeFieldEnd()
        if self.offset_expr is not None:
            oprot.writeFieldBegin('offset_expr', TType.STRUCT, 11)
            self.offset_expr.write(oprot)
            oprot.writeFieldEnd()
        if self.order_by_list is not None:
            oprot.writeFieldBegin('order_by_list', TType.LIST, 12)
            oprot.writeListBegin(TType.STRUCT, len(self.order_by_list))
            for iter391 in self.order_by_list:
                iter391.write(oprot)
            oprot.writeListEnd()
            oprot.writeFieldEnd()
        if self.total_hits_count is not None:
            oprot.writeFieldBegin('total_hits_count', TType.BOOL, 13)
            oprot.writeBool(self.total_hits_count)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class SelectResponse(object):
    """
    Attributes:
     - error_code
     - error_msg
     - column_defs
     - column_fields
     - extra_result

    """
    thrift_spec = None


    def __init__(self, error_code = None, error_msg = None, column_defs = [
    ], column_fields = [
    ], extra_result = None,):
        self.error_code = error_code
        self.error_msg = error_msg
        if column_defs is self.thrift_spec[3][4]:
            column_defs = [
            ]
        self.column_defs = column_defs
        if column_fields is self.thrift_spec[4][4]:
            column_fields = [
            ]
        self.column_fields = column_fields
        self.extra_result = extra_result

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.I64:
                    self.error_code = iprot.readI64()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRING:
                    self.error_msg = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.LIST:
                    self.column_defs = []
                    (_etype395, _size392) = iprot.readListBegin()
                    for _i396 in range(_size392):
                        _elem397 = ColumnDef()
                        _elem397.read(iprot)
                        self.column_defs.append(_elem397)
                    iprot.readListEnd()
                else:
                    iprot.skip(ftype)
            elif fid == 4:
                if ftype == TType.LIST:
                    self.column_fields = []
                    (_etype401, _size398) = iprot.readListBegin()
                    for _i402 in range(_size398):
                        _elem403 = ColumnField()
                        _elem403.read(iprot)
                        self.column_fields.append(_elem403)
                    iprot.readListEnd()
                else:
                    iprot.skip(ftype)
            elif fid == 5:
                if ftype == TType.STRING:
                    self.extra_result = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('SelectResponse')
        if self.error_code is not None:
            oprot.writeFieldBegin('error_code', TType.I64, 1)
            oprot.writeI64(self.error_code)
            oprot.writeFieldEnd()
        if self.error_msg is not None:
            oprot.writeFieldBegin('error_msg', TType.STRING, 2)
            oprot.writeString(self.error_msg.encode('utf-8') if sys.version_info[0] == 2 else self.error_msg)
            oprot.writeFieldEnd()
        if self.column_defs is not None:
            oprot.writeFieldBegin('column_defs', TType.LIST, 3)
            oprot.writeListBegin(TType.STRUCT, len(self.column_defs))
            for iter404 in self.column_defs:
                iter404.write(oprot)
            oprot.writeListEnd()
            oprot.writeFieldEnd()
        if self.column_fields is not None:
            oprot.writeFieldBegin('column_fields', TType.LIST, 4)
            oprot.writeListBegin(TType.STRUCT, len(self.column_fields))
            for iter405 in self.column_fields:
                iter405.write(oprot)
            oprot.writeListEnd()
            oprot.writeFieldEnd()
        if self.extra_result is not None:
            oprot.writeFieldBegin('extra_result', TType.STRING, 5)
            oprot.writeString(self.extra_result.encode('utf-8') if sys.version_info[0] == 2 else self.extra_result)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class DeleteRequest(object):
    """
    Attributes:
     - db_name
     - table_name
     - where_expr
     - session_id

    """
    thrift_spec = None


    def __init__(self, db_name = None, table_name = None, where_expr = None, session_id = None,):
        self.db_name = db_name
        self.table_name = table_name
        self.where_expr = where_expr
        self.session_id = session_id

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.STRING:
                    self.db_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRING:
                    self.table_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.STRUCT:
                    self.where_expr = ParsedExpr()
                    self.where_expr.read(iprot)
                else:
                    iprot.skip(ftype)
            elif fid == 4:
                if ftype == TType.I64:
                    self.session_id = iprot.readI64()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('DeleteRequest')
        if self.db_name is not None:
            oprot.writeFieldBegin('db_name', TType.STRING, 1)
            oprot.writeString(self.db_name.encode('utf-8') if sys.version_info[0] == 2 else self.db_name)
            oprot.writeFieldEnd()
        if self.table_name is not None:
            oprot.writeFieldBegin('table_name', TType.STRING, 2)
            oprot.writeString(self.table_name.encode('utf-8') if sys.version_info[0] == 2 else self.table_name)
            oprot.writeFieldEnd()
        if self.where_expr is not None:
            oprot.writeFieldBegin('where_expr', TType.STRUCT, 3)
            self.where_expr.write(oprot)
            oprot.writeFieldEnd()
        if self.session_id is not None:
            oprot.writeFieldBegin('session_id', TType.I64, 4)
            oprot.writeI64(self.session_id)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class DeleteResponse(object):
    """
    Attributes:
     - error_code
     - error_msg
     - deleted_rows

    """
    thrift_spec = None


    def __init__(self, error_code = None, error_msg = None, deleted_rows = None,):
        self.error_code = error_code
        self.error_msg = error_msg
        self.deleted_rows = deleted_rows

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.I64:
                    self.error_code = iprot.readI64()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRING:
                    self.error_msg = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.I64:
                    self.deleted_rows = iprot.readI64()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('DeleteResponse')
        if self.error_code is not None:
            oprot.writeFieldBegin('error_code', TType.I64, 1)
            oprot.writeI64(self.error_code)
            oprot.writeFieldEnd()
        if self.error_msg is not None:
            oprot.writeFieldBegin('error_msg', TType.STRING, 2)
            oprot.writeString(self.error_msg.encode('utf-8') if sys.version_info[0] == 2 else self.error_msg)
            oprot.writeFieldEnd()
        if self.deleted_rows is not None:
            oprot.writeFieldBegin('deleted_rows', TType.I64, 3)
            oprot.writeI64(self.deleted_rows)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class UpdateRequest(object):
    """
    Attributes:
     - db_name
     - table_name
     - where_expr
     - update_expr_array
     - session_id

    """
    thrift_spec = None


    def __init__(self, db_name = None, table_name = None, where_expr = None, update_expr_array = [
    ], session_id = None,):
        self.db_name = db_name
        self.table_name = table_name
        self.where_expr = where_expr
        if update_expr_array is self.thrift_spec[4][4]:
            update_expr_array = [
            ]
        self.update_expr_array = update_expr_array
        self.session_id = session_id

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.STRING:
                    self.db_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRING:
                    self.table_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.STRUCT:
                    self.where_expr = ParsedExpr()
                    self.where_expr.read(iprot)
                else:
                    iprot.skip(ftype)
            elif fid == 4:
                if ftype == TType.LIST:
                    self.update_expr_array = []
                    (_etype409, _size406) = iprot.readListBegin()
                    for _i410 in range(_size406):
                        _elem411 = UpdateExpr()
                        _elem411.read(iprot)
                        self.update_expr_array.append(_elem411)
                    iprot.readListEnd()
                else:
                    iprot.skip(ftype)
            elif fid == 5:
                if ftype == TType.I64:
                    self.session_id = iprot.readI64()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('UpdateRequest')
        if self.db_name is not None:
            oprot.writeFieldBegin('db_name', TType.STRING, 1)
            oprot.writeString(self.db_name.encode('utf-8') if sys.version_info[0] == 2 else self.db_name)
            oprot.writeFieldEnd()
        if self.table_name is not None:
            oprot.writeFieldBegin('table_name', TType.STRING, 2)
            oprot.writeString(self.table_name.encode('utf-8') if sys.version_info[0] == 2 else self.table_name)
            oprot.writeFieldEnd()
        if self.where_expr is not None:
            oprot.writeFieldBegin('where_expr', TType.STRUCT, 3)
            self.where_expr.write(oprot)
            oprot.writeFieldEnd()
        if self.update_expr_array is not None:
            oprot.writeFieldBegin('update_expr_array', TType.LIST, 4)
            oprot.writeListBegin(TType.STRUCT, len(self.update_expr_array))
            for iter412 in self.update_expr_array:
                iter412.write(oprot)
            oprot.writeListEnd()
            oprot.writeFieldEnd()
        if self.session_id is not None:
            oprot.writeFieldBegin('session_id', TType.I64, 5)
            oprot.writeI64(self.session_id)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class AddColumnsRequest(object):
    """
    Attributes:
     - db_name
     - table_name
     - column_defs
     - session_id

    """
    thrift_spec = None


    def __init__(self, db_name = None, table_name = None, column_defs = [
    ], session_id = None,):
        self.db_name = db_name
        self.table_name = table_name
        if column_defs is self.thrift_spec[3][4]:
            column_defs = [
            ]
        self.column_defs = column_defs
        self.session_id = session_id

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.STRING:
                    self.db_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRING:
                    self.table_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.LIST:
                    self.column_defs = []
                    (_etype416, _size413) = iprot.readListBegin()
                    for _i417 in range(_size413):
                        _elem418 = ColumnDef()
                        _elem418.read(iprot)
                        self.column_defs.append(_elem418)
                    iprot.readListEnd()
                else:
                    iprot.skip(ftype)
            elif fid == 4:
                if ftype == TType.I64:
                    self.session_id = iprot.readI64()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('AddColumnsRequest')
        if self.db_name is not None:
            oprot.writeFieldBegin('db_name', TType.STRING, 1)
            oprot.writeString(self.db_name.encode('utf-8') if sys.version_info[0] == 2 else self.db_name)
            oprot.writeFieldEnd()
        if self.table_name is not None:
            oprot.writeFieldBegin('table_name', TType.STRING, 2)
            oprot.writeString(self.table_name.encode('utf-8') if sys.version_info[0] == 2 else self.table_name)
            oprot.writeFieldEnd()
        if self.column_defs is not None:
            oprot.writeFieldBegin('column_defs', TType.LIST, 3)
            oprot.writeListBegin(TType.STRUCT, len(self.column_defs))
            for iter419 in self.column_defs:
                iter419.write(oprot)
            oprot.writeListEnd()
            oprot.writeFieldEnd()
        if self.session_id is not None:
            oprot.writeFieldBegin('session_id', TType.I64, 4)
            oprot.writeI64(self.session_id)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class DropColumnsRequest(object):
    """
    Attributes:
     - db_name
     - table_name
     - column_names
     - session_id

    """
    thrift_spec = None


    def __init__(self, db_name = None, table_name = None, column_names = [
    ], session_id = None,):
        self.db_name = db_name
        self.table_name = table_name
        if column_names is self.thrift_spec[3][4]:
            column_names = [
            ]
        self.column_names = column_names
        self.session_id = session_id

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.STRING:
                    self.db_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRING:
                    self.table_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.LIST:
                    self.column_names = []
                    (_etype423, _size420) = iprot.readListBegin()
                    for _i424 in range(_size420):
                        _elem425 = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                        self.column_names.append(_elem425)
                    iprot.readListEnd()
                else:
                    iprot.skip(ftype)
            elif fid == 4:
                if ftype == TType.I64:
                    self.session_id = iprot.readI64()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('DropColumnsRequest')
        if self.db_name is not None:
            oprot.writeFieldBegin('db_name', TType.STRING, 1)
            oprot.writeString(self.db_name.encode('utf-8') if sys.version_info[0] == 2 else self.db_name)
            oprot.writeFieldEnd()
        if self.table_name is not None:
            oprot.writeFieldBegin('table_name', TType.STRING, 2)
            oprot.writeString(self.table_name.encode('utf-8') if sys.version_info[0] == 2 else self.table_name)
            oprot.writeFieldEnd()
        if self.column_names is not None:
            oprot.writeFieldBegin('column_names', TType.LIST, 3)
            oprot.writeListBegin(TType.STRING, len(self.column_names))
            for iter426 in self.column_names:
                oprot.writeString(iter426.encode('utf-8') if sys.version_info[0] == 2 else iter426)
            oprot.writeListEnd()
            oprot.writeFieldEnd()
        if self.session_id is not None:
            oprot.writeFieldBegin('session_id', TType.I64, 4)
            oprot.writeI64(self.session_id)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class DumpIndexRequest(object):
    """
    Attributes:
     - db_name
     - table_name
     - index_name
     - session_id

    """
    thrift_spec = None


    def __init__(self, db_name = None, table_name = None, index_name = None, session_id = None,):
        self.db_name = db_name
        self.table_name = table_name
        self.index_name = index_name
        self.session_id = session_id

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.STRING:
                    self.db_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRING:
                    self.table_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.STRING:
                    self.index_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 4:
                if ftype == TType.I64:
                    self.session_id = iprot.readI64()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('DumpIndexRequest')
        if self.db_name is not None:
            oprot.writeFieldBegin('db_name', TType.STRING, 1)
            oprot.writeString(self.db_name.encode('utf-8') if sys.version_info[0] == 2 else self.db_name)
            oprot.writeFieldEnd()
        if self.table_name is not None:
            oprot.writeFieldBegin('table_name', TType.STRING, 2)
            oprot.writeString(self.table_name.encode('utf-8') if sys.version_info[0] == 2 else self.table_name)
            oprot.writeFieldEnd()
        if self.index_name is not None:
            oprot.writeFieldBegin('index_name', TType.STRING, 3)
            oprot.writeString(self.index_name.encode('utf-8') if sys.version_info[0] == 2 else self.index_name)
            oprot.writeFieldEnd()
        if self.session_id is not None:
            oprot.writeFieldBegin('session_id', TType.I64, 4)
            oprot.writeI64(self.session_id)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class ShowTablesRequest(object):
    """
    Attributes:
     - session_id
     - db_name

    """
    thrift_spec = None


    def __init__(self, session_id = None, db_name = None,):
        self.session_id = session_id
        self.db_name = db_name

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.I64:
                    self.session_id = iprot.readI64()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRING:
                    self.db_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('ShowTablesRequest')
        if self.session_id is not None:
            oprot.writeFieldBegin('session_id', TType.I64, 1)
            oprot.writeI64(self.session_id)
            oprot.writeFieldEnd()
        if self.db_name is not None:
            oprot.writeFieldBegin('db_name', TType.STRING, 2)
            oprot.writeString(self.db_name.encode('utf-8') if sys.version_info[0] == 2 else self.db_name)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class ShowSegmentsRequest(object):
    """
    Attributes:
     - session_id
     - db_name
     - table_name

    """
    thrift_spec = None


    def __init__(self, session_id = None, db_name = None, table_name = None,):
        self.session_id = session_id
        self.db_name = db_name
        self.table_name = table_name

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.I64:
                    self.session_id = iprot.readI64()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRING:
                    self.db_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.STRING:
                    self.table_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('ShowSegmentsRequest')
        if self.session_id is not None:
            oprot.writeFieldBegin('session_id', TType.I64, 1)
            oprot.writeI64(self.session_id)
            oprot.writeFieldEnd()
        if self.db_name is not None:
            oprot.writeFieldBegin('db_name', TType.STRING, 2)
            oprot.writeString(self.db_name.encode('utf-8') if sys.version_info[0] == 2 else self.db_name)
            oprot.writeFieldEnd()
        if self.table_name is not None:
            oprot.writeFieldBegin('table_name', TType.STRING, 3)
            oprot.writeString(self.table_name.encode('utf-8') if sys.version_info[0] == 2 else self.table_name)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class ShowSegmentRequest(object):
    """
    Attributes:
     - session_id
     - db_name
     - table_name
     - segment_id

    """
    thrift_spec = None


    def __init__(self, session_id = None, db_name = None, table_name = None, segment_id = None,):
        self.session_id = session_id
        self.db_name = db_name
        self.table_name = table_name
        self.segment_id = segment_id

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.I64:
                    self.session_id = iprot.readI64()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRING:
                    self.db_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.STRING:
                    self.table_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 4:
                if ftype == TType.I64:
                    self.segment_id = iprot.readI64()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('ShowSegmentRequest')
        if self.session_id is not None:
            oprot.writeFieldBegin('session_id', TType.I64, 1)
            oprot.writeI64(self.session_id)
            oprot.writeFieldEnd()
        if self.db_name is not None:
            oprot.writeFieldBegin('db_name', TType.STRING, 2)
            oprot.writeString(self.db_name.encode('utf-8') if sys.version_info[0] == 2 else self.db_name)
            oprot.writeFieldEnd()
        if self.table_name is not None:
            oprot.writeFieldBegin('table_name', TType.STRING, 3)
            oprot.writeString(self.table_name.encode('utf-8') if sys.version_info[0] == 2 else self.table_name)
            oprot.writeFieldEnd()
        if self.segment_id is not None:
            oprot.writeFieldBegin('segment_id', TType.I64, 4)
            oprot.writeI64(self.segment_id)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class ShowSegmentResponse(object):
    """
    Attributes:
     - error_code
     - error_msg
     - segment_id
     - status
     - path
     - size
     - block_count
     - row_capacity
     - row_count
     - room
     - column_count

    """
    thrift_spec = None


    def __init__(self, error_code = None, error_msg = None, segment_id = None, status = None, path = None, size = None, block_count = None, row_capacity = None, row_count = None, room = None, column_count = None,):
        self.error_code = error_code
        self.error_msg = error_msg
        self.segment_id = segment_id
        self.status = status
        self.path = path
        self.size = size
        self.block_count = block_count
        self.row_capacity = row_capacity
        self.row_count = row_count
        self.room = room
        self.column_count = column_count

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.I64:
                    self.error_code = iprot.readI64()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRING:
                    self.error_msg = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.I64:
                    self.segment_id = iprot.readI64()
                else:
                    iprot.skip(ftype)
            elif fid == 4:
                if ftype == TType.STRING:
                    self.status = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 5:
                if ftype == TType.STRING:
                    self.path = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 6:
                if ftype == TType.STRING:
                    self.size = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 7:
                if ftype == TType.I64:
                    self.block_count = iprot.readI64()
                else:
                    iprot.skip(ftype)
            elif fid == 8:
                if ftype == TType.I64:
                    self.row_capacity = iprot.readI64()
                else:
                    iprot.skip(ftype)
            elif fid == 9:
                if ftype == TType.I64:
                    self.row_count = iprot.readI64()
                else:
                    iprot.skip(ftype)
            elif fid == 10:
                if ftype == TType.I64:
                    self.room = iprot.readI64()
                else:
                    iprot.skip(ftype)
            elif fid == 11:
                if ftype == TType.I64:
                    self.column_count = iprot.readI64()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('ShowSegmentResponse')
        if self.error_code is not None:
            oprot.writeFieldBegin('error_code', TType.I64, 1)
            oprot.writeI64(self.error_code)
            oprot.writeFieldEnd()
        if self.error_msg is not None:
            oprot.writeFieldBegin('error_msg', TType.STRING, 2)
            oprot.writeString(self.error_msg.encode('utf-8') if sys.version_info[0] == 2 else self.error_msg)
            oprot.writeFieldEnd()
        if self.segment_id is not None:
            oprot.writeFieldBegin('segment_id', TType.I64, 3)
            oprot.writeI64(self.segment_id)
            oprot.writeFieldEnd()
        if self.status is not None:
            oprot.writeFieldBegin('status', TType.STRING, 4)
            oprot.writeString(self.status.encode('utf-8') if sys.version_info[0] == 2 else self.status)
            oprot.writeFieldEnd()
        if self.path is not None:
            oprot.writeFieldBegin('path', TType.STRING, 5)
            oprot.writeString(self.path.encode('utf-8') if sys.version_info[0] == 2 else self.path)
            oprot.writeFieldEnd()
        if self.size is not None:
            oprot.writeFieldBegin('size', TType.STRING, 6)
            oprot.writeString(self.size.encode('utf-8') if sys.version_info[0] == 2 else self.size)
            oprot.writeFieldEnd()
        if self.block_count is not None:
            oprot.writeFieldBegin('block_count', TType.I64, 7)
            oprot.writeI64(self.block_count)
            oprot.writeFieldEnd()
        if self.row_capacity is not None:
            oprot.writeFieldBegin('row_capacity', TType.I64, 8)
            oprot.writeI64(self.row_capacity)
            oprot.writeFieldEnd()
        if self.row_count is not None:
            oprot.writeFieldBegin('row_count', TType.I64, 9)
            oprot.writeI64(self.row_count)
            oprot.writeFieldEnd()
        if self.room is not None:
            oprot.writeFieldBegin('room', TType.I64, 10)
            oprot.writeI64(self.room)
            oprot.writeFieldEnd()
        if self.column_count is not None:
            oprot.writeFieldBegin('column_count', TType.I64, 11)
            oprot.writeI64(self.column_count)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class ShowBlocksRequest(object):
    """
    Attributes:
     - session_id
     - db_name
     - table_name
     - segment_id

    """
    thrift_spec = None


    def __init__(self, session_id = None, db_name = None, table_name = None, segment_id = None,):
        self.session_id = session_id
        self.db_name = db_name
        self.table_name = table_name
        self.segment_id = segment_id

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.I64:
                    self.session_id = iprot.readI64()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRING:
                    self.db_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.STRING:
                    self.table_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 4:
                if ftype == TType.I64:
                    self.segment_id = iprot.readI64()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('ShowBlocksRequest')
        if self.session_id is not None:
            oprot.writeFieldBegin('session_id', TType.I64, 1)
            oprot.writeI64(self.session_id)
            oprot.writeFieldEnd()
        if self.db_name is not None:
            oprot.writeFieldBegin('db_name', TType.STRING, 2)
            oprot.writeString(self.db_name.encode('utf-8') if sys.version_info[0] == 2 else self.db_name)
            oprot.writeFieldEnd()
        if self.table_name is not None:
            oprot.writeFieldBegin('table_name', TType.STRING, 3)
            oprot.writeString(self.table_name.encode('utf-8') if sys.version_info[0] == 2 else self.table_name)
            oprot.writeFieldEnd()
        if self.segment_id is not None:
            oprot.writeFieldBegin('segment_id', TType.I64, 4)
            oprot.writeI64(self.segment_id)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class ShowBlockRequest(object):
    """
    Attributes:
     - session_id
     - db_name
     - table_name
     - segment_id
     - block_id

    """
    thrift_spec = None


    def __init__(self, session_id = None, db_name = None, table_name = None, segment_id = None, block_id = None,):
        self.session_id = session_id
        self.db_name = db_name
        self.table_name = table_name
        self.segment_id = segment_id
        self.block_id = block_id

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.I64:
                    self.session_id = iprot.readI64()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRING:
                    self.db_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.STRING:
                    self.table_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 4:
                if ftype == TType.I64:
                    self.segment_id = iprot.readI64()
                else:
                    iprot.skip(ftype)
            elif fid == 5:
                if ftype == TType.I64:
                    self.block_id = iprot.readI64()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('ShowBlockRequest')
        if self.session_id is not None:
            oprot.writeFieldBegin('session_id', TType.I64, 1)
            oprot.writeI64(self.session_id)
            oprot.writeFieldEnd()
        if self.db_name is not None:
            oprot.writeFieldBegin('db_name', TType.STRING, 2)
            oprot.writeString(self.db_name.encode('utf-8') if sys.version_info[0] == 2 else self.db_name)
            oprot.writeFieldEnd()
        if self.table_name is not None:
            oprot.writeFieldBegin('table_name', TType.STRING, 3)
            oprot.writeString(self.table_name.encode('utf-8') if sys.version_info[0] == 2 else self.table_name)
            oprot.writeFieldEnd()
        if self.segment_id is not None:
            oprot.writeFieldBegin('segment_id', TType.I64, 4)
            oprot.writeI64(self.segment_id)
            oprot.writeFieldEnd()
        if self.block_id is not None:
            oprot.writeFieldBegin('block_id', TType.I64, 5)
            oprot.writeI64(self.block_id)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class ShowBlockResponse(object):
    """
    Attributes:
     - error_code
     - error_msg
     - block_id
     - path
     - size
     - row_capacity
     - row_count
     - column_count

    """
    thrift_spec = None


    def __init__(self, error_code = None, error_msg = None, block_id = None, path = None, size = None, row_capacity = None, row_count = None, column_count = None,):
        self.error_code = error_code
        self.error_msg = error_msg
        self.block_id = block_id
        self.path = path
        self.size = size
        self.row_capacity = row_capacity
        self.row_count = row_count
        self.column_count = column_count

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.I64:
                    self.error_code = iprot.readI64()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRING:
                    self.error_msg = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.I64:
                    self.block_id = iprot.readI64()
                else:
                    iprot.skip(ftype)
            elif fid == 4:
                if ftype == TType.STRING:
                    self.path = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 5:
                if ftype == TType.STRING:
                    self.size = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 6:
                if ftype == TType.I64:
                    self.row_capacity = iprot.readI64()
                else:
                    iprot.skip(ftype)
            elif fid == 7:
                if ftype == TType.I64:
                    self.row_count = iprot.readI64()
                else:
                    iprot.skip(ftype)
            elif fid == 8:
                if ftype == TType.I64:
                    self.column_count = iprot.readI64()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('ShowBlockResponse')
        if self.error_code is not None:
            oprot.writeFieldBegin('error_code', TType.I64, 1)
            oprot.writeI64(self.error_code)
            oprot.writeFieldEnd()
        if self.error_msg is not None:
            oprot.writeFieldBegin('error_msg', TType.STRING, 2)
            oprot.writeString(self.error_msg.encode('utf-8') if sys.version_info[0] == 2 else self.error_msg)
            oprot.writeFieldEnd()
        if self.block_id is not None:
            oprot.writeFieldBegin('block_id', TType.I64, 3)
            oprot.writeI64(self.block_id)
            oprot.writeFieldEnd()
        if self.path is not None:
            oprot.writeFieldBegin('path', TType.STRING, 4)
            oprot.writeString(self.path.encode('utf-8') if sys.version_info[0] == 2 else self.path)
            oprot.writeFieldEnd()
        if self.size is not None:
            oprot.writeFieldBegin('size', TType.STRING, 5)
            oprot.writeString(self.size.encode('utf-8') if sys.version_info[0] == 2 else self.size)
            oprot.writeFieldEnd()
        if self.row_capacity is not None:
            oprot.writeFieldBegin('row_capacity', TType.I64, 6)
            oprot.writeI64(self.row_capacity)
            oprot.writeFieldEnd()
        if self.row_count is not None:
            oprot.writeFieldBegin('row_count', TType.I64, 7)
            oprot.writeI64(self.row_count)
            oprot.writeFieldEnd()
        if self.column_count is not None:
            oprot.writeFieldBegin('column_count', TType.I64, 8)
            oprot.writeI64(self.column_count)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class ShowBlockColumnRequest(object):
    """
    Attributes:
     - session_id
     - db_name
     - table_name
     - segment_id
     - block_id
     - column_id

    """
    thrift_spec = None


    def __init__(self, session_id = None, db_name = None, table_name = None, segment_id = None, block_id = None, column_id = None,):
        self.session_id = session_id
        self.db_name = db_name
        self.table_name = table_name
        self.segment_id = segment_id
        self.block_id = block_id
        self.column_id = column_id

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.I64:
                    self.session_id = iprot.readI64()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRING:
                    self.db_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.STRING:
                    self.table_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 4:
                if ftype == TType.I64:
                    self.segment_id = iprot.readI64()
                else:
                    iprot.skip(ftype)
            elif fid == 5:
                if ftype == TType.I64:
                    self.block_id = iprot.readI64()
                else:
                    iprot.skip(ftype)
            elif fid == 6:
                if ftype == TType.I64:
                    self.column_id = iprot.readI64()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('ShowBlockColumnRequest')
        if self.session_id is not None:
            oprot.writeFieldBegin('session_id', TType.I64, 1)
            oprot.writeI64(self.session_id)
            oprot.writeFieldEnd()
        if self.db_name is not None:
            oprot.writeFieldBegin('db_name', TType.STRING, 2)
            oprot.writeString(self.db_name.encode('utf-8') if sys.version_info[0] == 2 else self.db_name)
            oprot.writeFieldEnd()
        if self.table_name is not None:
            oprot.writeFieldBegin('table_name', TType.STRING, 3)
            oprot.writeString(self.table_name.encode('utf-8') if sys.version_info[0] == 2 else self.table_name)
            oprot.writeFieldEnd()
        if self.segment_id is not None:
            oprot.writeFieldBegin('segment_id', TType.I64, 4)
            oprot.writeI64(self.segment_id)
            oprot.writeFieldEnd()
        if self.block_id is not None:
            oprot.writeFieldBegin('block_id', TType.I64, 5)
            oprot.writeI64(self.block_id)
            oprot.writeFieldEnd()
        if self.column_id is not None:
            oprot.writeFieldBegin('column_id', TType.I64, 6)
            oprot.writeI64(self.column_id)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class ShowBlockColumnResponse(object):
    """
    Attributes:
     - error_code
     - error_msg
     - column_name
     - column_id
     - data_type
     - path
     - extra_file_count
     - extra_file_names

    """
    thrift_spec = None


    def __init__(self, error_code = None, error_msg = None, column_name = None, column_id = None, data_type = None, path = None, extra_file_count = None, extra_file_names = None,):
        self.error_code = error_code
        self.error_msg = error_msg
        self.column_name = column_name
        self.column_id = column_id
        self.data_type = data_type
        self.path = path
        self.extra_file_count = extra_file_count
        self.extra_file_names = extra_file_names

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.I64:
                    self.error_code = iprot.readI64()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRING:
                    self.error_msg = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.STRING:
                    self.column_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 4:
                if ftype == TType.I64:
                    self.column_id = iprot.readI64()
                else:
                    iprot.skip(ftype)
            elif fid == 5:
                if ftype == TType.STRING:
                    self.data_type = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 6:
                if ftype == TType.STRING:
                    self.path = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 7:
                if ftype == TType.I64:
                    self.extra_file_count = iprot.readI64()
                else:
                    iprot.skip(ftype)
            elif fid == 8:
                if ftype == TType.STRING:
                    self.extra_file_names = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('ShowBlockColumnResponse')
        if self.error_code is not None:
            oprot.writeFieldBegin('error_code', TType.I64, 1)
            oprot.writeI64(self.error_code)
            oprot.writeFieldEnd()
        if self.error_msg is not None:
            oprot.writeFieldBegin('error_msg', TType.STRING, 2)
            oprot.writeString(self.error_msg.encode('utf-8') if sys.version_info[0] == 2 else self.error_msg)
            oprot.writeFieldEnd()
        if self.column_name is not None:
            oprot.writeFieldBegin('column_name', TType.STRING, 3)
            oprot.writeString(self.column_name.encode('utf-8') if sys.version_info[0] == 2 else self.column_name)
            oprot.writeFieldEnd()
        if self.column_id is not None:
            oprot.writeFieldBegin('column_id', TType.I64, 4)
            oprot.writeI64(self.column_id)
            oprot.writeFieldEnd()
        if self.data_type is not None:
            oprot.writeFieldBegin('data_type', TType.STRING, 5)
            oprot.writeString(self.data_type.encode('utf-8') if sys.version_info[0] == 2 else self.data_type)
            oprot.writeFieldEnd()
        if self.path is not None:
            oprot.writeFieldBegin('path', TType.STRING, 6)
            oprot.writeString(self.path.encode('utf-8') if sys.version_info[0] == 2 else self.path)
            oprot.writeFieldEnd()
        if self.extra_file_count is not None:
            oprot.writeFieldBegin('extra_file_count', TType.I64, 7)
            oprot.writeI64(self.extra_file_count)
            oprot.writeFieldEnd()
        if self.extra_file_names is not None:
            oprot.writeFieldBegin('extra_file_names', TType.STRING, 8)
            oprot.writeString(self.extra_file_names.encode('utf-8') if sys.version_info[0] == 2 else self.extra_file_names)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class ShowCurrentNodeRequest(object):
    """
    Attributes:
     - session_id

    """
    thrift_spec = None


    def __init__(self, session_id = None,):
        self.session_id = session_id

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.I64:
                    self.session_id = iprot.readI64()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('ShowCurrentNodeRequest')
        if self.session_id is not None:
            oprot.writeFieldBegin('session_id', TType.I64, 1)
            oprot.writeI64(self.session_id)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class ShowCurrentNodeResponse(object):
    """
    Attributes:
     - error_code
     - error_msg
     - node_role
     - server_status

    """
    thrift_spec = None


    def __init__(self, error_code = None, error_msg = None, node_role = None, server_status = None,):
        self.error_code = error_code
        self.error_msg = error_msg
        self.node_role = node_role
        self.server_status = server_status

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.I64:
                    self.error_code = iprot.readI64()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRING:
                    self.error_msg = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.STRING:
                    self.node_role = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 4:
                if ftype == TType.STRING:
                    self.server_status = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('ShowCurrentNodeResponse')
        if self.error_code is not None:
            oprot.writeFieldBegin('error_code', TType.I64, 1)
            oprot.writeI64(self.error_code)
            oprot.writeFieldEnd()
        if self.error_msg is not None:
            oprot.writeFieldBegin('error_msg', TType.STRING, 2)
            oprot.writeString(self.error_msg.encode('utf-8') if sys.version_info[0] == 2 else self.error_msg)
            oprot.writeFieldEnd()
        if self.node_role is not None:
            oprot.writeFieldBegin('node_role', TType.STRING, 3)
            oprot.writeString(self.node_role.encode('utf-8') if sys.version_info[0] == 2 else self.node_role)
            oprot.writeFieldEnd()
        if self.server_status is not None:
            oprot.writeFieldBegin('server_status', TType.STRING, 4)
            oprot.writeString(self.server_status.encode('utf-8') if sys.version_info[0] == 2 else self.server_status)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class CommandRequest(object):
    """
    Attributes:
     - session_id
     - command_type
     - test_command_content

    """
    thrift_spec = None


    def __init__(self, session_id = None, command_type = None, test_command_content = None,):
        self.session_id = session_id
        self.command_type = command_type
        self.test_command_content = test_command_content

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.I64:
                    self.session_id = iprot.readI64()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRING:
                    self.command_type = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.STRING:
                    self.test_command_content = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('CommandRequest')
        if self.session_id is not None:
            oprot.writeFieldBegin('session_id', TType.I64, 1)
            oprot.writeI64(self.session_id)
            oprot.writeFieldEnd()
        if self.command_type is not None:
            oprot.writeFieldBegin('command_type', TType.STRING, 2)
            oprot.writeString(self.command_type.encode('utf-8') if sys.version_info[0] == 2 else self.command_type)
            oprot.writeFieldEnd()
        if self.test_command_content is not None:
            oprot.writeFieldBegin('test_command_content', TType.STRING, 3)
            oprot.writeString(self.test_command_content.encode('utf-8') if sys.version_info[0] == 2 else self.test_command_content)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class FlushRequest(object):
    """
    Attributes:
     - session_id
     - flush_type

    """
    thrift_spec = None


    def __init__(self, session_id = None, flush_type = None,):
        self.session_id = session_id
        self.flush_type = flush_type

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.I64:
                    self.session_id = iprot.readI64()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRING:
                    self.flush_type = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('FlushRequest')
        if self.session_id is not None:
            oprot.writeFieldBegin('session_id', TType.I64, 1)
            oprot.writeI64(self.session_id)
            oprot.writeFieldEnd()
        if self.flush_type is not None:
            oprot.writeFieldBegin('flush_type', TType.STRING, 2)
            oprot.writeString(self.flush_type.encode('utf-8') if sys.version_info[0] == 2 else self.flush_type)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class CompactRequest(object):
    """
    Attributes:
     - session_id
     - db_name
     - table_name

    """
    thrift_spec = None


    def __init__(self, session_id = None, db_name = None, table_name = None,):
        self.session_id = session_id
        self.db_name = db_name
        self.table_name = table_name

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.I64:
                    self.session_id = iprot.readI64()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRING:
                    self.db_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.STRING:
                    self.table_name = iprot.readString().decode('utf-8', errors='replace') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        self.validate()
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('CompactRequest')
        if self.session_id is not None:
            oprot.writeFieldBegin('session_id', TType.I64, 1)
            oprot.writeI64(self.session_id)
            oprot.writeFieldEnd()
        if self.db_name is not None:
            oprot.writeFieldBegin('db_name', TType.STRING, 2)
            oprot.writeString(self.db_name.encode('utf-8') if sys.version_info[0] == 2 else self.db_name)
            oprot.writeFieldEnd()
        if self.table_name is not None:
            oprot.writeFieldBegin('table_name', TType.STRING, 3)
            oprot.writeString(self.table_name.encode('utf-8') if sys.version_info[0] == 2 else self.table_name)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)
all_structs.append(Property)
Property.thrift_spec = (
    None,  # 0
    (1, TType.STRING, 'key', 'UTF8', None, ),  # 1
    (2, TType.STRING, 'value', 'UTF8', None, ),  # 2
)
all_structs.append(CreateOption)
CreateOption.thrift_spec = (
    None,  # 0
    (1, TType.I32, 'conflict_type', None, None, ),  # 1
    (2, TType.LIST, 'properties', (TType.STRUCT, [Property, None], False), [
    ], ),  # 2
)
all_structs.append(DropOption)
DropOption.thrift_spec = (
    None,  # 0
    (1, TType.I32, 'conflict_type', None, None, ),  # 1
)
all_structs.append(NumberType)
NumberType.thrift_spec = (
)
all_structs.append(VarcharType)
VarcharType.thrift_spec = (
)
all_structs.append(EmbeddingType)
EmbeddingType.thrift_spec = (
    None,  # 0
    (1, TType.I32, 'dimension', None, None, ),  # 1
    (2, TType.I32, 'element_type', None, None, ),  # 2
)
all_structs.append(SparseType)
SparseType.thrift_spec = (
    None,  # 0
    (1, TType.I64, 'dimension', None, None, ),  # 1
    (2, TType.I32, 'element_type', None, None, ),  # 2
    (3, TType.I32, 'index_type', None, None, ),  # 3
)
all_structs.append(ArrayType)
ArrayType.thrift_spec = (
    None,  # 0
    (1, TType.STRUCT, 'element_data_type', [DataType, None], None, ),  # 1
)
all_structs.append(PhysicalType)
PhysicalType.thrift_spec = (
    None,  # 0
    (1, TType.STRUCT, 'number_type', [NumberType, None], None, ),  # 1
    (2, TType.STRUCT, 'varchar_type', [VarcharType, None], None, ),  # 2
    (3, TType.STRUCT, 'embedding_type', [EmbeddingType, None], None, ),  # 3
    (4, TType.STRUCT, 'sparse_type', [SparseType, None], None, ),  # 4
    (5, TType.STRUCT, 'array_type', [ArrayType, None], None, ),  # 5
)
all_structs.append(DataType)
DataType.thrift_spec = (
    None,  # 0
    (1, TType.I32, 'logic_type', None, None, ),  # 1
    (2, TType.STRUCT, 'physical_type', [PhysicalType, None], None, ),  # 2
)
all_structs.append(ParsedExprType)
ParsedExprType.thrift_spec = (
    None,  # 0
    (1, TType.STRUCT, 'constant_expr', [ConstantExpr, None], None, ),  # 1
    (2, TType.STRUCT, 'column_expr', [ColumnExpr, None], None, ),  # 2
    (3, TType.STRUCT, 'function_expr', [FunctionExpr, None], None, ),  # 3
    (4, TType.STRUCT, 'between_expr', [BetweenExpr, None], None, ),  # 4
    (5, TType.STRUCT, 'knn_expr', [KnnExpr, None], None, ),  # 5
    (6, TType.STRUCT, 'match_sparse_expr', [MatchSparseExpr, None], None, ),  # 6
    (7, TType.STRUCT, 'match_tensor_expr', [MatchTensorExpr, None], None, ),  # 7
    (8, TType.STRUCT, 'match_expr', [MatchExpr, None], None, ),  # 8
    (9, TType.STRUCT, 'fusion_expr', [FusionExpr, None], None, ),  # 9
    (10, TType.STRUCT, 'search_expr', [SearchExpr, None], None, ),  # 10
    (11, TType.STRUCT, 'in_expr', [InExpr, None], None, ),  # 11
)
all_structs.append(ParsedExpr)
ParsedExpr.thrift_spec = (
    None,  # 0
    (1, TType.STRUCT, 'type', [ParsedExprType, None], None, ),  # 1
    (2, TType.STRING, 'alias_name', 'UTF8', None, ),  # 2
)
all_structs.append(ColumnExpr)
ColumnExpr.thrift_spec = (
    None,  # 0
    (1, TType.LIST, 'column_name', (TType.STRING, 'UTF8', False), [
    ], ),  # 1
    (2, TType.BOOL, 'star', None, None, ),  # 2
)
all_structs.append(EmbeddingData)
EmbeddingData.thrift_spec = (
    None,  # 0
    (1, TType.LIST, 'bool_array_value', (TType.BOOL, None, False), None, ),  # 1
    (2, TType.LIST, 'u8_array_value', (TType.I16, None, False), None, ),  # 2
    (3, TType.LIST, 'i8_array_value', (TType.I16, None, False), None, ),  # 3
    (4, TType.LIST, 'i16_array_value', (TType.I16, None, False), None, ),  # 4
    (5, TType.LIST, 'i32_array_value', (TType.I32, None, False), None, ),  # 5
    (6, TType.LIST, 'i64_array_value', (TType.I64, None, False), None, ),  # 6
    (7, TType.LIST, 'f32_array_value', (TType.DOUBLE, None, False), None, ),  # 7
    (8, TType.LIST, 'f64_array_value', (TType.DOUBLE, None, False), None, ),  # 8
    (9, TType.LIST, 'f16_array_value', (TType.DOUBLE, None, False), None, ),  # 9
    (10, TType.LIST, 'bf16_array_value', (TType.DOUBLE, None, False), None, ),  # 10
)
all_structs.append(InitParameter)
InitParameter.thrift_spec = (
    None,  # 0
    (1, TType.STRING, 'param_name', 'UTF8', None, ),  # 1
    (2, TType.STRING, 'param_value', 'UTF8', None, ),  # 2
)
all_structs.append(ConstantExpr)
ConstantExpr.thrift_spec = (
    None,  # 0
    (1, TType.I32, 'literal_type', None, None, ),  # 1
    (2, TType.BOOL, 'bool_value', None, None, ),  # 2
    (3, TType.I64, 'i64_value', None, None, ),  # 3
    (4, TType.DOUBLE, 'f64_value', None, None, ),  # 4
    (5, TType.STRING, 'str_value', 'UTF8', None, ),  # 5
    (6, TType.LIST, 'i64_array_value', (TType.I64, None, False), None, ),  # 6
    (7, TType.LIST, 'f64_array_value', (TType.DOUBLE, None, False), None, ),  # 7
    (8, TType.LIST, 'i64_tensor_value', (TType.LIST, (TType.I64, None, False), False), None, ),  # 8
    (9, TType.LIST, 'f64_tensor_value', (TType.LIST, (TType.DOUBLE, None, False), False), None, ),  # 9
    (10, TType.LIST, 'i64_tensor_array_value', (TType.LIST, (TType.LIST, (TType.I64, None, False), False), False), None, ),  # 10
    (11, TType.LIST, 'f64_tensor_array_value', (TType.LIST, (TType.LIST, (TType.DOUBLE, None, False), False), False), None, ),  # 11
    (12, TType.LIST, 'i64_array_idx', (TType.I64, None, False), None, ),  # 12
    (13, TType.LIST, 'curly_brackets_array', (TType.STRUCT, [ConstantExpr, None], False), None, ),  # 13
)
all_structs.append(KnnExpr)
KnnExpr.thrift_spec = (
    None,  # 0
    (1, TType.STRUCT, 'column_expr', [ColumnExpr, None], None, ),  # 1
    (2, TType.STRUCT, 'embedding_data', [EmbeddingData, None], None, ),  # 2
    (3, TType.I32, 'embedding_data_type', None, None, ),  # 3
    (4, TType.I32, 'distance_type', None, None, ),  # 4
    (5, TType.I64, 'topn', None, None, ),  # 5
    (6, TType.LIST, 'opt_params', (TType.STRUCT, [InitParameter, None], False), [
    ], ),  # 6
    (7, TType.STRUCT, 'filter_expr', [ParsedExpr, None], None, ),  # 7
)
all_structs.append(MatchSparseExpr)
MatchSparseExpr.thrift_spec = (
    None,  # 0
    (1, TType.STRUCT, 'column_expr', [ColumnExpr, None], None, ),  # 1
    (2, TType.STRUCT, 'query_sparse_expr', [ConstantExpr, None], None, ),  # 2
    (3, TType.STRING, 'metric_type', 'UTF8', None, ),  # 3
    (4, TType.I64, 'topn', None, None, ),  # 4
    (5, TType.LIST, 'opt_params', (TType.STRUCT, [InitParameter, None], False), [
    ], ),  # 5
    (6, TType.STRUCT, 'filter_expr', [ParsedExpr, None], None, ),  # 6
)
all_structs.append(MatchTensorExpr)
MatchTensorExpr.thrift_spec = (
    None,  # 0
    (1, TType.STRING, 'search_method', 'UTF8', None, ),  # 1
    (2, TType.STRUCT, 'column_expr', [ColumnExpr, None], None, ),  # 2
    (3, TType.I32, 'embedding_data_type', None, None, ),  # 3
    (4, TType.STRUCT, 'embedding_data', [EmbeddingData, None], None, ),  # 4
    (5, TType.STRING, 'extra_options', 'UTF8', None, ),  # 5
    (6, TType.STRUCT, 'filter_expr', [ParsedExpr, None], None, ),  # 6
)
all_structs.append(MatchExpr)
MatchExpr.thrift_spec = (
    None,  # 0
    (1, TType.STRING, 'fields', 'UTF8', None, ),  # 1
    (2, TType.STRING, 'matching_text', 'UTF8', None, ),  # 2
    (3, TType.STRING, 'options_text', 'UTF8', None, ),  # 3
    (4, TType.STRUCT, 'filter_expr', [ParsedExpr, None], None, ),  # 4
)
all_structs.append(GenericMatchExpr)
GenericMatchExpr.thrift_spec = (
    None,  # 0
    (1, TType.STRUCT, 'match_vector_expr', [KnnExpr, None], None, ),  # 1
    (2, TType.STRUCT, 'match_sparse_expr', [MatchSparseExpr, None], None, ),  # 2
    (3, TType.STRUCT, 'match_tensor_expr', [MatchTensorExpr, None], None, ),  # 3
    (4, TType.STRUCT, 'match_text_expr', [MatchExpr, None], None, ),  # 4
)
all_structs.append(FusionExpr)
FusionExpr.thrift_spec = (
    None,  # 0
    (1, TType.STRING, 'method', 'UTF8', None, ),  # 1
    (2, TType.STRING, 'options_text', 'UTF8', None, ),  # 2
    (3, TType.STRUCT, 'optional_match_tensor_expr', [MatchTensorExpr, None], None, ),  # 3
)
all_structs.append(SearchExpr)
SearchExpr.thrift_spec = (
    None,  # 0
    (1, TType.LIST, 'match_exprs', (TType.STRUCT, [GenericMatchExpr, None], False), None, ),  # 1
    (2, TType.LIST, 'fusion_exprs', (TType.STRUCT, [FusionExpr, None], False), None, ),  # 2
)
all_structs.append(FunctionExpr)
FunctionExpr.thrift_spec = (
    None,  # 0
    (1, TType.STRING, 'function_name', 'UTF8', None, ),  # 1
    (2, TType.LIST, 'arguments', (TType.STRUCT, [ParsedExpr, None], False), None, ),  # 2
)
all_structs.append(BetweenExpr)
BetweenExpr.thrift_spec = (
    None,  # 0
    (1, TType.STRUCT, 'value', [ParsedExpr, None], None, ),  # 1
    (2, TType.STRUCT, 'upper_bound', [ParsedExpr, None], None, ),  # 2
    (3, TType.STRUCT, 'lower_bound', [ParsedExpr, None], None, ),  # 3
)
all_structs.append(UpdateExpr)
UpdateExpr.thrift_spec = (
    None,  # 0
    (1, TType.STRING, 'column_name', 'UTF8', None, ),  # 1
    (2, TType.STRUCT, 'value', [ParsedExpr, None], None, ),  # 2
)
all_structs.append(OrderByExpr)
OrderByExpr.thrift_spec = (
    None,  # 0
    (1, TType.STRUCT, 'expr', [ParsedExpr, None], None, ),  # 1
    (2, TType.BOOL, 'asc', None, None, ),  # 2
)
all_structs.append(InExpr)
InExpr.thrift_spec = (
    None,  # 0
    (1, TType.STRUCT, 'left_operand', [ParsedExpr, None], None, ),  # 1
    (2, TType.LIST, 'arguments', (TType.STRUCT, [ParsedExpr, None], False), None, ),  # 2
    (3, TType.BOOL, 'in_type', None, None, ),  # 3
)
all_structs.append(ColumnDef)
ColumnDef.thrift_spec = (
    None,  # 0
    (1, TType.I32, 'id', None, None, ),  # 1
    (2, TType.STRING, 'name', 'UTF8', None, ),  # 2
    (3, TType.STRUCT, 'data_type', [DataType, None], None, ),  # 3
    (4, TType.LIST, 'constraints', (TType.I32, None, False), [
    ], ),  # 4
    (5, TType.STRUCT, 'constant_expr', [ConstantExpr, None], None, ),  # 5
    (6, TType.STRING, 'comment', 'UTF8', None, ),  # 6
)
all_structs.append(Field)
Field.thrift_spec = (
    None,  # 0
    (1, TType.LIST, 'column_names', (TType.STRING, 'UTF8', False), [
    ], ),  # 1
    (2, TType.LIST, 'parse_exprs', (TType.STRUCT, [ParsedExpr, None], False), [
    ], ),  # 2
)
all_structs.append(ColumnField)
ColumnField.thrift_spec = (
    None,  # 0
    (1, TType.I32, 'column_type', None, None, ),  # 1
    (2, TType.LIST, 'column_vectors', (TType.STRING, 'BINARY', False), [
    ], ),  # 2
    (3, TType.STRING, 'column_name', 'UTF8', None, ),  # 3
)
all_structs.append(ImportOption)
ImportOption.thrift_spec = (
    None,  # 0
    (1, TType.STRING, 'delimiter', 'UTF8', None, ),  # 1
    (2, TType.BOOL, 'has_header', None, None, ),  # 2
    (3, TType.I32, 'copy_file_type', None, None, ),  # 3
)
all_structs.append(ExportOption)
ExportOption.thrift_spec = (
    None,  # 0
    (1, TType.STRING, 'delimiter', 'UTF8', None, ),  # 1
    (2, TType.BOOL, 'has_header', None, None, ),  # 2
    (3, TType.I32, 'copy_file_type', None, None, ),  # 3
    (4, TType.I64, 'offset', None, None, ),  # 4
    (5, TType.I64, 'limit', None, None, ),  # 5
    (6, TType.I64, 'row_limit', None, None, ),  # 6
)
all_structs.append(OptimizeOptions)
OptimizeOptions.thrift_spec = (
    None,  # 0
    (1, TType.STRING, 'index_name', 'UTF8', None, ),  # 1
    (2, TType.LIST, 'opt_params', (TType.STRUCT, [InitParameter, None], False), [
    ], ),  # 2
)
all_structs.append(ConnectRequest)
ConnectRequest.thrift_spec = (
    None,  # 0
    (1, TType.I64, 'client_version', None, None, ),  # 1
)
all_structs.append(CommonRequest)
CommonRequest.thrift_spec = (
    None,  # 0
    (1, TType.I64, 'session_id', None, None, ),  # 1
)
all_structs.append(CommonResponse)
CommonResponse.thrift_spec = (
    None,  # 0
    (1, TType.I64, 'error_code', None, None, ),  # 1
    (2, TType.STRING, 'error_msg', 'UTF8', None, ),  # 2
    (3, TType.I64, 'session_id', None, None, ),  # 3
)
all_structs.append(ListDatabaseRequest)
ListDatabaseRequest.thrift_spec = (
    None,  # 0
    (1, TType.I64, 'session_id', None, None, ),  # 1
)
all_structs.append(ListDatabaseResponse)
ListDatabaseResponse.thrift_spec = (
    None,  # 0
    (1, TType.I64, 'error_code', None, None, ),  # 1
    (2, TType.STRING, 'error_msg', 'UTF8', None, ),  # 2
    (3, TType.LIST, 'db_names', (TType.STRING, 'UTF8', False), [
    ], ),  # 3
    (4, TType.LIST, 'db_dirs', (TType.STRING, 'UTF8', False), [
    ], ),  # 4
    (5, TType.LIST, 'db_comments', (TType.STRING, 'UTF8', False), [
    ], ),  # 5
)
all_structs.append(ListTableRequest)
ListTableRequest.thrift_spec = (
    None,  # 0
    (1, TType.STRING, 'db_name', 'UTF8', None, ),  # 1
    (2, TType.I64, 'session_id', None, None, ),  # 2
)
all_structs.append(ListTableResponse)
ListTableResponse.thrift_spec = (
    None,  # 0
    (1, TType.I64, 'error_code', None, None, ),  # 1
    (2, TType.STRING, 'error_msg', 'UTF8', None, ),  # 2
    (3, TType.LIST, 'table_names', (TType.STRING, 'UTF8', False), [
    ], ),  # 3
)
all_structs.append(ListIndexRequest)
ListIndexRequest.thrift_spec = (
    None,  # 0
    (1, TType.STRING, 'db_name', 'UTF8', None, ),  # 1
    (2, TType.STRING, 'table_name', 'UTF8', None, ),  # 2
    (3, TType.I64, 'session_id', None, None, ),  # 3
)
all_structs.append(ListIndexResponse)
ListIndexResponse.thrift_spec = (
    None,  # 0
    (1, TType.I64, 'error_code', None, None, ),  # 1
    (2, TType.STRING, 'error_msg', 'UTF8', None, ),  # 2
    (3, TType.LIST, 'index_names', (TType.STRING, 'UTF8', False), [
    ], ),  # 3
)
all_structs.append(ShowDatabaseRequest)
ShowDatabaseRequest.thrift_spec = (
    None,  # 0
    (1, TType.STRING, 'db_name', 'UTF8', None, ),  # 1
    (2, TType.I64, 'session_id', None, None, ),  # 2
)
all_structs.append(ShowDatabaseResponse)
ShowDatabaseResponse.thrift_spec = (
    None,  # 0
    (1, TType.I64, 'error_code', None, None, ),  # 1
    (2, TType.STRING, 'error_msg', 'UTF8', None, ),  # 2
    (3, TType.STRING, 'database_name', 'UTF8', None, ),  # 3
    (4, TType.STRING, 'store_dir', 'UTF8', None, ),  # 4
    (5, TType.I64, 'table_count', None, None, ),  # 5
    (6, TType.STRING, 'comment', 'UTF8', None, ),  # 6
)
all_structs.append(ShowTableRequest)
ShowTableRequest.thrift_spec = (
    None,  # 0
    (1, TType.STRING, 'db_name', 'UTF8', None, ),  # 1
    (2, TType.STRING, 'table_name', 'UTF8', None, ),  # 2
    (3, TType.I64, 'session_id', None, None, ),  # 3
)
all_structs.append(ShowTableResponse)
ShowTableResponse.thrift_spec = (
    None,  # 0
    (1, TType.I64, 'error_code', None, None, ),  # 1
    (2, TType.STRING, 'error_msg', 'UTF8', None, ),  # 2
    (3, TType.STRING, 'database_name', 'UTF8', None, ),  # 3
    (4, TType.STRING, 'table_name', 'UTF8', None, ),  # 4
    (5, TType.STRING, 'store_dir', 'UTF8', None, ),  # 5
    (6, TType.I64, 'column_count', None, None, ),  # 6
    (7, TType.I64, 'segment_count', None, None, ),  # 7
    (8, TType.I64, 'row_count', None, None, ),  # 8
)
all_structs.append(ShowColumnsRequest)
ShowColumnsRequest.thrift_spec = (
    None,  # 0
    (1, TType.STRING, 'db_name', 'UTF8', None, ),  # 1
    (2, TType.STRING, 'table_name', 'UTF8', None, ),  # 2
    (3, TType.I64, 'session_id', None, None, ),  # 3
)
all_structs.append(GetTableRequest)
GetTableRequest.thrift_spec = (
    None,  # 0
    (1, TType.STRING, 'db_name', 'UTF8', None, ),  # 1
    (2, TType.STRING, 'table_name', 'UTF8', None, ),  # 2
    (3, TType.I64, 'session_id', None, None, ),  # 3
)
all_structs.append(IndexInfo)
IndexInfo.thrift_spec = (
    None,  # 0
    (1, TType.STRING, 'column_name', 'UTF8', None, ),  # 1
    (2, TType.I32, 'index_type', None, None, ),  # 2
    (3, TType.LIST, 'index_param_list', (TType.STRUCT, [InitParameter, None], False), [
    ], ),  # 3
)
all_structs.append(CreateIndexRequest)
CreateIndexRequest.thrift_spec = (
    None,  # 0
    (1, TType.STRING, 'db_name', 'UTF8', None, ),  # 1
    (2, TType.STRING, 'table_name', 'UTF8', None, ),  # 2
    (3, TType.STRING, 'index_name', 'UTF8', None, ),  # 3
    (4, TType.STRING, 'index_comment', 'UTF8', None, ),  # 4
    (5, TType.STRUCT, 'index_info', [IndexInfo, None], None, ),  # 5
    (6, TType.I64, 'session_id', None, None, ),  # 6
    (7, TType.STRUCT, 'create_option', [CreateOption, None], None, ),  # 7
)
all_structs.append(DropIndexRequest)
DropIndexRequest.thrift_spec = (
    None,  # 0
    (1, TType.STRING, 'db_name', 'UTF8', None, ),  # 1
    (2, TType.STRING, 'table_name', 'UTF8', None, ),  # 2
    (3, TType.STRING, 'index_name', 'UTF8', None, ),  # 3
    (4, TType.I64, 'session_id', None, None, ),  # 4
    (5, TType.STRUCT, 'drop_option', [DropOption, None], None, ),  # 5
)
all_structs.append(ShowIndexRequest)
ShowIndexRequest.thrift_spec = (
    None,  # 0
    (1, TType.STRING, 'db_name', 'UTF8', None, ),  # 1
    (2, TType.STRING, 'table_name', 'UTF8', None, ),  # 2
    (3, TType.STRING, 'index_name', 'UTF8', None, ),  # 3
    (4, TType.I64, 'session_id', None, None, ),  # 4
)
all_structs.append(ShowIndexResponse)
ShowIndexResponse.thrift_spec = (
    None,  # 0
    (1, TType.I64, 'error_code', None, None, ),  # 1
    (2, TType.STRING, 'error_msg', 'UTF8', None, ),  # 2
    (3, TType.STRING, 'db_name', 'UTF8', None, ),  # 3
    (4, TType.STRING, 'table_name', 'UTF8', None, ),  # 4
    (5, TType.STRING, 'index_name', 'UTF8', None, ),  # 5
    (6, TType.STRING, 'index_comment', 'UTF8', None, ),  # 6
    (7, TType.STRING, 'index_type', 'UTF8', None, ),  # 7
    (8, TType.STRING, 'index_column_names', 'UTF8', None, ),  # 8
    (9, TType.STRING, 'index_column_ids', 'UTF8', None, ),  # 9
    (10, TType.STRING, 'other_parameters', 'UTF8', None, ),  # 10
    (11, TType.STRING, 'store_dir', 'UTF8', None, ),  # 11
    (12, TType.STRING, 'segment_index_count', 'UTF8', None, ),  # 12
)
all_structs.append(OptimizeRequest)
OptimizeRequest.thrift_spec = (
    None,  # 0
    (1, TType.STRING, 'db_name', 'UTF8', None, ),  # 1
    (2, TType.STRING, 'table_name', 'UTF8', None, ),  # 2
    (3, TType.STRUCT, 'optimize_options', [OptimizeOptions, None], None, ),  # 3
    (4, TType.I64, 'session_id', None, None, ),  # 4
)
all_structs.append(GetDatabaseRequest)
GetDatabaseRequest.thrift_spec = (
    None,  # 0
    (1, TType.STRING, 'db_name', 'UTF8', None, ),  # 1
    (2, TType.I64, 'session_id', None, None, ),  # 2
)
all_structs.append(CreateDatabaseRequest)
CreateDatabaseRequest.thrift_spec = (
    None,  # 0
    (1, TType.STRING, 'db_name', 'UTF8', None, ),  # 1
    (2, TType.I64, 'session_id', None, None, ),  # 2
    (3, TType.STRUCT, 'create_option', [CreateOption, None], None, ),  # 3
    (4, TType.STRING, 'db_comment', 'UTF8', None, ),  # 4
)
all_structs.append(DropDatabaseRequest)
DropDatabaseRequest.thrift_spec = (
    None,  # 0
    (1, TType.STRING, 'db_name', 'UTF8', None, ),  # 1
    (2, TType.I64, 'session_id', None, None, ),  # 2
    (3, TType.STRUCT, 'drop_option', [DropOption, None], None, ),  # 3
)
all_structs.append(CreateTableRequest)
CreateTableRequest.thrift_spec = (
    None,  # 0
    (1, TType.STRING, 'db_name', 'UTF8', None, ),  # 1
    (2, TType.STRING, 'table_name', 'UTF8', None, ),  # 2
    (3, TType.LIST, 'column_defs', (TType.STRUCT, [ColumnDef, None], False), [
    ], ),  # 3
    None,  # 4
    None,  # 5
    (6, TType.I64, 'session_id', None, None, ),  # 6
    (7, TType.STRUCT, 'create_option', [CreateOption, None], None, ),  # 7
)
all_structs.append(DropTableRequest)
DropTableRequest.thrift_spec = (
    None,  # 0
    (1, TType.STRING, 'db_name', 'UTF8', None, ),  # 1
    (2, TType.STRING, 'table_name', 'UTF8', None, ),  # 2
    (3, TType.I64, 'session_id', None, None, ),  # 3
    (4, TType.STRUCT, 'drop_option', [DropOption, None], None, ),  # 4
)
all_structs.append(InsertRequest)
InsertRequest.thrift_spec = (
    None,  # 0
    (1, TType.STRING, 'db_name', 'UTF8', None, ),  # 1
    (2, TType.STRING, 'table_name', 'UTF8', None, ),  # 2
    (3, TType.LIST, 'fields', (TType.STRUCT, [Field, None], False), [
    ], ),  # 3
    (4, TType.I64, 'session_id', None, None, ),  # 4
)
all_structs.append(ImportRequest)
ImportRequest.thrift_spec = (
    None,  # 0
    (1, TType.STRING, 'db_name', 'UTF8', None, ),  # 1
    (2, TType.STRING, 'table_name', 'UTF8', None, ),  # 2
    (3, TType.STRING, 'file_name', 'UTF8', None, ),  # 3
    (4, TType.STRUCT, 'import_option', [ImportOption, None], None, ),  # 4
    (5, TType.I64, 'session_id', None, None, ),  # 5
)
all_structs.append(ExportRequest)
ExportRequest.thrift_spec = (
    None,  # 0
    (1, TType.STRING, 'db_name', 'UTF8', None, ),  # 1
    (2, TType.STRING, 'table_name', 'UTF8', None, ),  # 2
    (3, TType.LIST, 'columns', (TType.STRING, 'UTF8', False), None, ),  # 3
    (4, TType.STRING, 'file_name', 'UTF8', None, ),  # 4
    (5, TType.STRUCT, 'export_option', [ExportOption, None], None, ),  # 5
    (6, TType.I64, 'session_id', None, None, ),  # 6
)
all_structs.append(ExplainRequest)
ExplainRequest.thrift_spec = (
    None,  # 0
    (1, TType.I64, 'session_id', None, None, ),  # 1
    (2, TType.STRING, 'db_name', 'UTF8', None, ),  # 2
    (3, TType.STRING, 'table_name', 'UTF8', None, ),  # 3
    (4, TType.LIST, 'select_list', (TType.STRUCT, [ParsedExpr, None], False), [
    ], ),  # 4
    (5, TType.LIST, 'highlight_list', (TType.STRUCT, [ParsedExpr, None], False), [
    ], ),  # 5
    (6, TType.STRUCT, 'search_expr', [SearchExpr, None], None, ),  # 6
    (7, TType.STRUCT, 'where_expr', [ParsedExpr, None], None, ),  # 7
    (8, TType.LIST, 'group_by_list', (TType.STRUCT, [ParsedExpr, None], False), [
    ], ),  # 8
    (9, TType.STRUCT, 'having_expr', [ParsedExpr, None], None, ),  # 9
    (10, TType.STRUCT, 'limit_expr', [ParsedExpr, None], None, ),  # 10
    (11, TType.STRUCT, 'offset_expr', [ParsedExpr, None], None, ),  # 11
    (12, TType.LIST, 'order_by_list', (TType.STRUCT, [OrderByExpr, None], False), [
    ], ),  # 12
    (13, TType.I32, 'explain_type', None, None, ),  # 13
)
all_structs.append(ExplainResponse)
ExplainResponse.thrift_spec = (
    None,  # 0
    (1, TType.I64, 'error_code', None, None, ),  # 1
    (2, TType.STRING, 'error_msg', 'UTF8', None, ),  # 2
    (3, TType.LIST, 'column_defs', (TType.STRUCT, [ColumnDef, None], False), [
    ], ),  # 3
    (4, TType.LIST, 'column_fields', (TType.STRUCT, [ColumnField, None], False), [
    ], ),  # 4
)
all_structs.append(SelectRequest)
SelectRequest.thrift_spec = (
    None,  # 0
    (1, TType.I64, 'session_id', None, None, ),  # 1
    (2, TType.STRING, 'db_name', 'UTF8', None, ),  # 2
    (3, TType.STRING, 'table_name', 'UTF8', None, ),  # 3
    (4, TType.LIST, 'select_list', (TType.STRUCT, [ParsedExpr, None], False), [
    ], ),  # 4
    (5, TType.LIST, 'highlight_list', (TType.STRUCT, [ParsedExpr, None], False), [
    ], ),  # 5
    (6, TType.STRUCT, 'search_expr', [SearchExpr, None], None, ),  # 6
    (7, TType.STRUCT, 'where_expr', [ParsedExpr, None], None, ),  # 7
    (8, TType.LIST, 'group_by_list', (TType.STRUCT, [ParsedExpr, None], False), [
    ], ),  # 8
    (9, TType.STRUCT, 'having_expr', [ParsedExpr, None], None, ),  # 9
    (10, TType.STRUCT, 'limit_expr', [ParsedExpr, None], None, ),  # 10
    (11, TType.STRUCT, 'offset_expr', [ParsedExpr, None], None, ),  # 11
    (12, TType.LIST, 'order_by_list', (TType.STRUCT, [OrderByExpr, None], False), [
    ], ),  # 12
    (13, TType.BOOL, 'total_hits_count', None, None, ),  # 13
)
all_structs.append(SelectResponse)
SelectResponse.thrift_spec = (
    None,  # 0
    (1, TType.I64, 'error_code', None, None, ),  # 1
    (2, TType.STRING, 'error_msg', 'UTF8', None, ),  # 2
    (3, TType.LIST, 'column_defs', (TType.STRUCT, [ColumnDef, None], False), [
    ], ),  # 3
    (4, TType.LIST, 'column_fields', (TType.STRUCT, [ColumnField, None], False), [
    ], ),  # 4
    (5, TType.STRING, 'extra_result', 'UTF8', None, ),  # 5
)
all_structs.append(DeleteRequest)
DeleteRequest.thrift_spec = (
    None,  # 0
    (1, TType.STRING, 'db_name', 'UTF8', None, ),  # 1
    (2, TType.STRING, 'table_name', 'UTF8', None, ),  # 2
    (3, TType.STRUCT, 'where_expr', [ParsedExpr, None], None, ),  # 3
    (4, TType.I64, 'session_id', None, None, ),  # 4
)
all_structs.append(DeleteResponse)
DeleteResponse.thrift_spec = (
    None,  # 0
    (1, TType.I64, 'error_code', None, None, ),  # 1
    (2, TType.STRING, 'error_msg', 'UTF8', None, ),  # 2
    (3, TType.I64, 'deleted_rows', None, None, ),  # 3
)
all_structs.append(UpdateRequest)
UpdateRequest.thrift_spec = (
    None,  # 0
    (1, TType.STRING, 'db_name', 'UTF8', None, ),  # 1
    (2, TType.STRING, 'table_name', 'UTF8', None, ),  # 2
    (3, TType.STRUCT, 'where_expr', [ParsedExpr, None], None, ),  # 3
    (4, TType.LIST, 'update_expr_array', (TType.STRUCT, [UpdateExpr, None], False), [
    ], ),  # 4
    (5, TType.I64, 'session_id', None, None, ),  # 5
)
all_structs.append(AddColumnsRequest)
AddColumnsRequest.thrift_spec = (
    None,  # 0
    (1, TType.STRING, 'db_name', 'UTF8', None, ),  # 1
    (2, TType.STRING, 'table_name', 'UTF8', None, ),  # 2
    (3, TType.LIST, 'column_defs', (TType.STRUCT, [ColumnDef, None], False), [
    ], ),  # 3
    (4, TType.I64, 'session_id', None, None, ),  # 4
)
all_structs.append(DropColumnsRequest)
DropColumnsRequest.thrift_spec = (
    None,  # 0
    (1, TType.STRING, 'db_name', 'UTF8', None, ),  # 1
    (2, TType.STRING, 'table_name', 'UTF8', None, ),  # 2
    (3, TType.LIST, 'column_names', (TType.STRING, 'UTF8', False), [
    ], ),  # 3
    (4, TType.I64, 'session_id', None, None, ),  # 4
)
all_structs.append(DumpIndexRequest)
DumpIndexRequest.thrift_spec = (
    None,  # 0
    (1, TType.STRING, 'db_name', 'UTF8', None, ),  # 1
    (2, TType.STRING, 'table_name', 'UTF8', None, ),  # 2
    (3, TType.STRING, 'index_name', 'UTF8', None, ),  # 3
    (4, TType.I64, 'session_id', None, None, ),  # 4
)
all_structs.append(ShowTablesRequest)
ShowTablesRequest.thrift_spec = (
    None,  # 0
    (1, TType.I64, 'session_id', None, None, ),  # 1
    (2, TType.STRING, 'db_name', 'UTF8', None, ),  # 2
)
all_structs.append(ShowSegmentsRequest)
ShowSegmentsRequest.thrift_spec = (
    None,  # 0
    (1, TType.I64, 'session_id', None, None, ),  # 1
    (2, TType.STRING, 'db_name', 'UTF8', None, ),  # 2
    (3, TType.STRING, 'table_name', 'UTF8', None, ),  # 3
)
all_structs.append(ShowSegmentRequest)
ShowSegmentRequest.thrift_spec = (
    None,  # 0
    (1, TType.I64, 'session_id', None, None, ),  # 1
    (2, TType.STRING, 'db_name', 'UTF8', None, ),  # 2
    (3, TType.STRING, 'table_name', 'UTF8', None, ),  # 3
    (4, TType.I64, 'segment_id', None, None, ),  # 4
)
all_structs.append(ShowSegmentResponse)
ShowSegmentResponse.thrift_spec = (
    None,  # 0
    (1, TType.I64, 'error_code', None, None, ),  # 1
    (2, TType.STRING, 'error_msg', 'UTF8', None, ),  # 2
    (3, TType.I64, 'segment_id', None, None, ),  # 3
    (4, TType.STRING, 'status', 'UTF8', None, ),  # 4
    (5, TType.STRING, 'path', 'UTF8', None, ),  # 5
    (6, TType.STRING, 'size', 'UTF8', None, ),  # 6
    (7, TType.I64, 'block_count', None, None, ),  # 7
    (8, TType.I64, 'row_capacity', None, None, ),  # 8
    (9, TType.I64, 'row_count', None, None, ),  # 9
    (10, TType.I64, 'room', None, None, ),  # 10
    (11, TType.I64, 'column_count', None, None, ),  # 11
)
all_structs.append(ShowBlocksRequest)
ShowBlocksRequest.thrift_spec = (
    None,  # 0
    (1, TType.I64, 'session_id', None, None, ),  # 1
    (2, TType.STRING, 'db_name', 'UTF8', None, ),  # 2
    (3, TType.STRING, 'table_name', 'UTF8', None, ),  # 3
    (4, TType.I64, 'segment_id', None, None, ),  # 4
)
all_structs.append(ShowBlockRequest)
ShowBlockRequest.thrift_spec = (
    None,  # 0
    (1, TType.I64, 'session_id', None, None, ),  # 1
    (2, TType.STRING, 'db_name', 'UTF8', None, ),  # 2
    (3, TType.STRING, 'table_name', 'UTF8', None, ),  # 3
    (4, TType.I64, 'segment_id', None, None, ),  # 4
    (5, TType.I64, 'block_id', None, None, ),  # 5
)
all_structs.append(ShowBlockResponse)
ShowBlockResponse.thrift_spec = (
    None,  # 0
    (1, TType.I64, 'error_code', None, None, ),  # 1
    (2, TType.STRING, 'error_msg', 'UTF8', None, ),  # 2
    (3, TType.I64, 'block_id', None, None, ),  # 3
    (4, TType.STRING, 'path', 'UTF8', None, ),  # 4
    (5, TType.STRING, 'size', 'UTF8', None, ),  # 5
    (6, TType.I64, 'row_capacity', None, None, ),  # 6
    (7, TType.I64, 'row_count', None, None, ),  # 7
    (8, TType.I64, 'column_count', None, None, ),  # 8
)
all_structs.append(ShowBlockColumnRequest)
ShowBlockColumnRequest.thrift_spec = (
    None,  # 0
    (1, TType.I64, 'session_id', None, None, ),  # 1
    (2, TType.STRING, 'db_name', 'UTF8', None, ),  # 2
    (3, TType.STRING, 'table_name', 'UTF8', None, ),  # 3
    (4, TType.I64, 'segment_id', None, None, ),  # 4
    (5, TType.I64, 'block_id', None, None, ),  # 5
    (6, TType.I64, 'column_id', None, None, ),  # 6
)
all_structs.append(ShowBlockColumnResponse)
ShowBlockColumnResponse.thrift_spec = (
    None,  # 0
    (1, TType.I64, 'error_code', None, None, ),  # 1
    (2, TType.STRING, 'error_msg', 'UTF8', None, ),  # 2
    (3, TType.STRING, 'column_name', 'UTF8', None, ),  # 3
    (4, TType.I64, 'column_id', None, None, ),  # 4
    (5, TType.STRING, 'data_type', 'UTF8', None, ),  # 5
    (6, TType.STRING, 'path', 'UTF8', None, ),  # 6
    (7, TType.I64, 'extra_file_count', None, None, ),  # 7
    (8, TType.STRING, 'extra_file_names', 'UTF8', None, ),  # 8
)
all_structs.append(ShowCurrentNodeRequest)
ShowCurrentNodeRequest.thrift_spec = (
    None,  # 0
    (1, TType.I64, 'session_id', None, None, ),  # 1
)
all_structs.append(ShowCurrentNodeResponse)
ShowCurrentNodeResponse.thrift_spec = (
    None,  # 0
    (1, TType.I64, 'error_code', None, None, ),  # 1
    (2, TType.STRING, 'error_msg', 'UTF8', None, ),  # 2
    (3, TType.STRING, 'node_role', 'UTF8', None, ),  # 3
    (4, TType.STRING, 'server_status', 'UTF8', None, ),  # 4
)
all_structs.append(CommandRequest)
CommandRequest.thrift_spec = (
    None,  # 0
    (1, TType.I64, 'session_id', None, None, ),  # 1
    (2, TType.STRING, 'command_type', 'UTF8', None, ),  # 2
    (3, TType.STRING, 'test_command_content', 'UTF8', None, ),  # 3
)
all_structs.append(FlushRequest)
FlushRequest.thrift_spec = (
    None,  # 0
    (1, TType.I64, 'session_id', None, None, ),  # 1
    (2, TType.STRING, 'flush_type', 'UTF8', None, ),  # 2
)
all_structs.append(CompactRequest)
CompactRequest.thrift_spec = (
    None,  # 0
    (1, TType.I64, 'session_id', None, None, ),  # 1
    (2, TType.STRING, 'db_name', 'UTF8', None, ),  # 2
    (3, TType.STRING, 'table_name', 'UTF8', None, ),  # 3
)
fix_spec(all_structs)
del all_structs
