[general]
version = "0.6.0"
time_zone = "utc-8"
server_mode = "admin"

[network]
server_address = "********"
postgres_port = 5434
http_port = 23822
client_port = 23819
connection_pool_size = 128
peer_ip = "********"
peer_port = 23852
peer_retry_delay = 0
peer_retry_count = 0
peer_connect_timeout = 2000
peer_recv_timeout = 0
peer_send_timeout = 0

[log]
log_filename = "infinity.log"
log_dir = "/var/infinity/follower/log"
log_to_stdout = false
log_file_max_size = "10GB"
log_file_rotate_count = 10
log_level = "trace"

[storage]
persistence_dir = "/var/infinity/follower/persistence"
data_dir = "/var/infinity/follower/data"
optimize_interval = "10s"
cleanup_interval = "60s"
compact_interval = "120s"
mem_index_capacity = 65536
storage_type = "minio"

[storage.object_storage]
url = "********:9005"
bucket_name = "infinity"
access_key = "minioadmin"
secret_key = "minioadmin"
enable_https = false

[buffer]
buffer_manager_size = "4GB"
lru_num = 7
temp_dir = "/var/infinity/follower/tmp"
memindex_memory_quota = "1GB"

[wal]
wal_dir = "/var/infinity/follower/wal"
checkpoint_interval = "86400s"
wal_compact_threshold = "1GB"
wal_flush = "only_write"

[resource]
resource_dir = "/var/infinity/follower/resource"
